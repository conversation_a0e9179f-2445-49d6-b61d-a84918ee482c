.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.page-heading {
  font-size: 25px;
  font-weight: 500;
  color: #333;
}

.add-button {
  padding: 8px 16px;
  font-size: 16px;
  color: white;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: #0056b3;
}

.card {
  overflow-x: auto;
  background: #ffffff !important;
}

.video-tab-label {
  display: inline-block;
  background-color: #e0f7fa;
  padding: 5px 10px;
  border-radius: 5px;
}
