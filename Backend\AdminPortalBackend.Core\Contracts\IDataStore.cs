﻿namespace AdminPortalBackend.Core.Contracts;

public interface IDataStore
{
    Task<List<Dictionary<string, object>>> RetrieveDataAsync(string tableName);
    Task<List<Dictionary<string, object>>> SaveDataAsync(List<Dictionary<string, object>> data, string tableName, string sourceTableName, string primaryKeys="");
    Task<Dictionary<string, object>> UpdateDataAsync(Dictionary<string, object> data, string tableName, Dictionary<string, object> primaryKeyValues);
    void SetConfig(string key,  object value);
    object GetConfig(string key);
}
