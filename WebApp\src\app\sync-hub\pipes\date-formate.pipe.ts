import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateFormate',
  standalone: true
})
export class DateFormatePipe implements PipeTransform {
  transform(value: string, format: string = 'dd/MM/yyyy'): string | null {
    if (!value) return null;

    let date: any = new Date(value);
    //console.log(date);

    let now: any = new Date();
    let diffInSeconds = Math.floor((now - date) / 1000);

    let days = Math.floor(diffInSeconds / (3600 * 24));
    let hours = Math.floor((diffInSeconds % (3600 * 24)) / 3600);
    let minutes = Math.floor((diffInSeconds % 3600) / 60);
    let seconds = diffInSeconds % 60;
    const remainingSeconds = seconds % 60;

    if (days > 0) {
      return `${days}d ago`;
    }

    if (hours > 0) {
      return `${hours}h ago`;
    }

    if (minutes > 0) {
      return `${minutes}m ago`;
    }

    return `${remainingSeconds}s ago`;
  }


}
