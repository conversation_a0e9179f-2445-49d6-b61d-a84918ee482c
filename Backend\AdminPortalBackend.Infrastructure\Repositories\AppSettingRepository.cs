﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Repositiories;
using Dapper;
using System.Data;

namespace AdminPortalBackend.Infrastructure.Repositories
{

    public class AppSettingRepository(IDbConnection _dbConnection): IAppSettingRepository
    {
        public async Task<ResponseMessage> CreateOrUpdateWebhookNotificationUrl(string notificationUrl)
        {
            // Define the key and description
            string key = "WebhookNotificationUrl";
            string description = "webhook";
            DateTime currentDateTime = DateTime.UtcNow;

            try
            {
                // First, check if the notification URL already exists
                var existingNotification = await _dbConnection.QueryFirstOrDefaultAsync(
                    "SELECT Id, [Key], Value, Description, CreatedAt, UpdatedAt FROM AppSettings WHERE [Key] = @Key",
                    new { Key = key });


                if (existingNotification != null)
                {
                    // If exists, update the record
                    string updateQuery = @"
                UPDATE AppSettings
                SET Value = @NotificationUrl,
                    Description = @Description,
                    UpdatedAt = @UpdatedAt
                WHERE Id = @Id";

                    await _dbConnection.ExecuteAsync(updateQuery, new
                    {
                        NotificationUrl = notificationUrl,
                        Description = description,
                        UpdatedAt = currentDateTime,
                        Id = existingNotification.Id
                    });

                    return new ResponseMessage { IsError = false, Message = "Webhook notification updated successfully." };
                }
                else
                {
                    // If doesn't exist, insert a new record
                    string insertQuery = @"
                INSERT INTO AppSettings ([Key], Value, Description, CreatedAt, UpdatedAt)
                VALUES (@Key, @NotificationUrl, @Description, @CreatedAt, @UpdatedAt)";

                    await _dbConnection.ExecuteAsync(insertQuery, new
                    {
                        Key = key,
                        NotificationUrl = notificationUrl,
                        Description = description,
                        CreatedAt = currentDateTime,
                        UpdatedAt = currentDateTime
                    });

                    return new ResponseMessage { IsError = false, Message = "Webhook notification added successfully." };
                }
            }
            catch (Exception ex)
            {
                // Log or handle the exception
                return new ResponseMessage { IsError = true, Message = "An error occurred while processing the request: " + ex.Message };
            }
        }

        public async Task<ResponseMessage> GetWebhookNotificationUrl()
        {
            string key = "WebhookNotificationUrl";

            try
            {
                // Query to retrieve the webhook URL from the AppSettings table
                var notification = await _dbConnection.QueryFirstOrDefaultAsync<string>(
                    "SELECT Value FROM AppSettings WHERE [Key] = @Key",
                    new { Key = key });

                if (!string.IsNullOrEmpty(notification))
                {
                    return new ResponseMessage { IsError = false, Message = notification };
                }
                else
                {
                    return new ResponseMessage { IsError = true, Message = "Notification not found!" };
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }
}
