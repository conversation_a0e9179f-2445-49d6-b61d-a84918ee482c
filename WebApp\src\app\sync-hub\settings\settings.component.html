
<div class="page-header d-flex ">
  <h2>Settings</h2>
</div>
<div class="d-flex gap-5 my-2 align-items-center">
  <h3 class="mb-0">Webhook Url</h3>

  <button *ngIf="!url" (click)="addWebHook()" style="border: none; color: white; background: #085e81;padding: 0;" class="btn p-2 py-1 ">
    Add Webhook Url
  </button>
</div>
<div *ngIf="url || isInputShown" class="d-flex">
  <input type="text" [(ngModel)]="url" pInputText style="border-radius: unset; width: 550px;" />
  <p-button icon="pi pi-check"
    [style]="{border: 'none', color: 'white', background: '#085e81', margin: '1px'}"
    (onClick)="updateUrl($event)"
    />
</div>
<p-toast position="bottom-right" key="br" />

