import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ServiceProxyModule } from '../../shared/service-proxies/service-proxy.module';
import { VideoComponent } from './video.component';
import { VideoPageVideoListComponent } from './video-page-video-list/video-page-video-list.component';
import { VideoGalleryComponent } from './video-gallery/video-gallery.component';
import { SearchingComponent } from '../searching/searching.component';

@NgModule({
  declarations: [],
  imports: [
    RouterModule.forChild([
      {
        path: "",
        children: [
          {
            path: '', component: VideoComponent,
            children: [
              { path: "", redirectTo: "video", pathMatch: "full" },
              { path: 'video', component: SearchingComponent, },
              { path: 'video-gallery', component: VideoGalleryComponent },
              { path: 'video-list', component: VideoPageVideoListComponent },
              { path: '', redirectTo: 'video-gallery', pathMatch: 'full' }
            ],
          },
        ]
      },
    ]),
    CommonModule, ServiceProxyModule
  ]
})
export class VideoModule { }
