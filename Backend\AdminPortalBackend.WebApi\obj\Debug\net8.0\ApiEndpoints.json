[{"ContainingType": "AdminPortalBackend.WebApi.Controllers.AppSettingController", "Method": "AddOrEditWebHookNotification", "RelativePath": "api/AppSetting/AddOrEditWebHookNotification", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.AppSettingController", "Method": "GetWebhookNotificationUrl", "RelativePath": "api/AppSetting/GetWebhookNotificationUrl", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ATestController", "Method": "BCToSqlTest", "RelativePath": "api/ATest/bctosql", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ATestController", "Method": "TransferCompanyData", "RelativePath": "api/ATest/companies/{connectionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ATestController", "Method": "TransferEntities", "RelativePath": "api/ATest/entities/{connectionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ATestController", "Method": "TransferMetadata", "RelativePath": "api/ATest/metadata/{connectionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ATestController", "Method": "SqlToBCTest", "RelativePath": "api/ATest/sqltobc", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ATestController", "Method": "TestBCUrl", "RelativePath": "api/ATest/test-bc-url/{token}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "CheckIfViewExists", "RelativePath": "api/ConnectionIntegration/CheckIfViewExists", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPortalBackend.Core.Features.IsViewDto", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "CheckTableExist", "RelativePath": "api/ConnectionIntegration/CheckTableExist", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "table", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}, {"Name": "destinationGuid", "Type": "System.Guid", "IsRequired": false}, {"Name": "destinationDatabase", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "Create", "RelativePath": "api/ConnectionIntegration/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "integration", "Type": "AdminPortalBackend.Core.Features.CreateConnectionIntegrationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "CreateTableForODataWebService", "RelativePath": "api/ConnectionIntegration/CreateTableForODataWebService", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "integration", "Type": "AdminPortalBackend.Core.Features.CreateConnectionIntegrationDto", "IsRequired": true}, {"Name": "isExecute", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/ConnectionIntegration/CreateTrigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "integrationGuid", "Type": "System.Guid", "IsRequired": false}, {"Name": "destinationGuid", "Type": "System.Guid", "IsRequired": false}, {"Name": "databaseName", "Type": "System.String", "IsRequired": false}, {"Name": "tableName", "Type": "System.String", "IsRequired": false}, {"Name": "sourceGuid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/ConnectionIntegration/CreateView", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPortalBackend.Core.Features.CreateViewDto", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "Delete", "RelativePath": "api/ConnectionIntegration/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Entities.ConnectionIntegration", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "DeleteJob", "RelativePath": "api/ConnectionIntegration/DeleteJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "DeleteTrigger", "RelativePath": "api/ConnectionIntegration/DeleteTrigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "destinationGuid", "Type": "System.Guid", "IsRequired": false}, {"Name": "databaseName", "Type": "System.String", "IsRequired": false}, {"Name": "tableName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/ConnectionIntegration/DisableJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "Edit", "RelativePath": "api/ConnectionIntegration/Edit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "integration", "Type": "AdminPortalBackend.Core.Entities.ConnectionIntegration", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Entities.ConnectionIntegration", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "EditTableForODataWebService", "RelativePath": "api/ConnectionIntegration/EditTableForODataWebService", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "integration", "Type": "AdminPortalBackend.Core.Entities.ConnectionIntegration", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "ExecuteJob", "RelativePath": "api/ConnectionIntegration/ExecuteJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "integrationId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetBcSqlAllAsync", "RelativePath": "api/ConnectionIntegration/GetBcSqlAllAsync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[AdminPortalBackend.Core.Entities.ConnectionIntegration, AdminPortalBackend.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetColumns", "RelativePath": "api/ConnectionIntegration/GetColumn/{guid}/{databaseName}/{tableName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": true}, {"Name": "databaseName", "Type": "System.String", "IsRequired": true}, {"Name": "tableName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetCompanyId", "RelativePath": "api/ConnectionIntegration/GetCompanyId", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionGuid", "Type": "System.Guid", "IsRequired": false}, {"Name": "database", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetCompanyNames", "RelativePath": "api/ConnectionIntegration/GetCompanyNames/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetDatabases", "RelativePath": "api/ConnectionIntegration/GetDatabase/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetDestinationPrimaryKey", "RelativePath": "api/ConnectionIntegration/GetDestinationPrimaryKey/{destinationTable}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "destinationTable", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetEntityNames", "RelativePath": "api/ConnectionIntegration/GetEntityNames/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetFieldFromService", "RelativePath": "api/ConnectionIntegration/GetFieldFromService", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "serviceName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Features.ServiceField", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetFieldNames", "RelativePath": "api/ConnectionIntegration/GetFieldNames", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetPropertiesForEntity", "RelativePath": "api/ConnectionIntegration/GetPropertiesForEntity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityName", "Type": "System.String", "IsRequired": false}, {"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetById", "RelativePath": "api/ConnectionIntegration/GetSingle", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Entities.ConnectionIntegration", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetSqlSqlAllAsync", "RelativePath": "api/ConnectionIntegration/GetSqlSqlAllAsync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[AdminPortalBackend.Core.Entities.ConnectionIntegration, AdminPortalBackend.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetTableNames", "RelativePath": "api/ConnectionIntegration/GetTableNames", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "GetTables", "RelativePath": "api/ConnectionIntegration/GetTables/{guid}/{databaseName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": true}, {"Name": "databaseName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessageList", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "IsSystemRowVersionAvailable", "RelativePath": "api/ConnectionIntegration/IsSystemRowVersionAvailable", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityName", "Type": "System.String", "IsRequired": false}, {"Name": "connectionGuid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "ProcessSqlDelete", "RelativePath": "api/ConnectionIntegration/ProcessSqlDelete", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ConnectionIntegrationController", "Method": "<PERSON>Job", "RelativePath": "api/ConnectionIntegration/ScheduleJob", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "jobRequest", "Type": "AdminPortalBackend.Core.Features.JobRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.DbConnectionController", "Method": "CreateOrEdit", "RelativePath": "api/DbConnection/CreateOrEdit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPortalBackend.Core.Features.DbConnectionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Features.DbConnectionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.DbConnectionController", "Method": "Delete", "RelativePath": "api/DbConnection/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.DbConnectionController", "Method": "GetAll", "RelativePath": "api/DbConnection/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[AdminPortalBackend.Core.Features.DbConnectionDto, AdminPortalBackend.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.DbConnectionController", "Method": "GetSingle", "RelativePath": "api/DbConnection/GetSingle", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Features.DbConnectionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.Controllers.DbConnectionController", "Method": "GetTop10JobLogs", "RelativePath": "api/DbConnection/GetTop10JobLogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "integrationId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[AdminPortalBackend.Core.Features.JobLogEntry, AdminPortalBackend.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.ExternalController", "Method": "HandleRequest", "RelativePath": "api/External/request", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "payload", "Type": "AdminPortalBackend.WebApi.Controllers.RequestPayload", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "DeleteFile", "RelativePath": "api/File/DeleteFile", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "GetFile", "RelativePath": "api/File/Getfile/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.FileController", "Method": "UploadFiles", "RelativePath": "api/File/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.GenAIController", "Method": "AddAIMemory", "RelativePath": "api/GenAI/AddAIMemory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.GenAIController", "Method": "GetAIResponse", "RelativePath": "api/GenAI/GetAIResponse/{question}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "question", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "AdminPortalBackend.Infrastructure.AIResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.GenAIController", "Method": "MapColumns", "RelativePath": "api/GenAI/MapColumns", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "mapColumns", "Type": "AdminPortalBackend.Core.Features.MapColumns", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[AdminPortalBackend.Core.Features.MappedColumn, AdminPortalBackend.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.TestConnectionController", "Method": "CheckBcConnection", "RelativePath": "api/TestConnection/CheckBcConnection", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientSecret", "Type": "System.String", "IsRequired": false}, {"Name": "clientId", "Type": "System.String", "IsRequired": false}, {"Name": "tokenEndpoint", "Type": "System.String", "IsRequired": false}, {"Name": "scope", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.TestConnectionController", "Method": "CheckSqlConnection", "RelativePath": "api/TestConnection/CheckSqlConnection", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "password", "Type": "System.String", "IsRequired": false}, {"Name": "serverName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.TestConnectionController", "Method": "TestRealtime", "RelativePath": "api/TestConnection/realtime", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.WebhookController", "Method": "ReceiveWebhook", "RelativePath": "api/webhooks/bc-notifications", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "validationToken", "Type": "System.String", "IsRequired": false}, {"Name": "request", "Type": "WebhookRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.WebhookController", "Method": "DeleteSubscription", "RelativePath": "api/webhooks/delete-subscription/{connectionId}/{integrationId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionId", "Type": "System.String", "IsRequired": true}, {"Name": "integrationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.WebhookController", "Method": "IsSubscription", "RelativePath": "api/webhooks/IsSubscription", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionId", "Type": "System.String", "IsRequired": false}, {"Name": "integrationId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AdminPortalBackend.Core.Contracts.Features.ResponseMessage", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.WebhookController", "Method": "RegisterWebhook", "RelativePath": "api/webhooks/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPortalBackend.Core.Models.SubscriptionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPortalBackend.WebApi.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AdminPortalBackend.WebApi.WeatherForecast, AdminPortalBackend.WebApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]