<div class="container">
  <h2>Distribution Group Self Service</h2>
  <div class="d-flex align-items-center  gap-5">

    <p>
      Selecting a group below enables you to receive the email and newsletter communications sent to that group.
      Please select all groups you would like to join.
      You are automatically assigned to any groups that are disabled. You cannot change these selections.</p>

    <button pButton type="button" label="Save" class="save-button" (click)="save()"></button>
  </div>

  <div *ngFor="let group of distributionGroups" class="heading-section">
    <table class="custom-table">
      <thead>
        <tr>
          <th colspan="2">{{ group.heading }}</th>
        </tr>
      </thead>
      <tbody>
        <!-- Loop through items in the heading -->
        <tr *ngFor="let item of group.items">
          <td>
            <p-checkbox name="pizza" [value]="true" [binary]="true" [(ngModel)]="item.isChecked"></p-checkbox>
            <label for="ny" style="margin-left: 10px;">{{ item.label }}</label>
          </td>
          <td>{{ item.description }}</td>
        </tr>
      </tbody>
    </table>
  </div>

</div>
