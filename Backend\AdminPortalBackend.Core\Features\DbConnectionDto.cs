﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Features
{
    using System.Text.Json;
    using System.Text.Json.Serialization;

    public class ODataCredDto
    {
        [JsonPropertyName("endpointUrl")]
        public string EndpointUrl { get; set; }
       
        [JsonPropertyName("tokenEndpoint")]
        public string TokenEndpoint { get; set; }

        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        [JsonPropertyName("clientId")]
        public string ClientId { get; set; }

        [JsonPropertyName("clientSecret")]
        public string ClientSecret { get; set; }

        [JsonPropertyName("baseUrl")]
        public string BaseUrl { get; set; }

    }

    public class DbConnectionDto
    {
        public Guid Guid { get; set; }
        public string ConnectionName { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string ConnectionCredJson { get; set; }
    }

    public class ODataConnectionDetailDto
    {
        public Guid Guid { get; set; }
        public string ConnectionName { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string ConnectionCredJson { get; set; }
        public string AccessToken { get; set; }
        public DateTimeOffset? ExpiresOn { get; set; }
        public ODataCredDto ConnectionCreds => JsonSerializer.Deserialize<ODataCredDto>(ConnectionCredJson);
    }

    public class MssqlServerCredDto
    {
        public string ServerName { get; set; }
        public string UserId { get; set; }
        public string Password { get; set; }
        public bool TrustedCertificate { get; set; }
    }
    public class ApiCredDto
    {
        public string ApiUrl { get; set; }
        public Dictionary<string, string> Headers { get; set; }
    }

}
