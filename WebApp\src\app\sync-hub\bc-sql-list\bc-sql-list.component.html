<div class="page-header d-flex">
  <div class="d-flex gap-4 align-items-center">
    <h2>BC SQL List</h2>
    <div class="d-flex gap-2 align-items-center justify-content-center">
      <p-dropdown class="dropdown" [options]="connectionType" [(ngModel)]="selectedConnectionType"
        placeholder="Select type" (onChange)="onConnTypeChange()"></p-dropdown>
      <p-dropdown class="dropdown" [options]="companyType" [(ngModel)]="selectedCompanyType"
        placeholder="Select company type" (onChange)="onCompanyTypeChange()"></p-dropdown>
      <input class="form-control mr-sm-2" [(ngModel)]="searchByEntityKey" (input)="updateSearchEntityKey($event)"
        type="text" placeholder="Search Entities" aria-label="Search by destination.." style="padding: 10px .75rem;" />

      <button *ngIf="isFilterAdded"
        style="display: flex; align-items: center; justify-content: center; border: none; outline: none;"
        (click)="clearAllFilter()">
        <i class="pi pi-times"></i>
      </button>
    </div>
  </div>
  <button class="Btn" (click)="navigateToAddIntegration()">
    <i class="fa-solid fa-plus" style="margin-right: 5px;"></i>Add Integration
  </button>
</div>
<table class="card compact-table ">
  @if (isDataLoaded) {

  @if (groupedSqlListData.length) {
  <p-table [value]="groupedSqlListData" showGridlines
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries">
    <ng-template pTemplate="header">
      <tr>
        <th>Last Run</th>
        <th style="min-width: 13rem;">Source</th>
        <th>Source Entity</th>
        <th>Destination DB</th>
        <th>Destination Table</th>
        <th>Action</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-group>
      <tr>
        <td colspan="7" class="font-weight-bold py-0">
          <h3 class="mb-0 d-flex gap-3 align-items-center" (click)="toggleGroupExpansion(group)"
            style="cursor: pointer; padding: 8px 0; font-weight: 500;">
            <i class="fas"
              [ngClass]="{ 'fa-chevron-down': !group.isExpanded, 'fa-chevron-right': group.isExpanded }"></i>
            <span>Company: {{ group.sourceDatabase }}</span>
          </h3>
        </td>
      </tr>

      <ng-container *ngIf="!group.isExpanded">
        <ng-container *ngFor="let item of group.items">
          <tr [ngClass]="{'highlighted-row': item.guid === highlightedGuid, 'executing-row': executingJobs.has(item.guid)}">
            <td>{{item.lastExecutionDate}}</td>

            <td>
              <div class="d-flex align-items-center gap-1">
                <span [pTooltip]="item.guid" tooltipPosition="top" tooltipStyleClass="tooltip">
                  <img [src]="'../../assets/img/' + logo(item.sourceConnectionGuid)" style="width: 20px;" />
                  {{ item.sourceConnectionName }}
                </span>
              </div>
            </td>
            <td>{{ item.sourceTable }}</td>
            <td>{{ item.destinationDatabase }}</td>
            <td>{{ item.destinationTable }}</td>
            <td>
              <div class="d-flex justify-content-center">
                <div class="d-flex align-items-center justify-content-center gap-1 dataflow " style="cursor: pointer;"
                  (click)="showJobLogsDialog(item)">
                  <span><img [src]="'../../assets/img/' + logo(item.sourceConnectionGuid)"
                      style="width: 20px;" /></span>
                  <i [class]="'pi ' + getSettings(item.settings)"></i>
                  <span><img src="../../assets/img/sql-logo.png" style="width: 20px;" /></span>
                </div>
                <button class="action-button edit-button btn" style="color: var(--bg-color);"
                  (click)="updateBcSql(item.guid)" title="Edit">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="action-button delete-button btn" title="Delete" style="color: red; font-size: 0.9rem;"
                  (click)="deleteBCIntegration(item.guid)">
                  <i class="fas fa-trash-alt"></i>
                </button>
                <button class="action-button btn" [title]="executingJobs.has(item.guid) ? 'Stop' : 'Execute'"
                  [style.color]="executingJobs.has(item.guid) ? 'red' : 'var(--bg-color)'"
                  (click)="executingJobs.has(item.guid) ? stopJob(item.guid) : executeJob(item.guid)">
                  <i class="fas" [class.fa-play]="!executingJobs.has(item.guid)"
                    [class.fa-stop]="executingJobs.has(item.guid)"></i>
                </button>
              </div>
            </td>

          </tr>
        </ng-container>
      </ng-container>
    </ng-template>
  </p-table>
  }@else {
  <div class="d-flex align-items-center justify-content-center " style="height: 70vh;">

    <span style="font-size: 1.5rem;font-weight: 500;">No relevant search results found...</span>
  </div>
  }
  }@else {
  <div style="    height: 45vh;
  display: flex
;
  align-items: center;
  justify-content: center;">

    <span class="loader"><i class="pi pi-spin pi-spinner" style="font-size: 5rem"></i></span>
  </div>
  }
</table>
