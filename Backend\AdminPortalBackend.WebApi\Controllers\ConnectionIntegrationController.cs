﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Repositiories;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConnectionIntegrationController(IConnectionIntegrationRepository _connectionInterationRepo) : ControllerBase
    {
        [HttpGet("GetDatabase/{guid}")]
        public async Task<ActionResult<ResponseMessageList>> GetDatabases(Guid guid)
        {
            var databases = await _connectionInterationRepo.GetDatabase(guid);
            return Ok(databases);
        }

        [HttpGet("GetTables/{guid}/{databaseName}")]
        public async Task<ActionResult<ResponseMessageList>> GetTables(Guid guid, string databaseName)
        {
            var tables = await _connectionInterationRepo.GetTables(guid, databaseName);
            return Ok(tables);
        }

        [HttpGet("GetColumn/{guid}/{databaseName}/{tableName}")]
        public async Task<ActionResult<ResponseMessageList>> GetColumns(Guid guid, string databaseName, string tableName)
        {
            var columns = await _connectionInterationRepo.GetColumns(guid, databaseName, tableName);
            return Ok(columns);
        }

        [HttpPost("Edit")]
        public async Task<ActionResult<ConnectionIntegration>> Edit(ConnectionIntegration integration)
        {
            var createdIntegration = await _connectionInterationRepo.Edit(integration);
            return Ok(createdIntegration);
        }

        [HttpPost("Create")]
        public async Task<ActionResult<ResponseMessage>> Create(CreateConnectionIntegrationDto integration)
        {
            var res = await _connectionInterationRepo.Create(integration);
            return Ok(res);
        }


        [HttpPost("GetCompanyId")]
        public async Task<ActionResult<ResponseMessage>> GetCompanyId(Guid connectionGuid, string database)
        {
            var res = await _connectionInterationRepo.GetCompanyId(connectionGuid, database);
            return Ok(res);
        }

        [HttpPost("CreateTableForODataWebService")]
        public async Task<ActionResult<ResponseMessage>> CreateTableForODataWebService(CreateConnectionIntegrationDto integration, bool isExecute)
        {
            var res = await _connectionInterationRepo.CreateTableForODataWebService(integration, isExecute);
            return Ok(res);
        }

        [HttpGet("GetSqlSqlAllAsync")]
        public async Task<ActionResult<List<ConnectionIntegration>>> GetSqlSqlAllAsync()
        {
            var integrations = await _connectionInterationRepo.GetSqlSqlAllAsync();
            return Ok(integrations);
        }

        [HttpGet("GetBcSqlAllAsync")]
        public async Task<ActionResult<List<ConnectionIntegration>>> GetBcSqlAllAsync()
        {
            var integrations = await _connectionInterationRepo.GetBcSqlAllAsync();
            return Ok(integrations);
        }

        [HttpGet("GetSingle")]
        public async Task<ActionResult<ConnectionIntegration>> GetById(Guid guid)
        {
            var integration = await _connectionInterationRepo.GetByIdAsync(guid);
            return Ok(integration);
        }

        [HttpDelete("Delete")]
        public async Task<ActionResult<ConnectionIntegration>> Delete(Guid guid)
        {
            var deletedIntegration = await _connectionInterationRepo.DeleteAsync(guid);
            return Ok(deletedIntegration);
        }

        [HttpPost("ScheduleJob")]
        public async Task<ActionResult<ResponseMessage>> ScheduleJob(JobRequest jobRequest)
        {
            var res = await _connectionInterationRepo.ScheduleJob(jobRequest);
            return Ok(res);
        }

        [HttpPost("DisableJob")]
        public async Task<ActionResult<ResponseMessage>> DisableJob(Guid guid)
        {
            var res = await _connectionInterationRepo.DisableJob(guid);
            return Ok(res);
        }

        [HttpPost("DeleteJob")]
        public async Task<ActionResult<ResponseMessage>> DeleteJob(Guid guid)
        {
            var res = await _connectionInterationRepo.DeleteJob(guid);
            return Ok(res);
        }

        [HttpPost("ExecuteJob")]
        public async Task<ActionResult<ResponseMessage>> ExecuteJob(Guid integrationId)
        {
            var res = await _connectionInterationRepo.ExecuteJob(integrationId);
            return Ok(res);
        }

        [HttpGet("GetEntityNames/{guid}")]
        public async Task<ActionResult<ResponseMessageList>> GetEntityNames(Guid guid)
        {
            var res = await _connectionInterationRepo.GetEntityNames(guid);
            return Ok(res);
        }

        [HttpGet("GetPropertiesForEntity")]
        public async Task<ActionResult<ResponseMessageList>> GetPropertiesForEntity(string entityName, Guid guid)
        {
            var res = await _connectionInterationRepo.GetPropertiesForEntity(entityName, guid);
            return Ok(res);
        }

        [HttpGet("GetCompanyNames/{guid}")]
        public async Task<ActionResult<ResponseMessageList>> GetCompanyNames(Guid guid)
        {
            var res = await _connectionInterationRepo.GetCompanyNames(guid);
            return Ok(res);
        }

        [HttpPost("CreateTrigger")]
        public async Task<ActionResult<ResponseMessage>> CreateTrigger(Guid integrationGuid, Guid destinationGuid, string databaseName, string tableName, Guid sourceGuid)
        {
            var res = await _connectionInterationRepo.CreateTrigger(integrationGuid, destinationGuid, databaseName, tableName, sourceGuid);
            return Ok(res);
        }

        [HttpPost("DeleteTrigger")]
        public async Task<ActionResult<ResponseMessage>> DeleteTrigger(Guid destinationGuid, string databaseName, string tableName)
        {
            var res = await _connectionInterationRepo.DeleteTrigger(destinationGuid, databaseName, tableName);
            return Ok(res);
        }

        [HttpPost("EditTableForODataWebService")]
        public async Task<ActionResult<ResponseMessage>> EditTableForODataWebService(ConnectionIntegration integration)
        {
            var res = await _connectionInterationRepo.EditTableForODataWebService(integration);
            return Ok(res);
        }

        [HttpPost("CheckTableExist")]
        public async Task<ActionResult<ResponseMessageList>> CheckTableExist(List<string> table, Guid destinationGuid, string destinationDatabase)
        {
            var res = await _connectionInterationRepo.CheckTableExist(table, destinationGuid, destinationDatabase);
            return Ok(res);
        }

        [HttpGet("IsSystemRowVersionAvailable")]
        public async Task<ActionResult<ResponseMessage>> IsSystemRowVersionAvailable(string entityName, Guid connectionGuid){
            var res = await _connectionInterationRepo.IsSystemRowVersionAvailable(entityName, connectionGuid);
            return Ok(res);
        }

        [HttpPost("CreateView")]
        public async Task<ActionResult<ResponseMessage>> CreateView(CreateViewDto request)
        {
            var res = await _connectionInterationRepo.CreateView(request);
            return Ok(res);
        }
        
        [HttpPost("CheckIfViewExists")]
        public async Task<ActionResult<ResponseMessage>> CheckIfViewExists(IsViewDto request)
        {
            var res = await _connectionInterationRepo.CheckIfViewExists(request);
            return Ok(res);
        }

        [HttpGet("GetFieldNames")]
        public async Task<ActionResult<ResponseMessageList>> GetFieldNames(string tableName)
        {
            var res = await _connectionInterationRepo.GetFieldNames(tableName);
            return Ok(res);
        }

        [HttpGet("GetTableNames")]
        public async Task<ActionResult<ResponseMessageList>> GetTableNames()
        {
            var res = await _connectionInterationRepo.GetTableNames();
            return Ok(res);
        }

        [HttpGet("GetFieldFromService")]
        public async Task<ActionResult<ServiceField>> GetFieldFromService(string serviceName)
        {
            var res = await _connectionInterationRepo.GetFieldFromService(serviceName);
            return Ok(res);
        }
        
        [HttpPut("ProcessSqlDelete")]
        public async Task<ActionResult<ResponseMessage>> ProcessSqlDelete()
        {
            var res = await _connectionInterationRepo.ProcessSqlDelete();
            return Ok(res);
        }

        [HttpGet("GetDestinationPrimaryKey/{destinationTable}")]
        public async Task<ActionResult<ResponseMessage>> GetDestinationPrimaryKey(string destinationTable)
        {
            var primaryKey = await _connectionInterationRepo.GetDestinationPrimaryKey(destinationTable);
            return Ok(primaryKey);
        }
    }
}
