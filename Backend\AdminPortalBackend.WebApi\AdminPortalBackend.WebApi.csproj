﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.5.0" />
    <PackageReference Include="Stripe.net" Version="45.14.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AdminPortalBackend.Core\AdminPortalBackend.Core.csproj" />
    <ProjectReference Include="..\AdminPortalBackend.Infrastructure\AdminPortalBackend.Infrastructure.csproj" />
  </ItemGroup>

</Project>
