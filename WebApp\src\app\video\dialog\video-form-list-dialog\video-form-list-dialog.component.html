<h2 mat-dialog-title class="dialog-title">Create Video</h2>
<form #videoForm="ngForm" (ngSubmit)="onSubmit(videoForm)" class="video-form">
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Video Link</mat-label>
    <input matInput placeholder="Enter Video Link" name="videoLink" [(ngModel)]="video.videoLink" required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Description</mat-label>
    <input matInput placeholder="Enter Description" name="description" [(ngModel)]="video.description" required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Category</mat-label>
    <input matInput placeholder="Enter Category" name="category" [(ngModel)]="video.category" required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Sub-Category</mat-label>
    <input matInput placeholder="Enter Sub-Category" name="subCategory" [(ngModel)]="video.subCategory" required />
  </mat-form-field>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-flat-button color="primary" type="submit" [disabled]="!videoForm.valid">Save</button>
    <button mat-button type="button" (click)="onClose()">Cancel</button>
  </div>
</form>
