import { CommonModule } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown'; // Import PrimeNG Dropdown
import { ConnectionIntegrationServiceProxy, GenAIServiceProxy, MapColumns } from '../../../../shared/service-proxies/service-proxies';
import { MatChipsModule } from '@angular/material/chips';


@Component({
  selector: 'app-table-column-mapping-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    ServiceProxyModule,
    MultiSelectModule,
    DropdownModule,
    MatChipsModule,
  ],
  templateUrl: './table-column-mapping-dialog.component.html',
  styleUrls: ['./table-column-mapping-dialog.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class TableColumnMappingDialogComponent {
  sourceTable: string = '';
  destinationTable: string = '';
  sourceColumns: string[] = [];
  destinationColumns: string[] = [];
  columnMappings: Array<{ source: string, destination: string }> = [];
  sourcePrimaryKey: string[] = [];
  destinationPrimaryKey: string[] = [];
  selectedSourcePrimaryKey: string[] = [];
  previousSourcePrimarySelection: string[] = [];
  selectedDestinationPrimaryKey: string[] = [];
  previousDestPrimarySelection: string[] = [];
  isMappingWithAi = false
  isDestinationKeyEmpty = false
  isSourceKeyEmpty = false
  constructor(
    public dialogRef: MatDialogRef<TableColumnMappingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _connectionIntegrationService: ConnectionIntegrationServiceProxy,
    private _genaiService: GenAIServiceProxy
  ) {
    //console.log(data);
  }

  async ngOnInit() {
    await this.loadColumns();

    if (this.data.mappingData) {
      let sourcePrimaryKey = this.data.mappingData.sourcePrimaryKey.split('@#')
      this.selectedSourcePrimaryKey = [...sourcePrimaryKey];
      this.sourcePrimaryKey = [...sourcePrimaryKey];

      let destinationPrimaryKey = this.data.mappingData.destinationPrimaryKey.split('@#')
      this.selectedDestinationPrimaryKey = [...destinationPrimaryKey];
      this.destinationPrimaryKey = [...destinationPrimaryKey];

      var columnmapping = JSON.parse(this.data.mappingData.mappedColumns);
      this.columnMappings = [];
      for (const key in columnmapping) {
        if (columnmapping.hasOwnProperty(key)) {
          this.columnMappings.push({ source: key, destination: columnmapping[key] });
        }
      }
    } else {
      // For new mappings, fetch destination primary key from existing integration
      try {
        const response = await this._connectionIntegrationService
          .getDestinationPrimaryKey(this.data.sourceTable)
          .toPromise();

        if (!response.isError && response.message) {
          const destKeys = response.message.split('@#');
          this.selectedDestinationPrimaryKey = [...destKeys];
          this.destinationPrimaryKey = [...destKeys];

          this.selectedSourcePrimaryKey = [...destKeys];
          this.sourcePrimaryKey = [...destKeys];
        }
      } catch (error) {
        //console.error('Error fetching destination primary key:', error);
      }
    }
  }
  addMapping() {
    this.columnMappings.push({ source: "", destination: "" })
  }

  addDestinationPrimaryKey(selectedTables: string[]) {
    this.isDestinationKeyEmpty = false

    // Replace the entire primary key selection instead of appending
    this.selectedDestinationPrimaryKey = [...selectedTables];
    this.previousDestPrimarySelection = [...selectedTables];

    //console.log(this.selectedDestinationPrimaryKey);

    if (!this.selectedDestinationPrimaryKey.length) {
      this.isDestinationKeyEmpty = true
    }
  }


  removeDestinationPrimaryKey(table: any) {
    this.isDestinationKeyEmpty = true
    //console.log('aksdf');

    const index = this.selectedDestinationPrimaryKey.indexOf(table);
    if (index >= 0) {
      this.selectedDestinationPrimaryKey.splice(index, 1);
      //console.log(`Table "${table}" removed successfully.`);
    }
  }
  addSourcePrimaryKey(selectedTables: string[]) {
    this.isSourceKeyEmpty = false

    // Replace the entire primary key selection instead of appending
    this.selectedSourcePrimaryKey = [...selectedTables];
    this.previousSourcePrimarySelection = [...selectedTables];

    if (!this.selectedSourcePrimaryKey.length) {
      this.isSourceKeyEmpty = true
    }
    //console.log(this.selectedSourcePrimaryKey);
    //console.log(this.previousDestPrimarySelection);
    //console.log(this.sourcePrimaryKey);
  }


  removeSourcePrimaryKey(table: any) {
    this.isSourceKeyEmpty = true
    const index = this.selectedSourcePrimaryKey.indexOf(table);
    if (index >= 0) {
      this.selectedSourcePrimaryKey.splice(index, 1);
      //console.log(`Table "${table}" removed successfully.`);
    }
  }

  mappedColumns = new MapColumns();
  async loadColumns() {
    // Fetch source columns
    const sourceColumns = await this._connectionIntegrationService
      .getColumn(this.data.sourceGuid, this.data.sourceDataBase, this.data.sourceTable)
      .toPromise();

    if (!sourceColumns.isError) {
      this.sourceColumns = sourceColumns.message;

      // Initialize the columnMappings array with source columns and empty destination
      let count = 0;
      this.sourceColumns.forEach(column => {
        this.columnMappings.push({ source: column, destination: "" });
        this.onSourceChange(column, count)
        count++;
      });
    } else {
      //console.error('Error fetching source columns');
      return;
    }

    // Check if destination table is provided
    if (this.data.destinationTable !== 'Create New') {
      // Fetch destination columns
      const destinationColumn = await this._connectionIntegrationService
        .getColumn(this.data.destinationGuid, this.data.destinationDataBase, this.data.destinationTable)
        .toPromise();

      if (!destinationColumn.isError) {
        this.destinationColumns = destinationColumn.message;

        // // Find common columns between source and destination
        const commonColumns = this.sourceColumns.filter(column =>
          this.destinationColumns.includes(column)
        );

        //console.log('Common Columns:', commonColumns);

        // Map the common columns with destination and source values
        commonColumns.forEach(column => {
          const mapping = this.columnMappings.find(mapping => mapping.source === column);
          if (mapping) {
            const mappingIndex = this.columnMappings.findIndex(mapping => mapping.source === column);
            mapping.destination = column; // Map the source column to destination column
            this.onDestinationChange(column, mappingIndex)
          }
        });
      } else {
        //console.error('Error fetching destination columns');
      }
    } else {
      // If the destination table is 'Create New', simply map source columns to destination columns
      this.destinationColumns = [...this.sourceColumns];
      //console.log('Destination columns are mapped as Create New:', this.sourceColumns);

      this.sourceColumns.forEach(column => {
        const mapping = this.columnMappings.find(mapping => mapping.source === column);
        if (mapping) {
          const mappingIndex = this.columnMappings.findIndex(mapping => mapping.source === column);
          mapping.destination = column; // Map the source column to destination column
          this.onDestinationChange(column, mappingIndex)
        }
      });
    }
  }


  async mapWithAi(e) {
    this.isMappingWithAi = true
    e.preventDefault();
    this.mappedColumns.sourceColumns = this.sourceColumns;
    this.mappedColumns.destinationColumns = this.destinationColumns;
    var res = await this._genaiService.mapColumns(this.mappedColumns).toPromise()
    if (res) {
      this.columnMappings = [...res];

      this.selectedSources = [];
      this.selectedDestinations = [];
      let count = 0;
      this.columnMappings.forEach(mapping => {
        // //console.log(mapping);
        this.onSourceChange(mapping.source, count);
        this.onDestinationChange(mapping.destination, count);

        count++;
      })
      count = 0;
      this.isMappingWithAi = false
    }
  }

  onClose(): void {
    this.dialogRef.close(null);
  }

  saveMappings() {
    if (this.selectedDestinationPrimaryKey.length == 0 || this.selectedSourcePrimaryKey.length == 0) {

      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {


      const formData = {
        sourcePrimaryKey: this.selectedSourcePrimaryKey.join('@#'),
        destinationPrimaryKey: this.selectedDestinationPrimaryKey.join('@#'),
        columnMappings: this.columnMappings
      };
      this.dialogRef.close(formData);
    }
  }
  selectedDestinations: string[] = []; // Tracks all selected destination columns
  selectedSources: string[] = []; // Tracks all selected destination columns
  // Function to handle the selection change
  private previousColumn: string | null = null; // Temporarily stores the previous value

  columnFocus(currentValue: string) {
    this.previousColumn = currentValue; // Store the current value before change
  }

  filteredDestinationColumns(index: number) {
    // Allow columns that are not selected or the one currently selected for this row
    return this.destinationColumns.filter(
      column =>
        !this.selectedDestinations.includes(column) ||
        this.columnMappings[index]?.destination === column
    );
  }

  onDestinationChange(newColumn: string, index: number) {
    // Remove the previous column from selectedDestinations if it exists
    if (this.previousColumn) {
      this.selectedDestinations = this.selectedDestinations.filter(
        col => col !== this.previousColumn
      );
    }

    // Add the new column to the list
    if (newColumn && !this.selectedDestinations.includes(newColumn)) {
      this.selectedDestinations.push(newColumn);
    }

    // Update the mapping
    this.columnMappings[index].destination = newColumn;

    // //console.log("Updated Selected Destinations:", this.selectedDestinations);
    // //console.log("Current Mapping:", this.columnMappings);
  }
  filteredSourceColumns(index: number) {
    // Allow columns that are not selected or the one currently selected for this row
    return this.sourceColumns.filter(
      column =>
        !this.selectedSources.includes(column) ||
        this.columnMappings[index]?.source === column
    );
  }

  onSourceChange(newColumn: string, index: number) {
    // Remove the previous column from selectedSources if it exists
    if (this.previousColumn) {
      this.selectedSources = this.selectedSources.filter(
        col => col !== this.previousColumn
      );
    }

    // Add the new column to the list
    if (newColumn && !this.selectedSources.includes(newColumn)) {
      this.selectedSources.push(newColumn);
    }

    // Update the mapping
    this.columnMappings[index].source = newColumn;

    // //console.log("Updated Selected Sources:", this.selectedSources);
    // //console.log("Current Mapping:", this.columnMappings);
  }
  // Your removetable function for handling row deletions
  removetable(index: number) {
    // Remove the destination column from the selectedDestinations list if it exists
    const removedDestination = this.columnMappings[index].destination;
    this.selectedDestinations = this.selectedDestinations.filter(col => col !== removedDestination);
    const removedSource = this.columnMappings[index].source;
    this.selectedSources = this.selectedSources.filter(col => col !== removedSource);

    // Now remove the mapping from the columnMappings array
    this.columnMappings.splice(index, 1);
  }
}


