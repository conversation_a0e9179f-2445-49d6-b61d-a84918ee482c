﻿using AdminPortalBackend.Core.Entities;

namespace AdminPortalBackend.Core.Extensions;

public static class ConnectionIntegrationExtensions
{
    // Extension method to get Source Connection Info
    public static IntegrationInfo GetSourceConnectionIntegration(this ConnectionIntegration integration)
    {
        return new IntegrationInfo
        {
            IntegrationId = integration.Guid,
            ConnectionGuid = integration.SourceConnectionGuid,
            ConnectionName = integration.SourceConnectionName,
            Database = integration.SourceDatabase,
            Table = integration.SourceTable,
            PrimaryKey = integration.SourcePrimaryKey,
            Settings = integration.Settings
        };
    }

    // Extension method to get Destination Connection Info
    public static IntegrationInfo GetDestinationConnectionIntegration(this ConnectionIntegration integration)
    {
        return new IntegrationInfo
        {
            IntegrationId = integration.Guid,
            ConnectionGuid = integration.DestinationConnectionGuid,
            ConnectionName = integration.DestinationConnectionName,
            Database = integration.DestinationDatabase,
            Table = integration.DestinationTable,
            PrimaryKey = integration.DestinationPrimaryKey,
            Settings = integration.Settings
        };
    }
}
