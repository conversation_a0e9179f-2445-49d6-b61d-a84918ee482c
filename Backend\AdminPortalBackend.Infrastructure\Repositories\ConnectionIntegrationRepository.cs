﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Repositiories;
using Dapper;
using System.Data;
using System.Data.SqlClient;
using System.Text.Json;
using Hangfire;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.DataStore;
using Microsoft.Extensions.Logging;
using System.Text;
using Hangfire.Storage;

namespace AdminPortalBackend.Infrastructure.Repositories
{
    public class ConnectionIntegrationRepository(IDbConnection _dbConnection,
        IDbConnectionRepository _dbConnectionRepo, ODataService _oDataService, SyncManager _syncManager,
        ILogger<ConnectionIntegrationRepository> _logger) : IConnectionIntegrationRepository
    {
        private async Task<string> GetConnectionString(Guid guid, string databaseName = null)
        {
            var dbConnection = await _dbConnectionRepo.GetConnection(guid);
            if (dbConnection != null)
            {
                var connectionCreds = JsonSerializer.Deserialize<MssqlServerCredDto>(dbConnection.ConnectionCredJson);
                if (connectionCreds != null)
                {
                    return $"Server={connectionCreds.ServerName};" +
                           (databaseName != null ? $"Database={databaseName};" : "") +
                           $"User Id={connectionCreds.UserId};" +
                           $"Password={connectionCreds.Password};" +
                           $"TrustServerCertificate={(connectionCreds.TrustedCertificate ? "yes" : "no")};";
                }
            }
            return null;
        }

        public async Task<ResponseMessageList> GetDatabase(Guid guid)
        {
            _logger.LogInformation("Fetching SQL Database");
            var connectionString = await GetConnectionString(guid);
            if (connectionString == null)
                return new ResponseMessageList { IsError = true, Message = new[] { "Invalid guid" } };

            var databases = new List<string>();
            try
            {
                using (var sqlConnection = new SqlConnection(connectionString))
                {
                    await sqlConnection.OpenAsync();
                    using (var command = new SqlCommand(
                        @"SELECT name 
                            FROM sys.databases
                            WHERE database_id > 4 ORDER BY name;",
                        sqlConnection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            databases.Add(reader.GetString(0));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error: " + ex.Message);
                return new ResponseMessageList { IsError = true, Message = new[] { ex.Message } };
            }
            _logger.LogInformation("Fetched SQL Database");
            return new ResponseMessageList { IsError = false, Message = databases.ToArray() };
        }

        public async Task<ResponseMessageList> GetTables(Guid guid, string databaseName)
        {
            _logger.LogInformation("Fetching SQL Table");
            var connectionString = await GetConnectionString(guid, databaseName);
            if (connectionString == null)
                return new ResponseMessageList { IsError = true, Message = new[] { "Invalid guid or database name" } };

            var tables = new List<string>();
            try
            {
                using (var sqlConnection = new SqlConnection(connectionString))
                {
                    await sqlConnection.OpenAsync();
                    using (var command = new SqlCommand("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE IN ('BASE TABLE', 'VIEW') ORDER BY TABLE_NAME", sqlConnection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            tables.Add(reader.GetString(0));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: " + ex.Message);
                return new ResponseMessageList { IsError = true, Message = new[] { ex.Message } };
            }
            _logger.LogInformation("Fetched SQL Table");
            return new ResponseMessageList { IsError = false, Message = tables.ToArray() };
        }

        public async Task<ResponseMessageList> GetColumns(Guid guid, string databaseName, string tableName)
        {
            _logger.LogInformation("Fetching SQL Column");
            var connectionString = await GetConnectionString(guid, databaseName);
            if (connectionString == null)
                return new ResponseMessageList { IsError = true, Message = new[] { "Invalid guid, database name, or table name" } };

            var columns = new List<string>();
            try
            {
                using (var sqlConnection = new SqlConnection(connectionString))
                {
                    await sqlConnection.OpenAsync();
                    using (var command = new SqlCommand("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = @TableName", sqlConnection))
                    {
                        command.Parameters.AddWithValue("@TableName", tableName);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                columns.Add(reader.GetString(0));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error: " + ex.Message);
                return new ResponseMessageList { IsError = true, Message = new[] { ex.Message } };
            }
            _logger.LogInformation("Fetched SQL Column");
            return new ResponseMessageList { IsError = false, Message = columns.ToArray() };
        }

        public async Task<ConnectionIntegration> Edit(ConnectionIntegration integration)
        {
            _logger.LogInformation("Editing sql integration");
            integration.LastModifiedDate = DateTime.Now;

            var sql = @"
                UPDATE ConnectionIntegrations
                SET IntegrationName = @IntegrationName,
                    SourceConnectionGuid = @SourceConnectionGuid,
                    SourceConnectionName = @SourceConnectionName,
                    SourceDatabase = @SourceDatabase,
                    SourceTable = @SourceTable,
                    DestinationConnectionGuid = @DestinationConnectionGuid,
                    DestinationConnectionName = @DestinationConnectionName,
                    DestinationDatabase = @DestinationDatabase,
                    DestinationTable = @DestinationTable,
                    SourcePrimaryKey = @SourcePrimaryKey,
                    DestinationPrimaryKey = @DestinationPrimaryKey,
                    JobFrequency = @JobFrequency,
                    MappedColumns = @MappedColumns,
                    LastModifiedDate = @LastModifiedDate,
                    Settings = @Settings
                WHERE Guid = @Guid;";

            var affectedRows = await _dbConnection.ExecuteAsync(sql, integration);

            if (affectedRows > 0)
            {
                if(integration.JobFrequency != "On Demand")
                {
                    JobRequest request = new JobRequest { ReqeustGuid = integration.Guid, Frequency = integration.JobFrequency };
                    await ScheduleJob(request);
                    return integration;
                }
                else
                {
                    return integration;
                }
            }
            _logger.LogInformation("Edited sql integration");
            return integration;
        }

        public async Task<ResponseMessage> Create(CreateConnectionIntegrationDto integration)
        {
            using (_logger.BeginScope(new Dictionary<string, object> { { "SourceConnectionId", integration.SourceConnectionGuid } }))
            {
                _logger.LogInformation("Creating Sql Integration");
                var sourceConnection = await _dbConnectionRepo.GetConnection(integration.SourceConnectionGuid);
                var type = sourceConnection.Type;
                for (int i = 0; i < integration.SourceTable.Count; i++)
                {
                    var destinationTableName = integration.DestinationTable[i];
                    if (destinationTableName.ToLower() == "create new")
                    {
                        destinationTableName = integration.SourceTable[i];
                    }
                    var destinationConnectionString = await GetConnectionString(integration.DestinationConnectionGuid, integration.DestinationDatabase);

                    // Assuming you have a method to create a new connection
                    using (var destinationConnection = new SqlConnection(destinationConnectionString))
                    {
                        await destinationConnection.OpenAsync();

                        // Check if the destination table exists
                        var tableExistsSql = @"
                        SELECT COUNT(*) 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_NAME = @TableName AND TABLE_CATALOG = @DatabaseName";

                        var tableExists = await destinationConnection.ExecuteScalarAsync<int>(tableExistsSql, new
                        {
                            TableName = destinationTableName,
                            DatabaseName = integration.DestinationDatabase
                        });

                        try
                        {
                            // If the table does not exist, create it from the source table's schema
                            if (tableExists == 0)
                            {
                                var createTableSql = await GetCreateTableSql(integration.SourceConnectionGuid, integration.SourceDatabase, integration.SourceTable[i], type);
                                await destinationConnection.ExecuteAsync(createTableSql);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error: " + ex.Message);
                            throw new Exception(ex.Message);
                        }
                    }



                    var guid = Guid.NewGuid();
                    string integrationName = $"{integration.SourceDatabase}__{integration.SourceTable[i]}_To_{destinationTableName}";

                    var sql = @"
                        INSERT INTO ConnectionIntegrations (Guid, IntegrationName, 
                        SourceConnectionGuid, SourceConnectionName, SourceDatabase, SourceTable, 
                        DestinationConnectionGuid, DestinationConnectionName, DestinationDatabase, DestinationTable, 
                        SourcePrimaryKey, DestinationPrimaryKey, JobFrequency, IsActive, MappedColumns, CreatedDate, Settings)
                        VALUES (@Guid, @IntegrationName, 
                        @SourceConnectionGuid, @SourceConnectionName, @SourceDatabase, @SourceTable, 
                        @DestinationConnectionGuid, @DestinationConnectionName, @DestinationDatabase, @DestinationTable, 
                        @SourcePrimaryKey, @DestinationPrimaryKey, @JobFrequency, 1, @MappedColumns, @CreatedDate, @Settings);
                        SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";

                    var currentIntegration = new
                    {
                        Guid = guid,
                        IntegrationName = integrationName,
                        SourceConnectionGuid = integration.SourceConnectionGuid,
                        SourceConnectionName = integration.SourceConnectionName,
                        SourceDatabase = integration.SourceDatabase,
                        SourceTable = integration.SourceTable[i],
                        DestinationConnectionGuid = integration.DestinationConnectionGuid,
                        DestinationConnectionName = integration.DestinationConnectionName,
                        DestinationDatabase = integration.DestinationDatabase,
                        DestinationTable = destinationTableName,
                        SourcePrimaryKey = integration.SourcePrimaryKey[i],
                        DestinationPrimaryKey = integration.DestinationPrimaryKey[i],
                        JobFrequency = integration.JobFrequency,
                        MappedColumns = integration.MappedColumns[i],
                        CreatedDate = DateTime.Now,
                        Settings = JsonSerializer.Serialize(new { BcToSql = "Clean Load" }),
                    };

                    var res = await _dbConnection.QuerySingleAsync<ConnectionIntegration>(sql, currentIntegration);
                    if (integration.JobFrequency != "On Demand")
                    {
                        JobRequest request = new JobRequest { ReqeustGuid = guid, Frequency = integration.JobFrequency };
                        await ScheduleJob(request);
                    }
                }
            }

            return new ResponseMessage { IsError = false, Message = "Added Successfully" };
        }

        // This method retrieves the SQL to create a table from the source database
        private async Task<string> GetCreateTableSql(Guid sourceConnectionGuid, string databaseName, string tableName, string type)
        {
            if (type == "Ms SQL Server")
            {
                var connectionString = await GetConnectionString(sourceConnectionGuid, databaseName);
                using (var sourceConnection = new SqlConnection(connectionString))
                {
                    await sourceConnection.OpenAsync();

                    var schemaSql = $@"
                        SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_NAME = @TableName AND TABLE_CATALOG = @DatabaseName";

                    var columns = await sourceConnection.QueryAsync<dynamic>(schemaSql, new { TableName = tableName, DatabaseName = databaseName });

                    var createTableSql = $"CREATE TABLE [{tableName}] (";

                    foreach (var column in columns)
                    {
                        var columnDefinition = $"{column.COLUMN_NAME} {column.DATA_TYPE}";

                        if (column.DATA_TYPE == "varchar" || column.DATA_TYPE == "nvarchar")
                        {
                            if (column.CHARACTER_MAXIMUM_LENGTH == -1)
                            {
                                columnDefinition += "(max)";
                            }
                            else
                            {
                                columnDefinition += $"({column.CHARACTER_MAXIMUM_LENGTH})";
                            }
                        }

                        createTableSql += $"{columnDefinition}, ";
                    }

                    createTableSql = createTableSql.TrimEnd(',', ' ') + ");";
                    _logger.LogInformation("Generated MSSql query" + createTableSql);
                    return createTableSql;
                }
            }
            else if (type == "BCODataWebService" || type == "BCODataRestApiService")
            {
                var metadataQuery = "SELECT [EntityName], [PropertyName], [PropertyType], [Size] FROM BCEntityMetadata WHERE EntityName = @TableName AND ConnectionId = @ConnectionId";
                var properties = await _dbConnection.QueryAsync<BCEntityMetadata>(metadataQuery, new { TableName = tableName, ConnectionId = sourceConnectionGuid });

                var propertySizeQuery = "SELECT [ServiceFieldName], [Size] FROM [ServiceTableView] WHERE DataType IN('varchar', 'nvarchar') AND ServiceName = @ServiceName";
                var propLengths = _dbConnection.Query(propertySizeQuery, new { ServiceName = tableName }).ToDictionary(
                    row => (string)row.ServiceFieldName,
                    row => (int)row.Size
                );

                var responseMessageList = new ResponseMessageList();
                var sqlColumns = new List<string>();

                foreach (var prop in properties)
                {
                    string sqlType = ConvertEdmTypeToSql(prop.PropertyType); // Convert EDM type to SQL type

                    if (sqlType == "NVARCHAR" || sqlType == "VARCHAR")
                    {
                        if (prop.Size != null)
                        {
                            sqlType = $"{sqlType}({prop.Size})";
                        }
                        else
                        {
                            // If Size is null, try to get the size from the dictionary
                            if (propLengths.ContainsKey(prop.PropertyName) && propLengths[prop.PropertyName] > 0)
                            {
                                // If found in dictionary and the size is positive, use the size from the dictionary
                                sqlType = $"{sqlType}({propLengths[prop.PropertyName]})";
                            }
                            else
                            {
                                // If not found in dictionary or the size is not positive, set a default size (e.g., VARCHAR(MAX))
                                sqlType = $"{sqlType}(MAX)";
                            }
                        }
                    }

                    // Add column definition to the list
                    sqlColumns.Add($"[{prop.PropertyName}] {sqlType}");
                }


                // Generate the CREATE TABLE SQL statement
                string createTableSql = $"CREATE TABLE [{databaseName}_{tableName}] (\n" + string.Join(",\n", sqlColumns) + "\n);";

                _logger.LogInformation($"Generated CREATE TABLE query: {createTableSql}");

                // Return or execute the SQL statement
                return createTableSql;

            }
            return "";
        }

        public async Task<ResponseMessage> CreateTableForODataWebService(CreateConnectionIntegrationDto integration, bool isExecute)
        {
            _logger.LogInformation("Creating Destination table for OData");
            var databases = integration.SourceDatabase.Split("@#");
            foreach (var database in databases)
            {
                for (int i = 0; i < integration.SourceTable.Count; i++)
                {
                    var destinationTableName = database + "_" + integration.SourceTable[i];
                    var primaryKeys = await GetPrimaryKey(integration.SourceConnectionGuid, integration.SourceTable[i]);
                    var destinationConnectionString = await GetConnectionString(integration.DestinationConnectionGuid, integration.DestinationDatabase);
                    var mappedColumn = "";
                    // Assuming you have a method to create a new connection
                    using (var destinationConnection = new SqlConnection(destinationConnectionString))
                    {
                        await destinationConnection.OpenAsync();

                        // Check if the destination table exists
                        var tableExistsSql = @"
                        SELECT COUNT(*) 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_NAME = @TableName AND TABLE_CATALOG = @DatabaseName";

                        var tableExists = await destinationConnection.ExecuteScalarAsync<int>(tableExistsSql, new
                        {
                            TableName = destinationTableName,
                            DatabaseName = integration.DestinationDatabase
                        });

                        // If the table does not exist, create it from the source table's schema
                        if (tableExists == 0)
                        {

                            var sourceConnection = await _dbConnectionRepo.GetConnection(integration.SourceConnectionGuid);

                            var createTableSql = await GetCreateTableSql(integration.SourceConnectionGuid, database, integration.SourceTable[i], sourceConnection.Type);
                            await destinationConnection.ExecuteAsync(createTableSql);
                            var columns = await GetPropertiesForEntity(integration.SourceTable[i], integration.SourceConnectionGuid);
                            var dictionary = new Dictionary<string, string>();

                            foreach (var column in columns.Message)
                            {
                                // Assigning column to both key and value in the dictionary
                                dictionary[column] = column;
                            }

                            // Convert the dictionary to JSON
                            mappedColumn = JsonSerializer.Serialize(dictionary);

                            var guid = Guid.NewGuid();
                            string integrationName = $"{database}__{integration.SourceTable[i]}_To_{destinationTableName}";

                            var sql = @"
                        INSERT INTO ConnectionIntegrations (Guid, IntegrationName, 
                        SourceConnectionGuid, SourceConnectionName, SourceDatabase, SourceTable, 
                        DestinationConnectionGuid, DestinationConnectionName, DestinationDatabase, DestinationTable, 
                        SourcePrimaryKey, DestinationPrimaryKey, JobFrequency, IsActive, MappedColumns, Settings, CreatedDate)
                        VALUES (@Guid, @IntegrationName, 
                        @SourceConnectionGuid, @SourceConnectionName, @SourceDatabase, @SourceTable, 
                        @DestinationConnectionGuid, @DestinationConnectionName, @DestinationDatabase, @DestinationTable, 
                        @SourcePrimaryKey, @DestinationPrimaryKey, @JobFrequency, 0, @MappedColumns, @Settings, @CreatedDate);
                        SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";

                            var currentIntegration = new
                            {
                                Guid = guid,
                                IntegrationName = integrationName,
                                SourceConnectionGuid = integration.SourceConnectionGuid,
                                SourceConnectionName = integration.SourceConnectionName,
                                SourceDatabase = database,
                                SourceTable = integration.SourceTable[i],
                                DestinationConnectionGuid = integration.DestinationConnectionGuid,
                                DestinationConnectionName = integration.DestinationConnectionName,
                                DestinationDatabase = integration.DestinationDatabase,
                                DestinationTable = destinationTableName,
                                SourcePrimaryKey = string.Join("@#", primaryKeys),
                                DestinationPrimaryKey = string.Join("@#", primaryKeys),
                                JobFrequency = integration.JobFrequency,
                                MappedColumns = mappedColumn,
                                CreatedDate = DateTime.Now,
                                Settings = integration.Settings,
                            };

                            var res = await _dbConnection.QuerySingleAsync<ConnectionIntegration>(sql, currentIntegration);
                            if (isExecute)
                            {
                                await ExecuteJob(res.Guid);
                            }
                        }
                    }
                }

            }
            RecurringJob.AddOrUpdate(
                    "ProcessBCToSqlUpdate",
                    () => _syncManager.ProcessBCToSqlUpdate(),
                    "* * * * *" // Cron expression for every minute
                );
            _logger.LogInformation("Added Destination table for OData");

            return new ResponseMessage { IsError = false, Message = "Added Successfully" };
        }

        public async Task<ResponseMessage> EditTableForODataWebService(ConnectionIntegration integration)
        {
            _logger.LogInformation("Editing Destination table for OData");

            var destinationTableName = $"{integration.SourceDatabase}_{integration.SourceTable}";
            var destinationConnectionString = await GetConnectionString(integration.DestinationConnectionGuid, integration.DestinationDatabase);
            var integrationName = integration.IntegrationName;

            string mappedColumn = integration.MappedColumns;
            var primaryKeys = integration.SourcePrimaryKey.Split("@#").ToList();

            using (var destinationConnection = new SqlConnection(destinationConnectionString))
            {
                await destinationConnection.OpenAsync();

                // Check if the destination table exists
                var tableExistsSql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = @TableName AND TABLE_CATALOG = @DatabaseName";

                var tableExists = await destinationConnection.ExecuteScalarAsync<int>(tableExistsSql, new
                {
                    TableName = destinationTableName,
                    DatabaseName = integration.DestinationDatabase
                });

                if (tableExists == 0)
                {
                    mappedColumn = "";
                    // call primary key
                    var sourceConnection = await _dbConnectionRepo.GetConnection(integration.SourceConnectionGuid);
                    var createTableSql = await GetCreateTableSql(integration.SourceConnectionGuid, integration.SourceDatabase, integration.SourceTable, sourceConnection.Type);
                    await destinationConnection.ExecuteAsync(createTableSql);

                    integrationName = $"{integration.SourceDatabase}__{integration.SourceTable}_To_{destinationTableName}";
                    var columns = await GetPropertiesForEntity(integration.SourceTable, integration.SourceConnectionGuid);
                    if (columns.Message != null && columns.Message.Any())
                    {
                        var dictionary = columns.Message.ToDictionary(column => column, column => column);
                        mappedColumn = JsonSerializer.Serialize(dictionary);
                    }
                }

                // Now perform the update operation
                var sql = @"
                    UPDATE ConnectionIntegrations 
                    SET IntegrationName = @IntegrationName, 
                        SourceConnectionGuid = @SourceConnectionGuid, 
                        SourceConnectionName = @SourceConnectionName, 
                        SourceDatabase = @SourceDatabase, 
                        SourceTable = @SourceTable, 
                        DestinationConnectionGuid = @DestinationConnectionGuid, 
                        DestinationConnectionName = @DestinationConnectionName, 
                        DestinationDatabase = @DestinationDatabase, 
                        DestinationTable = @DestinationTable, 
                        SourcePrimaryKey = @SourcePrimaryKey, 
                        DestinationPrimaryKey = @DestinationPrimaryKey, 
                        JobFrequency = @JobFrequency, 
                        IsActive = isActive, 
                        MappedColumns = @MappedColumns,
                        Settings = @Settings,
                        LastModifiedDate = @LastModifiedDate
                    WHERE Guid = @Guid;";

                var currentIntegration = new
                {
                    Guid = integration.Guid,
                    IntegrationName = integrationName,
                    SourceConnectionGuid = integration.SourceConnectionGuid,
                    SourceConnectionName = integration.SourceConnectionName,
                    SourceDatabase = integration.SourceDatabase,
                    SourceTable = integration.SourceTable,
                    DestinationConnectionGuid = integration.DestinationConnectionGuid,
                    DestinationConnectionName = integration.DestinationConnectionName,
                    DestinationDatabase = integration.DestinationDatabase,
                    DestinationTable = destinationTableName,
                    SourcePrimaryKey = string.Join("@#", primaryKeys),
                    DestinationPrimaryKey = string.Join("@#", primaryKeys),
                    JobFrequency = integration.JobFrequency,
                    MappedColumns = mappedColumn,
                    isActive = integration.IsActive,
                    Settings = integration.Settings,
                    LastModifiedDate = DateTime.Now,
                };

                await _dbConnection.ExecuteAsync(sql, currentIntegration);
            }
            _logger.LogInformation("Edited Destination table for OData");
            return new ResponseMessage { IsError = false, Message = "Updated Successfully" };
        }


        private string ConvertEdmTypeToSql(string edmType)
        {
            // Directly mapping EDM types to SQL types
            switch (edmType)
            {
                case "Edm.String":
                    return "NVARCHAR";
                case "Edm.Int32":
                    return "INT";
                case "Edm.Int64":
                    return "BIGINT";
                case "Edm.Decimal":
                    return "DECIMAL(38, 4)";
                case "Edm.Boolean":
                    return "BIT";
                case "Edm.DateTimeOffset":
                    return "DATETIMEOFFSET";
                case "Edm.Date":
                    return "DATE";  // Or DATETIME depending on your use case
                case "Edm.Guid":
                    return "NVARCHAR(100)";
                case "Edm.Stream":
                    return "VARBINARY(MAX)";  // For byte array (stream)
                default:
                    return "NVARCHAR(MAX)";  // Fallback type for unknown EDM types
            }
        }




        public async Task<List<ConnectionIntegration>> GetSqlSqlAllAsync()
        {
            // Using OUTER APPLY instead of correlated subquery for better performance
            // CASE expressions are computed at the database level, reducing load on application
            var sql = @"
                SELECT ci.*,
                    LastExec.FormattedTime as LastExecutionDate
                FROM ConnectionIntegrations ci
                JOIN DbConnections db ON ci.SourceConnectionGuid = db.guid
                OUTER APPLY (
                    SELECT TOP 1 
                        CASE
                            WHEN DATEDIFF(DAY, EndTime, GETDATE()) > 0 
                                THEN CONCAT(DATEDIFF(DAY, EndTime, GETDATE()), 'd ago')
                            WHEN DATEDIFF(HOUR, EndTime, GETDATE()) > 0 
                                THEN CONCAT(DATEDIFF(HOUR, EndTime, GETDATE()), 'h ago')
                            WHEN DATEDIFF(MINUTE, EndTime, GETDATE()) > 0 
                                THEN CONCAT(DATEDIFF(MINUTE, EndTime, GETDATE()), 'm ago')
                            ELSE CONCAT(DATEDIFF(SECOND, EndTime, GETDATE()), 's ago')
                        END as FormattedTime
                    FROM JobLogEntries 
                    WHERE ProcessId = ci.Guid 
                    ORDER BY LogId DESC
                ) LastExec
                WHERE db.type = 'Ms SQL Server' 
                ORDER BY ci.DestinationTable;";

            var result = await _dbConnection.QueryAsync<ConnectionIntegration>(sql);
            return result.ToList();
        }

        public async Task<List<ConnectionIntegration>> GetBcSqlAllAsync()
        {
            // Using OUTER APPLY instead of correlated subquery for better performance
            // CASE expressions are computed at the database level, reducing load on application
            var sql = @"
                SELECT ci.*,
                    LastExec.FormattedTime as LastExecutionDate
                FROM ConnectionIntegrations ci
                JOIN DbConnections db ON ci.SourceConnectionGuid = db.guid
                OUTER APPLY (
                    SELECT TOP 1 
                        CASE
                            WHEN DATEDIFF(DAY, EndTime, GETDATE()) > 0 
                                THEN CONCAT(DATEDIFF(DAY, EndTime, GETDATE()), 'd ago')
                            WHEN DATEDIFF(HOUR, EndTime, GETDATE()) > 0 
                                THEN CONCAT(DATEDIFF(HOUR, EndTime, GETDATE()), 'h ago')
                            WHEN DATEDIFF(MINUTE, EndTime, GETDATE()) > 0 
                                THEN CONCAT(DATEDIFF(MINUTE, EndTime, GETDATE()), 'm ago')
                            ELSE CONCAT(DATEDIFF(SECOND, EndTime, GETDATE()), 's ago')
                        END as FormattedTime
                    FROM JobLogEntries 
                    WHERE ProcessId = ci.Guid 
                    ORDER BY LogId DESC
                ) LastExec
                WHERE db.type IN ('BCODataWebService', 'BCODataRestApiService')
                ORDER BY ci.SourceTable;";

            var result = await _dbConnection.QueryAsync<ConnectionIntegration>(sql);
            return result.ToList();
        }


        public async Task<ConnectionIntegration> GetByIdAsync(Guid guid)
        {
            var sql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";
            return await _dbConnection.QuerySingleOrDefaultAsync<ConnectionIntegration>(sql, new { Guid = guid });
        }

        public async Task<ConnectionIntegration> DeleteAsync(Guid guid)
        {
            _logger.LogInformation("Deleting Integration");
            var integration = await GetByIdAsync(guid);
            if (integration != null)
            {
                var sql = "DELETE FROM ConnectionIntegrations WHERE Guid = @Guid;";
                await _dbConnection.ExecuteAsync(sql, new { Guid = guid });
                _logger.LogError("Deleted Integration");
                await DisableJob(guid);
                return integration; // Return the deleted integration
            }
            var ex = new Exception("Not found");
            _logger.LogError(ex, "Error: Integration Not found");
            throw ex;
        }

        public async Task<ResponseMessage> ScheduleJob(JobRequest request)
        {
            _logger.LogInformation("Scheduling job and setting integration to active");
            var responseMessage = new ResponseMessage();
            string cronExpression;

            // Get a deterministic minute offset (0-59) based on the GUID
            int minuteOffset = Math.Abs(request.ReqeustGuid.GetHashCode()) % 60;

            // Determine the appropriate cron expression based on the frequency
            switch (request.Frequency.ToLower())
            {
                case "realtime":
                    cronExpression = "* * * * *"; // Every minute (no offset needed)
                    break;
                case "hourly":
                    cronExpression = $"{minuteOffset} * * * *"; // Every hour at the offset minute
                    break;
                case "daily":
                    cronExpression = $"{minuteOffset} 0 * * *"; // Every day at 00:XX where XX is the offset
                    break;
                case "weekly":
                    cronExpression = $"{minuteOffset} 0 * * 0"; // Every Sunday at 00:XX where XX is the offset
                    break;
                default:
                    throw new ArgumentException("Invalid frequency specified.");
            }

            try
            {
                RecurringJob.AddOrUpdate(
                    request.ReqeustGuid.ToString(),
                    () => _syncManager.SyncDataFromSourceToTarget(request.ReqeustGuid, CancellationToken.None),
                    cronExpression
                );

                // Update IsActive to true for the scheduled job
                var sql = @"
                UPDATE ConnectionIntegrations
                SET IsActive = 1
                WHERE Guid = @Guid;";

                await _dbConnection.ExecuteAsync(sql, new { Guid = request.ReqeustGuid });

                responseMessage.IsError = false;
                responseMessage.Message = $"Job Scheduled Successfully to run at minute {minuteOffset}";
            }
            catch (Exception ex)
            {
                _logger.LogError("Error: " + ex.Message);
                responseMessage.IsError = true;
                responseMessage.Message = "Something went wrong";
            }
            return responseMessage;
        }

        public async Task<ResponseMessage> DisableJob(Guid jobId)
        {
            _logger.LogInformation("Deleting job and setting integration to inactive");
            var responseMessage = new ResponseMessage();

            try
            {
                // Remove the recurring job
                RecurringJob.RemoveIfExists(jobId.ToString());

                // Update IsActive to false for the disabled job
                var sql = @"
                UPDATE ConnectionIntegrations
                SET IsActive = 0
                WHERE Guid = @Guid;";

                await _dbConnection.ExecuteAsync(sql, new { Guid = jobId });

                _logger.LogInformation("Job Deleted successfully.");
                responseMessage.IsError = false;
                responseMessage.Message = "Job Scheduled Successfully";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: " + ex.Message);
                responseMessage.IsError = true;
                responseMessage.Message = "Something went wrong";
            }
            return responseMessage;
        }
        public async Task<ResponseMessage> DeleteJob(Guid jobId)
        {
            _logger.LogInformation("Attempting to delete a running job.");
            var responseMessage = new ResponseMessage();

            try
            {
                var connection = JobStorage.Current.GetMonitoringApi();
                var processingJobs = connection.ProcessingJobs(0, int.MaxValue);

                // Find the running job by the integration ID
                var runningJob = processingJobs.FirstOrDefault(job => job.Value.Job.Args.Contains(jobId));

                if (runningJob.Key != null)
                {
                    BackgroundJob.Delete(runningJob.Key); // Mark the job for deletion
                    _logger.LogInformation("Job marked for deletion.");

                    responseMessage.IsError = false;
                    responseMessage.Message = "Running job deleted successfully.";
                }
                else
                {
                    _logger.LogWarning("No running job found for the given ID.");
                    responseMessage.IsError = true;
                    responseMessage.Message = "No running job found for the given ID.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting running job: " + ex.Message);
                responseMessage.IsError = true;
                responseMessage.Message = "Something went wrong while deleting the running job.";
            }

            return responseMessage;
        }


        public async Task<ResponseMessage> ExecuteJob(Guid integrationId)
        {
            var responseMessage = new ResponseMessage();
            try
            {
                var sql = @"SELECT TOP 1 Status FROM JobLogEntries WHERE ProcessId = @Guid ORDER BY LogId DESC;";

                var status = await _dbConnection.QueryFirstOrDefaultAsync<string>(sql, new { Guid = integrationId });

                if (status == "Processing")
                {
                    return new ResponseMessage { IsError = true, Message = "One Process is Already Executing" };
                }


                _logger.LogInformation("Executing Job Manually");

                //trigger job immediately
                BackgroundJob.Enqueue(() => _syncManager.SyncDataFromSourceToTarget(integrationId, CancellationToken.None));

                responseMessage.IsError = false;
                responseMessage.Message = "Job executed successfully";
            }
            catch (Exception ex)
            {
                _logger.LogError("Error: " + ex.Message);
                responseMessage.IsError = true;
                responseMessage.Message = "Something went wrong: " + ex.Message;
            }

            return responseMessage;
        }


        public async Task<ResponseMessageList> GetEntityNames(Guid guid)
        {
            var sql = "select Name from BcEntities WHERE ConnectionId = @Guid ORDER BY Name;";
            var result = await _dbConnection.QueryAsync<string>(sql, new { Guid = guid });
            return new ResponseMessageList { IsError = false, Message = result.ToArray() };
        }

        public async Task<string> GetPrimaryKey(Guid guid, string entityName)
        {
            var sql = "select PrimaryKey from BcEntities WHERE ConnectionId = @Guid AND Name = @EntityName;";
            var result = await _dbConnection.QueryFirstAsync<string>(sql, new { Guid = guid, EntityName = entityName });
            return result;
        }

        public async Task<ResponseMessageList> GetPropertiesForEntity(string entityName, Guid guid)
        {
            var sql = "SELECT PropertyName FROM BCEntityMetadata WHERE EntityName = @EntityName AND ConnectionId = @Guid ORDER BY PropertyName;";
            var result = await _dbConnection.QueryAsync<string>(sql, new { EntityName = entityName, Guid = guid });
            return new ResponseMessageList { IsError = false, Message = result.ToArray() };
        }

        public async Task<ResponseMessageList> GetCompanyNames(Guid guid)
        {
            var sql = "select Name from BCCompanies WHERE ConnectionId = @Guid ORDER BY Name;";
            var result = await _dbConnection.QueryAsync<string>(sql, new { Guid = guid });
            return new ResponseMessageList { IsError = false, Message = result.ToArray() };
        }


        public async Task<ResponseMessage> CreateTrigger(Guid integrationGuid, Guid destinationGuid, string databaseName, string tableName, Guid sourceGuid)
        {
            _logger.LogInformation("Creating Trigger");
            var connectionString = await GetConnectionString(destinationGuid, databaseName);

            // SQL to check if ChangeTracking table exists
            var checkChangeTrackingTableSql = @"
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'ChangeTracking' AND TABLE_CATALOG = @DatabaseName";

            // SQL to create ChangeTracking table if it doesn't exist
            var createChangeTrackingTableSql = @"
            CREATE TABLE ChangeTracking (
                ChangeID INT IDENTITY(1,1) PRIMARY KEY,
                TableName NVARCHAR(255),
                Operation NVARCHAR(10),
                ChangeData NVARCHAR(MAX),
                ChangeDate DATETIME DEFAULT GETDATE(),
                Status NVARCHAR(50),
                OldData NVARCHAR(MAX),
                IntegrationId UNIQUEIDENTIFIER
            );";

            var entityName = tableName.Split('_')[1];
            var primaryKey = await GetPrimaryKey(sourceGuid, entityName);
            var keys = primaryKey.Split("@#").ToList();

            var primaryKeyConditions = string.Join(" AND ", keys.Select(pk => $"i.{pk} = d.{pk}"));

            string triggerSql = $@"
                IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'trg_{tableName}_Audit_witholddata')
                BEGIN
                    EXEC('
                    CREATE TRIGGER [dbo].[trg_{tableName}_Audit_witholddata]
                        ON [dbo].[{tableName}]
                        AFTER INSERT, UPDATE, DELETE
                        AS
                        BEGIN
                            -- Prevent unnecessary execution
                            IF SESSION_CONTEXT(N''SkipChangeTracking'') = N''1''
                                RETURN;

                            -- INSERT Operation: Log each inserted row
                            IF EXISTS (SELECT 1 FROM inserted) AND NOT EXISTS (SELECT 1 FROM deleted)
                            BEGIN
                                INSERT INTO ChangeTracking (TableName, Operation, ChangeData, OldData, Status, IntegrationId)
                                SELECT 
                                    N''{tableName}'', 
                                    ''INSERT'', 
                                    (SELECT i.* FOR JSON PATH, WITHOUT_ARRAY_WRAPPER), 
                                    NULL, 
                                    ''New'', 
                                    N''{integrationGuid}''
                                FROM inserted i;
                            END;

                            -- UPDATE Operation: Log each updated row
                            IF EXISTS (SELECT 1 FROM inserted) AND EXISTS (SELECT 1 FROM deleted)
                            BEGIN
                                INSERT INTO ChangeTracking (TableName, Operation, ChangeData, OldData, Status, IntegrationId)
                                SELECT 
                                    N''{tableName}'', 
                                    ''UPDATE'', 
                                    (SELECT i.* FOR JSON PATH, WITHOUT_ARRAY_WRAPPER), 
                                    (SELECT d.* FOR JSON PATH, WITHOUT_ARRAY_WRAPPER), 
                                    ''New'', 
                                    N''{integrationGuid}''
                                FROM inserted i
                                JOIN deleted d ON {primaryKeyConditions}; -- Use dynamic primary key condition
                            END;

                            -- DELETE Operation: Log each deleted row
                            IF EXISTS (SELECT 1 FROM deleted) AND NOT EXISTS (SELECT 1 FROM inserted)
                            BEGIN
                                INSERT INTO ChangeTracking (TableName, Operation, ChangeData, OldData, Status, IntegrationId)
                                SELECT 
                                    N''{tableName}'', 
                                    ''DELETE'', 
                                    NULL, 
                                    (SELECT d.* FOR JSON PATH, WITHOUT_ARRAY_WRAPPER), 
                                    ''New'', 
                                    N''{integrationGuid}''
                                FROM deleted d;
                            END;
                        END;');    
                END;";


            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Step 1: Check if the ChangeTracking table exists
                    var tableExists = await connection.ExecuteScalarAsync<int>(checkChangeTrackingTableSql, new
                    {
                        DatabaseName = databaseName
                    });

                    // Step 2: If the table does not exist, create it
                    if (tableExists == 0)
                    {
                        using (var createCommand = new SqlCommand(createChangeTrackingTableSql, connection))
                        {
                            await createCommand.ExecuteNonQueryAsync();
                        }


                        RecurringJob.AddOrUpdate(
                            $"ProcessSQLToBCUpdate_{databaseName}",
                            () => _syncManager.ProcessSQLToBCUpdate(destinationGuid.ToString(), databaseName),
                            "* * * * *"
                        );
                    }

                    // Step 3: Create the trigger if it doesn't exist
                    using (var triggerCommand = new SqlCommand(triggerSql, connection))
                    {
                        await triggerCommand.ExecuteNonQueryAsync();
                    }

                    await connection.CloseAsync();
                }
                _logger.LogInformation("Deleted Trigger");

                return new ResponseMessage { IsError = false, Message = "Trigger created successfully." };
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Error: " + ex.Message);
                return new ResponseMessage { IsError = true, Message = ex.Message };
            }
        }


        public async Task<ResponseMessage> DeleteTrigger(Guid destinationGuid, string databaseName, string tableName)
        {
            _logger.LogInformation("Deleting Trigger");

            var connectionString = await GetConnectionString(destinationGuid, databaseName);

            // SQL to drop the trigger if it exists
            string dropTriggerSql = $@"
                IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'trg_{tableName}_Audit_witholddata')
                BEGIN
                    DROP TRIGGER [dbo].[trg_{tableName}_Audit_witholddata];
                END;";

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Execute the command to drop the trigger
                    using (var command = new SqlCommand(dropTriggerSql, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }

                    await connection.CloseAsync();
                }
                _logger.LogInformation("Deleted Trigger");

                return new ResponseMessage { IsError = false, Message = "Trigger deleted successfully." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: " + ex.Message);
                return new ResponseMessage { IsError = true, Message = ex.Message };
            }
        }


        public async Task<ResponseMessageList> CheckTableExist(List<string> table, Guid destinationGuid, string destinationDatabase)
        {
            var connectionString = await GetConnectionString(destinationGuid, destinationDatabase);
            var existTable = new List<string>();
            var responseMessage = new ResponseMessageList();
            using (var destinationConnection = new SqlConnection(connectionString))
            {
                await destinationConnection.OpenAsync();

                foreach (var t in table)
                {
                    // Check if the destination table exists
                    var tableExistsSql = @"
                        SELECT COUNT(*) 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_NAME = @TableName AND TABLE_CATALOG = @DatabaseName";

                    var tableExists = await destinationConnection.ExecuteScalarAsync<int>(tableExistsSql, new
                    {
                        TableName = t,
                        DatabaseName = destinationDatabase
                    });

                    if (tableExists != 0)
                    {
                        existTable.Add(t);
                    }
                }
            }

            return new ResponseMessageList { IsError = false, Message = existTable.ToArray() };
        }

        public async Task<ResponseMessage> GetCompanyId(Guid connectionGuid, string companyName)
        {
            var companySql = $"Select [Id] from BCCompanies Where ConnectionId='{connectionGuid}' AND [Name]='{companyName}'";
            var companyId = _dbConnection.QuerySingleOrDefault<string>(companySql);
            return new ResponseMessage { IsError = false, Message = companyId };
        }

        public async Task<ResponseMessage> IsSystemRowVersionAvailable(string entityName, Guid guid)
        {
            // SQL query to check if 'SystemRowVersion' exists for the entity and connection ID
            var sql = "SELECT COUNT(1) FROM BCEntityMetadata WHERE EntityName = @EntityName AND ConnectionId = @Guid AND PropertyName = 'SystemRowVersion';";
            var result = await _dbConnection.ExecuteScalarAsync<int>(sql, new { EntityName = entityName, Guid = guid });

            if (result > 0)
            {
                return new ResponseMessage { IsError = false, Message = "SystemRowVersion property is available." };
            }
            else
            {
                return new ResponseMessage { IsError = true, Message = "SystemRowVersion property is not available." };
            }
        }

        public async Task<ResponseMessage> CreateView(CreateViewDto request)
        {
            try
            {
                var connectionString = await GetConnectionString(request.ConnectionGuid, request.DatabaseName);

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string tableName = request.TableName;
                    List<MappedColumn> mappedColumns = request.MappedColumns;

                    // Step 1: Generate SQL for the view
                    StringBuilder selectColumns = new StringBuilder();
                    foreach (var mapCol in mappedColumns)
                    {
                        selectColumns.AppendLine($"    [{mapCol.Source}] AS [{mapCol.Destination}],");
                    }

                    // Remove the trailing comma
                    if (selectColumns.Length > 0)
                        selectColumns.Length -= 3; // Remove last comma, line break

                    // Format view name
                    string viewName = request.ViewName;

                    // Step 2: Check if the view exists and drop it
                    string checkAndDropViewSql = $@"
                IF EXISTS (SELECT 1 FROM sys.views WHERE name = '{viewName}')
                BEGIN
                    DROP VIEW [{viewName}]
                END";

                    await connection.ExecuteAsync(checkAndDropViewSql);

                    // Step 3: Generate the full SQL to create the view
                    string createViewSql = $@"
                CREATE VIEW [{viewName}]
                AS
                SELECT
                {selectColumns.ToString()}
                FROM [{request.DatabaseName}].[dbo].[{tableName}]";

                    // Step 4: Execute SQL to create the view
                    await connection.ExecuteAsync(createViewSql);
                }

                return new ResponseMessage { IsError = false, Message = "View created successfully." };
            }
            catch (Exception ex)
            {
                // Log the exception and throw it to be handled by the caller
                Console.Error.WriteLine($"Error creating view: {ex.Message}");
                throw new InvalidOperationException("An error occurred while creating the view.", ex);
            }
        }
        public async Task<ResponseMessage> CheckIfViewExists(IsViewDto request)
        {
            var connectionString = await GetConnectionString(request.ConnectionGuid, request.DatabaseName);
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Query to check if the view exists
                    string checkViewSql = $@"
                        SELECT COUNT(1) 
                        FROM {request.DatabaseName}.sys.views 
                        WHERE name = @viewName";

                    // Execute the query and check if the view exists
                    var result = await connection.ExecuteScalarAsync<int>(checkViewSql, new { request.ViewName });

                    if (result > 0)
                    {
                        // Return message indicating the view exists
                        return new ResponseMessage { IsError = false, Message = $"View '{request.ViewName}' alReady exists." };
                    }
                    else
                    {
                        // Return message indicating the view does not exist
                        return new ResponseMessage { IsError = true, Message = $"View '{request.ViewName}' does not exist." };
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception and return an error message
                Console.Error.WriteLine($"Error checking view existence: {ex.Message}");
                return new ResponseMessage { IsError = true, Message = "An error occurred while checking the view existence." };
            }
        }



        public async Task<ResponseMessageList> GetFieldNames(string tableName)
        {
            try
            {
                var fields = await _dbConnection.QueryAsync<string>(
                   "SELECT [FieldName] FROM TableFields WHERE TableName = @TableName",
                   new { TableName = tableName }
                );

                return new ResponseMessageList { IsError = false, Message = fields.ToArray() };
            }
            catch (Exception ex)
            {
                return new ResponseMessageList { IsError = true, Message = ["not found"] };
            }
        }
        public async Task<ResponseMessageList> GetTableNames()
        {
            try
            {
                var fields = await _dbConnection.QueryAsync<string>(
                   "SELECT DISTINCT [TableName] FROM TableFields");

                return new ResponseMessageList { IsError = false, Message = fields.ToArray() };
            }
            catch (Exception ex)
            {
                return new ResponseMessageList { IsError = true, Message = ["not found"] };
            }
        }

        public async Task<ServiceField> GetFieldFromService(string serviceName)
        {
            try
            {
                // Query for FieldName and TableName where ServiceName matches
                var result = await _dbConnection.QueryAsync(
                    @"SELECT [TableName], [FieldName] 
                    FROM [ServiceTableView]
                    WHERE [ServiceName] = @ServiceName",
                    new { ServiceName = serviceName }
                );

                // Extract TableName and FieldName into separate collections
                var fieldNames = result.Select(r => (string)r.FieldName).ToList();
                var tableName = result.Select(r => (string)r.TableName).FirstOrDefault();  // Assuming the table name is the same for all rows

                return new ServiceField { TableName = tableName, FieldNames = fieldNames };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<ResponseMessage> ProcessSqlDelete()
        {
            string companyName = "";
            try
            {
                _logger.LogInformation("Starting SQL delete process");

                // Get all log table names from BCLogProcessingSetup
                var logTables = await _dbConnection.QueryAsync<string>(
                    "SELECT LogTableName FROM BCLogProcessingSetup"
                );

                // Get all last processed entries for all companies upfront
                var lastProcessedEntries = await _dbConnection.QueryAsync<(string Company, int LastEntry)>(@"
                    SELECT Company, MAX(CAST(LastProcessEntry as int)) as LastEntry
                    FROM BCProcessingLog 
                    GROUP BY Company");

                var companyLastEntries = lastProcessedEntries.ToDictionary(
                    x => x.Company,
                    x => x.LastEntry
                );

                var processedTables = new List<string>();
                var errors = new List<string>();

                foreach (var logTable in logTables)
                {
                    try
                    {
                        // Split table name to get company and table names
                        var parts = logTable.Split('_');
                        if (parts.Length < 2)
                        {
                            errors.Add($"Invalid table name format: {logTable}");
                            continue;
                        }

                        companyName = parts[0];
                        var tableName = parts[1];

                        _logger.LogInformation($"Processing tableName: {logTable}");

                        // Query ConnectionIntegrations for matching source
                        var sql = @"
                            SELECT DestinationConnectionGuid, DestinationDatabase
                            FROM ConnectionIntegrations Where DestinationTable = @TableName";

                        var destinationInfo = await _dbConnection.QueryFirstOrDefaultAsync<(Guid ConnectionGuid, string Database)>(
                            sql,
                            new
                            {
                                TableName = logTable
                            }
                        );

                        if (destinationInfo == default)
                        {
                            errors.Add($"No matching destination found for {companyName}.{tableName}");
                            continue;
                        }

                        // Get connection string using the existing method
                        var connectionString = await GetConnectionString(destinationInfo.ConnectionGuid, destinationInfo.Database);
                        if (connectionString == null)
                        {
                            errors.Add($"Could not generate connection string for database: {destinationInfo.Database}");
                            continue;
                        }

                        using (var connection = new SqlConnection(connectionString))
                        {
                            await connection.OpenAsync();

                            try
                            {
                                // First verify if the required columns exist
                                var columnsExist = await connection.QueryFirstOrDefaultAsync<int>($@"
                                    SELECT COUNT(*) 
                                    FROM INFORMATION_SCHEMA.COLUMNS 
                                    WHERE TABLE_NAME = '{logTable}'
                                    AND COLUMN_NAME IN ('Primary_Key_Field_2_Value', 'Type_of_Change', 'Table_Caption', 'Entry_No')");

                                if (columnsExist < 4)
                                {
                                    errors.Add($"Required columns missing in {logTable}");
                                    continue;
                                }

                                // Get the last processed entry for this company from our dictionary
                                var lastProcessedEntry = companyLastEntries.GetValueOrDefault(companyName, 0);

                                // First get the maximum Entry_No from the log table
                                var maxEntryNo = await connection.QueryFirstOrDefaultAsync<int>($@"
                                    SELECT MAX(Entry_No)
                                    FROM {logTable}");

                                var gettingTableCaptionSql = $@"
                                     WITH GroupedKeys AS (
                                         SELECT 
                                             Primary_Key_Field_2_Value,
                                             Table_Caption,
                                             COUNT(*) as TotalCount,
                                             SUM(CASE WHEN Type_of_Change = 'Deletion' THEN 1 ELSE 0 END) as DeletionCount
                                         FROM {logTable}
                                         WHERE Entry_No > {lastProcessedEntry}
                                         GROUP BY Primary_Key_Field_2_Value, Table_Caption
                                         HAVING COUNT(*) = SUM(CASE WHEN Type_of_Change = 'Deletion' THEN 1 ELSE 0 END)
                                             AND COUNT(*) > 1
                                     )
                                     SELECT DISTINCT Table_Caption
                                     FROM GroupedKeys
                                     ORDER BY Table_Caption";

                                var tableCaptions = await connection.QueryAsync<string>(gettingTableCaptionSql);

                                if (tableCaptions.Any())
                                {
                                    foreach (var tableCaption in tableCaptions)
                                    {
                                        processedTables.Add($"{tableCaption} (Entry #{maxEntryNo})");

                                        await _dbConnection.ExecuteAsync(@"
                                            INSERT INTO BCProcessingLog (TableName, Company, ChangeType, LastProcessEntry, AddedDate)
                                            VALUES (@TableName, @Company, @ChangeType, @LastProcessEntry, @AddedDate)",
                                            new
                                            {
                                                TableName = tableCaption,
                                                Company = companyName,
                                                ChangeType = "Deletion",
                                                LastProcessEntry = maxEntryNo.ToString(),
                                                AddedDate = DateTime.Now
                                            });
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                errors.Add($"Error processing {logTable}: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Error processing table {logTable}: {ex.Message}");
                    }
                }

                var message = new List<string>();
                if (processedTables.Any())
                {
                    message.Add($"Processed tables: {string.Join(", ", processedTables.Distinct())}");
                }
                if (errors.Any())
                {
                    message.Add($"Errors encountered: {string.Join(" | ", errors)}");
                }

                return new ResponseMessage
                {
                    IsError = errors.Any(),
                    Message = string.Join("\n", message)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ProcessSqlDelete");
                return new ResponseMessage { IsError = true, Message = ex.Message };
            }
        }

        public async Task<ResponseMessage> GetDestinationPrimaryKey(string destinationTable)
        {
            try
            {
                var sql = @"
                    SELECT DestinationPrimaryKey 
                    FROM ConnectionIntegrations 
                    WHERE DestinationTable = @DestinationTable";

                var primaryKey = await _dbConnection.QueryFirstOrDefaultAsync<string>(sql, new { DestinationTable = destinationTable });
                
                return new ResponseMessage 
                { 
                    IsError = false, 
                    Message = primaryKey ?? string.Empty 
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching destination primary key");
                return new ResponseMessage 
                { 
                    IsError = true, 
                    Message = "Error fetching destination primary key" 
                };
            }
        }
    }
}