<!-- app.component.html -->
<app-header (chatToggle)="toggleChat()"></app-header>

<div class="cards">
  <ng-container *ngIf="isChatOpen; else collapsedSplitter">
    <p-splitter [panelSizes]="[80, 20]" [minSizes]="[60, 16]">
      <!-- First panel with a minimum width of 800px -->
      <ng-template pTemplate>
        <div class="app-container m-2"
             [ngClass]="{'d-flex': isChatOpen}" >
          <router-outlet class="router-outlet"
              [ngClass]="{'flex1': isChatOpen}">
          </router-outlet>
        </div>
      </ng-template>

      <!-- Second panel with a minimum width of 300px for chat -->
      <ng-template pTemplate>
        <div class="h-100 w-100 pe-2">
          <div class="chat-section" [class.open]="isChatOpen">
            <app-chat [initialMessage]="messageToChat"></app-chat>
          </div>
        </div>
      </ng-template>
    </p-splitter>
  </ng-container>

  <ng-template #collapsedSplitter>
    <div class="app-container ">
      <router-outlet class="router-outlet"></router-outlet>
    </div>
  </ng-template>
</div>
<p-toast position="bottom-right" key="br" />
