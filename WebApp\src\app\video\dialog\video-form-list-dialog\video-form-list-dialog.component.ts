import { Component, Inject } from '@angular/core';
import {
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-video-form-list-dialog',
  standalone: true,
  imports: [MatFormFieldModule, MatInputModule, FormsModule, MatButtonModule],
  templateUrl: './video-form-list-dialog.component.html',
  styleUrl: './video-form-list-dialog.component.css'
})
export class VideoFormListDialogComponent {
  video = {
    videoLink: '',
    description: '',
    category: '',
    subCategory: ''
  };

  constructor(private dialogRef: MatDialogRef<VideoFormListDialogComponent>) { }

  onSubmit(form: any) {
    if (form.valid) {
      // Pass the video data back to the parent component
      console.log(form)
      this.dialogRef.close(this.video);
    }
  }

  onClose() {
    this.dialogRef.close();
  }

}
