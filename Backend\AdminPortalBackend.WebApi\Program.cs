using Serilog;
using Microsoft.IdentityModel.Tokens;
using AdminPortalBackend.Core;
using AdminPortalBackend.Infrastructure;
using Hangfire;
using AdminPortalBackend.Infrastructure.SignalRHub;
using AdminPortalBackend.WebApi;

try
{
    string DefaultCorsPolicyName = "DefaultCorsPolicyName";
    var builder = WebApplication.CreateBuilder(args);

    //Add services to the container.
    builder.Services.AddSerilog(options =>
    {
        options.ReadFrom.Configuration(builder.Configuration);
    });

    builder.Configuration.AddJsonFile("appsettings.json");

    builder.Services.AddCoreServices();
    builder.Services.AddInfrastructureServices(builder.Configuration);

    var corsOrigins = builder.Configuration.GetValue<string>("CorsOrigins")?.Split(',');
    builder.Services.AddCors(options =>
    {
        options.AddPolicy(DefaultCorsPolicyName, builder =>
        {
            builder.WithOrigins(corsOrigins)
                .AllowCredentials()
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
    });

    builder.Services.AddControllers();
    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen();

    builder.Services.AddHttpContextAccessor();


    var app = builder.Build();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }

    app.UseHttpsRedirection();

    app.UseAuthentication();

    app.UseAuthorization();

    app.MapHub<MessageHub>("/messageHub");

    app.UseHangfireDashboard(options: new DashboardOptions { Authorization = new[] { new HangFireAuthFilter() }});

    app.MapControllers();

    app.UseCors(DefaultCorsPolicyName); //Enable CORS!

    app.Run();
}
catch (Exception ex)
{
    if (ex.GetType().Name == "StopTheHostException")
    {
        throw;
    }
    Log.Fatal(ex, "Unhandled exception");
}
finally
{
    Log.Information("Shut down complete");
    Log.CloseAndFlush();
}
public static class StringExtenstion
{
    public static string RemovePostFix(this string str, params string[] postFixes)
    {
        if (str == null)
        {
            return null;
        }

        if (string.IsNullOrEmpty(str))
        {
            return string.Empty;
        }

        if (postFixes.IsNullOrEmpty())
        {
            return str;
        }

        foreach (string text in postFixes)
        {
            if (str.EndsWith(text))
            {
                return str.Left(str.Length - text.Length);
            }
        }

        return str;
    }

    public static string Left(this string str, int len)
    {
        if (str == null)
        {
            throw new ArgumentNullException("str");
        }

        if (str.Length < len)
        {
            throw new ArgumentException("len argument can not be bigger than given string's length!");
        }

        return str.Substring(0, len);
    }
}