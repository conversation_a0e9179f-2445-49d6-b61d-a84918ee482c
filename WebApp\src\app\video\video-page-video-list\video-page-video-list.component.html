<div class="video-header">
  <h1 class="page-heading">Video List</h1>
  <button class="add-button" (click)="AddVideoDialog()">Add+</button>
</div>

<div class="card">
  <p-table [value]="videos" [paginator]="true" [rows]="5" [first]="first" [showCurrentPageReport]="true"
    [tableStyle]="{ 'min-width': '50rem' }"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" (onPage)="pageChange($event)"
    [rowsPerPageOptions]="[5, 10, 25]">
    <ng-template pTemplate="header">
      <tr>
        <th>Thumbnail</th>
        <th>Category</th>
        <th>Sub Category</th>
        <th>Description</th>
        <th>Watch</th>
        <th>Action</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-video>
      <tr>
        <td>
          <img [src]="video.thumbImageLink || 'default-thumbnail.jpg'" alt="{{ video.description }} Thumbnail"
            width="100">
        </td>
        <td>{{ video.category }}</td>
        <td>{{ video.subCategory }}</td>
        <td> {{ video.description | slice:0:25 }}<span *ngIf="video.description.length > 25">...</span></td>
        <td><a [href]="video.videoLink" target="_blank">Watch Video</a></td>
        <td>Edit Delete</td>
      </tr>
    </ng-template>

    <ng-template pTemplate="paginatorleft">
      <p-button type="button" icon="pi pi-plus" styleClass="p-button-text" />
    </ng-template>
    <ng-template pTemplate="paginatorright">
      <p-button type="button" icon="pi pi-cloud" styleClass="p-button-text" />
    </ng-template>
  </p-table>
</div>
