﻿using AdminPortalBackend.Core.Contracts;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.SignalRHub;
using DocumentFormat.OpenXml.Office2016.Drawing.Command;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Text.Json;

namespace AdminPortalBackend.Infrastructure.DataStore;

public class BCWebServiceDataStore(HttpClient httpClient,
                                   ODataService oDataService,
                                   string endpointUrl,
                                   string companyId,
                                   string token,
                                   ILogger<DataStoreFactory> _logger,
                                   IHubContext<MessageHub, IMessageClient> messageHub,
                                   IntegrationInfo integrationInfo) : IDataStore
{
    private Dictionary<string, object> storeConfig = [];
    private int totalCount = 0;

    public object GetConfig(string key)
    {
        return storeConfig.GetValueOrDefault(key);
    }

    public async Task<List<Dictionary<string, object>>> RetrieveDataAsync(string entity)
    {
        httpClient.SetAuthorizationHeader(token);

        var allData = new List<Dictionary<string, object>>();

        var message = $"{integrationInfo.IntegrationId}~({entity}) Retrieving Connection Data from Entity {entity}";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            //httpClient.Timeout = TimeSpan.FromSeconds(480);
            string nextLink = storeConfig.GetValueOrDefault("NextLink") as string;
            string resourceUrl = string.Empty;
            if (!string.IsNullOrEmpty(nextLink))
            {
                resourceUrl = nextLink;
            }
            else
            {

                resourceUrl = $"{endpointUrl}Company('{companyId}')/{entity}";
                storeConfig.TryGetValue("Filters", out var filters);
                if (filters != null && filters is List<FilterCondition> filterList)
                {
                    var filterQuery = BuildODataFilter(filterList);
                    if (!string.IsNullOrEmpty(filterQuery))
                    {
                        resourceUrl += "?&$filter=" + filterQuery;
                    }
                }
            }

            while (!String.IsNullOrEmpty(resourceUrl))
            {
                // Make the request to the API
                httpClient.DefaultRequestHeaders.Add("Prefer", "odata.maxpagesize=4000");
                var response = await httpClient.GetAsync(resourceUrl);
                response.EnsureSuccessStatusCode(); // Throw if not a success code

                var responseData = await response.Content.ReadAsStringAsync();
                var apiData = JsonSerializer.Deserialize<ODataApiResponse>(responseData);

                resourceUrl = apiData.NextLink;
                this.SetConfig("NextLink", apiData.NextLink);

                // Transform the data to match the desired output format
                var transformedData = apiData.value.Select(row =>
                    row.ToDictionary(
                        kvp => char.ToUpper(kvp.Key[0]) + kvp.Key.Substring(1),
                        kvp => kvp.Value
                    )
                ).ToList();

                // Add the transformed data to the result list
                allData.AddRange(transformedData);

                message = $"{integrationInfo.IntegrationId}~({entity}) Retrieved {transformedData.Count} data. Total {(totalCount + allData.Count)}";
                await messageHub.Clients.All.SendMessage(message);
                _logger.LogInformation(message);

                if (allData.Count >= 50000) { break; }
            }

            totalCount += allData.Count;
            if (string.IsNullOrEmpty(resourceUrl))
            {
                message = $"{integrationInfo.IntegrationId}~({entity}) Retrieved Total {totalCount} Data from Entity {entity}";
                await messageHub.Clients.All.SendMessage(message);
                _logger.LogInformation(message);
            }
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({entity}) Error: Retrieving Connection Data";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
        }

        _logger.LogInformation("Received WebService Data");
        return allData;
    }

    public async Task<List<Dictionary<string, object>>> SaveDataAsync(List<Dictionary<string, object>> data, string entity, string sourceTable, string primaryKeys)
    {
        var message = $"{integrationInfo.IntegrationId}~({entity}) Saving data to WebService entity";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            var updatedProperties = data.FirstOrDefault();
            updatedProperties.Remove("SystemRowVersion");

            var postUrl = $"{endpointUrl}Company('{companyId}')/{entity}";
            var createdData = await oDataService.PostDataAsync(postUrl, updatedProperties, token);

            message = $"{integrationInfo.IntegrationId}~({entity}) Successfully saved data to WebService entity";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogInformation(message);
            return [createdData];
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({entity}) Error saving data to WebService entity";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
            throw;
        }
    }

    public void SetConfig(string key, object value)
    {
        storeConfig[key] = value;
    }

    public async Task<Dictionary<string, object>> UpdateDataAsync(Dictionary<string, object> updatedProperties, string entity, Dictionary<string, object> primaryKeyValues)
    {
        var message = $"{integrationInfo.IntegrationId}~({entity}) Updating data in WebService entity";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            string odataKeyFilter = string.Join(",", primaryKeyValues.Select(kv =>
                                        kv.Value is int || kv.Value is long || kv.Value is short || kv.Value is byte
                                            ? $"{kv.Key}={kv.Value}"
                                            : $"{kv.Key}='{kv.Value}'"
                                        ));

            var patchUrl = $"{endpointUrl}Company('{companyId}')/{entity}({odataKeyFilter})";
            var updatedRecord = await oDataService.PatchDataAsync(patchUrl, updatedProperties, token);

            message = $"{integrationInfo.IntegrationId}~({entity}) Successfully updated data in WebService entity";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogInformation(message);
            return updatedRecord;
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({entity}) Error updating data in WebService entity";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
            throw;
        }
    }

    private string BuildODataFilter(List<FilterCondition> filter)
    {
        storeConfig.TryGetValue("PropertiesMetadata", out var propsMetadata);
        var propsMetadataList = propsMetadata as List<BCEntityMetadata>;

        var filterStrings = filter.Select(f =>
        {
            var metadata = propsMetadataList?.FirstOrDefault(m => m.PropertyName.Equals(f.Field, StringComparison.OrdinalIgnoreCase));

            // If metadata is null, fallback to general logic
            if (metadata == null)
            {
                DateTime dateValue;
                if (DateTime.TryParse(f.Value, out dateValue))
                {
                    var formattedDate = dateValue.ToString("yyyy-MM-ddTHH:mm:ssZ");
                    return $"{Uri.EscapeDataString(f.Field)} {f.Operator} {Uri.EscapeDataString(formattedDate)}";
                }

                if (int.TryParse(f.Value, out int intValue))
                {
                    return $"{Uri.EscapeDataString(f.Field)} {f.Operator} {f.Value}";
                }

                if (long.TryParse(f.Value, out long longValue))
                {
                    return $"{Uri.EscapeDataString(f.Field)} {f.Operator} {f.Value}";
                }

                return $"{Uri.EscapeDataString(f.Field)} {f.Operator} '{Uri.EscapeDataString(f.Value)}'";
            }

            // Use the switch statement for metadata-based property types
            return metadata.PropertyType switch
            {
                "Edm.Date" or "Edm.DateTimeOffset" =>
                    DateTime.TryParse(f.Value, out var dateValue)
                        ? $"{Uri.EscapeDataString(f.Field)} {f.Operator} {Uri.EscapeDataString(dateValue.ToString("yyyy-MM-ddTHH:mm:ssZ"))}"
                        : $"{Uri.EscapeDataString(f.Field)} {f.Operator} '{Uri.EscapeDataString(f.Value)}'",

                "Edm.Int32" or "Edm.Int64" or "Edm.Decimal" or "Edm.Boolean" =>
                    $"{Uri.EscapeDataString(f.Field)} {f.Operator} {f.Value.ToLower()}",

                // Edm.String, Edm.Guid, and default case here
                _ =>
                    $"{Uri.EscapeDataString(f.Field)} {f.Operator} '{Uri.EscapeDataString(f.Value)}'"
            };
        });

        if(filter.Count > 1 && filter.All(f => f.Field == filter.First().Field)) return string.Join(" or ", filterStrings);

        return string.Join(" and ", filterStrings);
    }


    //private string BuildFilters(Dictionary<string, string> filters)
    //{
    //    var filterQuery = string.Join(
    //                "&",
    //                filters.Select(f =>
    //                {
    //                    // Try to parse the value to a DateTime
    //                    DateTime dateValue;
    //                    if (DateTime.TryParse(f.Value, out dateValue))
    //                    {
    //                        // Format the date value for OData (ISO 8601)
    //                        var formattedDate = dateValue.ToString("yyyy-MM-ddTHH:mm:ssZ");
    //                        return $"{Uri.EscapeDataString(f.Key)} gt {Uri.EscapeDataString(formattedDate)}"; // You can adjust the operator here as needed
    //                    }
    //                    else if(f.Key == "SystemRowVersion")
    //                    {
    //                        return $"{Uri.EscapeDataString(f.Key)} gt {Uri.EscapeDataString(f.Value)}";
    //                    }
    //                    else
    //                    {
    //                        // If it's not a date, treat it as a string and apply the 'eq' operator
    //                        return $"{Uri.EscapeDataString(f.Key)} eq {Uri.EscapeDataString(f.Value)}";
    //                    }
    //                })
    //            );

    //    return filterQuery;
    //}
}
