﻿using AdminPortalBackend.Core.Contracts;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Extensions;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.Repositories;
using AdminPortalBackend.Infrastructure.SignalRHub;
using Amazon.Runtime.Internal.Endpoints.StandardLibrary;
using Dapper;
using Hangfire;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.VisualBasic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Text.Json;
using UglyToad.PdfPig.DocumentLayoutAnalysis;

namespace AdminPortalBackend.Infrastructure.DataStore;

public class SyncManager(DataStoreFactory dataStoreFactory,
                         IDbConnection _dbConnection,
                         ODataService oDataService,
                         IDbConnectionRepository dbConnectionRepository,
                         IConfiguration configuration, ILogger<SyncManager> logger, IHubContext<MessageHub, IMessageClient> messageHub)
{
    [AutomaticRetry(Attempts = 0)]
    [MaximumConcurrentExecutions(1, timeoutInSeconds: 3600, pollingIntervalInSeconds: 30)]
    public async Task SyncDataFromSourceToTarget(Guid integrationGuid, CancellationToken cancellationToken)
    {
        long maxTimestamp = 0;
        int totlaRows = 0;

        using (logger.BeginScope(new Dictionary<string, object> { { "IntegrationId", integrationGuid } }))
        {
            int logId = dbConnectionRepository.InsertJobLog(integrationGuid.ToString());
            try
            {
                long lastMaxTimestamp = dbConnectionRepository.GetLastModifiedTimestamp(integrationGuid.ToString());
                var lastMaxPropertyName = "SystemRowVersion";

                var sql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";
                var integration = _dbConnection.QuerySingleOrDefaultAsync<ConnectionIntegration>(sql, new { Guid = integrationGuid }).Result;
                if (integration == null)
                {
                    logger.LogWarning("Integration {integrationGuid} is Null", integrationGuid);
                    return;
                }
                var tableName = string.IsNullOrEmpty(integration.SourceTable) ? [" "] : integration.SourceTable.Split('_');
                string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];
                var message = $"{integration.Guid}~({integration.SourceTable}) Successfully Fetched Integration info of {integration.IntegrationName}";
                await messageHub.Clients.All.SendMessage(message);
                logger.LogInformation(message);

                var sourceDataStoreFactory = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetSourceConnectionIntegration()));
                if (integration.SourceTable.Contains("ChangeLogEntry"))
                {
                    lastMaxPropertyName = "Entry_No";
                    sourceDataStoreFactory.SetChangeLogEntryFilterValue(lastMaxTimestamp);
                }
                else
                {
                    sourceDataStoreFactory.SetDifferentialLoadFilterValue();
                }
                var sourceDataStore = sourceDataStoreFactory.SetDefaultIntegrationDataLoadType()
                    .Build();

                int batchNumber = 1;
                var targetDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetDestinationConnectionIntegration()))
                    .SetDefaultIntegrationDataLoadType()
                    .Build();

                var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);
                string nextLink = "";
                var statusMessage = "Completed";
                do
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        statusMessage = "Cancelled";
                        var message2 = $"{integrationGuid}~Cancelled: Sync Cancelled";
                        await messageHub.Clients.All.SendMessage(message2);
                        logger.LogInformation(message2);
                        break;
                    }
                    targetDataStore.SetConfig("BatchNumber", batchNumber.ToString());

                    var data = sourceDataStore.RetrieveDataAsync(integration.SourceTable).Result;

                    var transformedData = TransformData(data, mappedColumns);

                    nextLink = sourceDataStore.GetConfig("NextLink") as string;
                    var hasMoredata = !String.IsNullOrEmpty(nextLink) ? "Yes" : "No";
                    targetDataStore.SetConfig("HasMoreData", hasMoredata);

                    targetDataStore.SaveDataAsync(transformedData, integration.DestinationTable, integration.SourceTable, integration.DestinationPrimaryKey).Wait();
                    batchNumber++;

                    var timeStamp = transformedData
                                            .Where(d => d.ContainsKey(lastMaxPropertyName))
                                            .Select(d => (long)ConvertToType(d[lastMaxPropertyName], typeof(long)))
                                            .DefaultIfEmpty(lastMaxTimestamp)
                                            .Max();
                    totlaRows += transformedData.Count();

                    if (timeStamp > maxTimestamp)
                    {
                        maxTimestamp = timeStamp;
                    }
                } while (!String.IsNullOrEmpty(nextLink));

                dbConnectionRepository.UpdateJobLog(logId, totlaRows, maxTimestamp, statusMessage);

                //for real-time processing
                if (integration.SourceTable.Contains("ChangeLogEntry"))
                {
                    await AddUpdatesToNavToSqlQueue(integration.DestinationTable);
                }
            }
            catch (Exception ex)
            {
                var message = $"{integrationGuid}~Error: Sync Failed";
                await messageHub.Clients.All.SendMessage(message);
                logger.LogError(ex, message);
                dbConnectionRepository.UpdateJobLog(logId, 0, 0, "Error");
            }
        }
    }

    public async Task AddUpdatesToNavToSqlQueue(string logTableName)
    {
        try
        {
            using var connection = new SqlConnection(configuration.GetConnectionString("AdminConnection"));
            await connection.OpenAsync();

            // Dynamically create the stored procedure name by appending "_UpdateQueue" to the log table name
            string procedureName = $"dbo.{logTableName}_UpdateQueue";

            // Execute the stored procedure
            await connection.ExecuteAsync(procedureName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "AddUpdatesToNavToSqlQueue failed");
        }
    }

    public async Task<List<Dictionary<string, object>>> RetrieveData(ConnectionIntegration integration, List<FilterCondition> filterConditions)
    {
        var sourceDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetSourceConnectionIntegration())).Build();
        sourceDataStore.SetConfig("Filters", filterConditions);

        var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);

        var data = sourceDataStore.RetrieveDataAsync(integration.SourceTable).Result;

        var transformedData = TransformData(data, mappedColumns);

        return transformedData;
    }

    [AutomaticRetry(Attempts = 0)]
    public async Task ProcessSQLToBCUpdate(string destiantionConnectionId, string database)
    {
        using (logger.BeginScope(new Dictionary<string, object> { { "ConnectionId", destiantionConnectionId } }))
        {
            logger.LogInformation("Sending data SQL to BC");
            var dbConnection = await dbConnectionRepository.GetConnection(Guid.Parse(destiantionConnectionId));

            var connectionCreds = JsonSerializer.Deserialize<MssqlServerCredDto>(dbConnection.ConnectionCredJson);
            var connectionString = $"Server={connectionCreds.ServerName};" +
                    $"Database={database};" +
                    $"User Id={connectionCreds.UserId};" +
                    $"Password={connectionCreds.Password};" +
                    $"TrustServerCertificate={(connectionCreds.TrustedCertificate ? "yes" : "no")};";

            using var connection = new SqlConnection(connectionString);
            connection.Open();

            // Update status of all records with status 'New' to 'Processing'
            var updateProcessingQuery = "UPDATE ChangeTracking SET Status = 'Processing' WHERE Status = 'New'";
            connection.Execute(updateProcessingQuery);

            // Query to select records now with status 'Processing'
            var query = "SELECT * FROM ChangeTracking WHERE Status = 'Processing' ORDER BY ChangeID";
            var records = connection.Query<ChangeTrackingRecord>(query).AsList();

            foreach (var record in records)
            {
                try
                {
                    var sql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";
                    var integration = await _dbConnection.QuerySingleOrDefaultAsync<ConnectionIntegration>(sql, new { Guid = record.IntegrationId });
                    if (integration == null)
                    {
                        continue;
                    }
                    var sourceConnection = await dbConnectionRepository.GetODataConnectionDetailAsync(integration.SourceConnectionGuid);
                    if (sourceConnection == null)
                    {
                        continue;
                    }

                    var changeData = record.ChangeData != null ? JsonSerializer.Deserialize<Dictionary<string, object>>(record.ChangeData) : null;
                    var oldData = record.OldData != null ? JsonSerializer.Deserialize<Dictionary<string, object>>(record.OldData) : null;

                    Dictionary<string, string> propertyMetadata = await GetPropertyMetadata(_dbConnection, integration);

                    var updatedProperties = FindUpdatedProperties(changeData, oldData, propertyMetadata);

                    await ProcessRecord(record.Operation, integration, updatedProperties);

                    var message = $"Successfully processed {record.Operation} operation";
                    logger.LogInformation(message);

                    // Update status to 'Processed'
                    connection.Execute("UPDATE ChangeTracking SET Status = 'Processed' WHERE ChangeId = @ChangeId", new { ChangeId = record.ChangeID });
                }
                catch (Exception ex)
                {
                    logger.LogError("Error: " + ex.Message);
                    connection.Execute("UPDATE ChangeTracking SET Status = 'Error' WHERE ChangeId = @ChangeId", new { ChangeId = record.ChangeID });
                }
            }
        }
    }

    private static async Task<Dictionary<string, string>> GetPropertyMetadata(IDbConnection _dbConnection, ConnectionIntegration integration)
    {
        var metadataQuery = "SELECT [EntityName], [PropertyName], [PropertyType] FROM BCEntityMetadata Where EntityName = @TableName AND ConnectionId = @ConnectionId";
        var propertyMetadata = (await _dbConnection.QueryAsync<BCEntityMetadata>(metadataQuery,
                new { TableName = integration.SourceTable, ConnectionId = integration.SourceConnectionGuid }))
                .ToDictionary(m => m.PropertyName, m => m.PropertyType);
        return propertyMetadata;
    }

    public async Task<Dictionary<string, object>> ProcessRecord(string action, ConnectionIntegration integration, Dictionary<string, object> data)
    {
        string companyId = await GetCompanyId(integration);
        switch (action)
        {
            case "INSERT":
                var sourceDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetSourceConnectionIntegration())).Build();
                var createdData = await sourceDataStore.SaveDataAsync([data], integration.SourceTable, integration.SourceTable, integration.SourcePrimaryKey);
                //process created record
                var targetDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetDestinationConnectionIntegration())).Build();
                var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);
                var transformedData = TransformData(createdData, mappedColumns).FirstOrDefault();
                await targetDataStore.SaveDataAsync([transformedData], integration.DestinationTable, integration.SourceTable, integration.DestinationPrimaryKey);

                return transformedData;

            case "UPDATE":
                Dictionary<string, string> propertyMetadata = await GetPropertyMetadata(_dbConnection, integration);

                var primaryKeys = integration.SourcePrimaryKey.Split("@#");
                Dictionary<string, object> primaryKeyValues = data.Where(kv => primaryKeys.Contains(kv.Key))
                    .ToDictionary(kv => kv.Key, kv => ConvertToType(kv.Value, edmTypeToDotNetType[propertyMetadata[kv.Key]]));

                var updateSourceDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetSourceConnectionIntegration())).Build();
                var updatedRecord = await updateSourceDataStore.UpdateDataAsync(data, integration.SourceTable, primaryKeyValues);

                //process updated record
                var targetDataStoreUpdate = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetDestinationConnectionIntegration())).Build();
                var mappedColumnsUpdate = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);
                var transformedDataUpdate = TransformData([updatedRecord], mappedColumnsUpdate).FirstOrDefault();
                await targetDataStoreUpdate.SaveDataAsync([transformedDataUpdate], integration.DestinationTable, integration.SourceTable, integration.DestinationPrimaryKey);

                return updatedRecord;

            case "DELETE":
                //string deleteSystemId = oldData["id"]?.ToString();
                //var deleteUrl = $"{sourceConnection.ConnectionCreds.EndpointUrl}companies({companyId})/{integration.SourceTable}({deleteSystemId})";
                //oDataService.DeleteDataAsync(deleteUrl, "", accessToken).Wait();

                return data;

            default:
                return null;
        }
    }

    private async Task<string> GetCompanyId(ConnectionIntegration integration)
    {
        var message = $"{integration.Guid}~({integration.SourceTable}) Getting company Id";
        await messageHub.Clients.All.SendMessage(message);
        logger.LogInformation(message);

        try
        {
            using var connection = new SqlConnection(configuration.GetConnectionString("AdminConnection"));
            connection.Open();
            var companySql = $"Select [Id] from BCCompanies Where ConnectionId='{integration.SourceConnectionGuid}' AND [Name]='{integration.SourceDatabase}'";
            var companyId = await connection.QuerySingleOrDefaultAsync<string>(companySql);
            message = $"{integration.Guid}~({integration.SourceTable}) Successfully retrieved company Id";
            await messageHub.Clients.All.SendMessage(message);
            logger.LogInformation(message);
            return companyId;
        }
        catch (Exception ex)
        {
            message = $"{integration.Guid}~({integration.SourceTable}) Error getting company ID";
            await messageHub.Clients.All.SendMessage(message);
            logger.LogError(ex, message);
            throw;
        }
    }

    [AutomaticRetry(Attempts = 0)]
    public async Task ProcessBCToSqlUpdate()
    {
        using (logger.BeginScope(new Dictionary<string, object> { { "Name", "BC to sql" } }))
        {
            try
            {
                using var connection = new SqlConnection(configuration.GetConnectionString("AdminConnection"));
                connection.Open();

                // Update status of all records with status 'New' to 'Processing'
                var updateProcessingQuery = "UPDATE BCWebhookNotifications SET Status = 'Processing' WHERE Status = 'New'";
                connection.Execute(updateProcessingQuery);

                // Query to select records now with status 'Processing'
                var query = "SELECT * FROM BCWebhookNotifications WHERE Status = 'Processing'";
                var records = connection.Query<BCWebhookNotification>(query).AsList();

                foreach (var record in records)
                {
                    try
                    {
                        logger.LogInformation("Getting subscriptionId");
                        string sql = $"SELECT * FROM [BCSubscriptions] WHERE SubscriptionId = '{record.SubscriptionId}'";
                        var subscription = connection.QuerySingleOrDefault<SubscriptionResponse>(sql);

                        if (subscription != null)
                        {
                            var connectionDetails = await dbConnectionRepository.GetODataConnectionDetailAsync(Guid.Parse(subscription.ConnectionId));
                            logger.LogInformation($"Saving {record.ChangeType} Data in Sql");
                            var integrationSql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @IntegrationId";
                            var integration = await connection.QuerySingleOrDefaultAsync<ConnectionIntegration>(integrationSql, new { IntegrationId = subscription.IntegrationId });

                            switch (record.ChangeType)
                            {
                                case "created":
                                case "updated":
                                case "collection":
                                    var token = await oDataService.GetAccessTokenAsync(Guid.Parse(subscription.ConnectionId));
                                    List<Dictionary<string, object>> data = new();

                                    if (record.ChangeType == "collection")
                                    {
                                        data = await oDataService.PullDataFromOData(record.Resource, token);
                                    }
                                    else
                                    {
                                        var resourceData = await oDataService.GetBCSingleRecord(record.Resource, token);
                                        data = [resourceData];
                                    }

                                    var targetDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integration.GetDestinationConnectionIntegration())).Build();

                                    var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);

                                    var transformedData = TransformData(data, mappedColumns);

                                    await targetDataStore.SaveDataAsync(transformedData, integration.DestinationTable, integration.SourceTable, integration.DestinationPrimaryKey);

                                    break;

                                case "deleted":
                                    var deleteConnectionInfo = await dbConnectionRepository.GetConnection(integration.DestinationConnectionGuid);
                                    var connectionCreds = JsonSerializer.Deserialize<MssqlServerCredDto>(deleteConnectionInfo.ConnectionCredJson);
                                    var deleteConnectionString = $"Server={connectionCreds.ServerName};" +
                                            (integration.DestinationDatabase != null ? $"Database={integration.DestinationDatabase};" : "") +
                                            $"User Id={connectionCreds.UserId};" +
                                            $"Password={connectionCreds.Password};" +
                                            $"TrustServerCertificate={(connectionCreds.TrustedCertificate ? "yes" : "no")};";
                                    using (var deleteConnection = new SqlConnection(deleteConnectionString))
                                    {
                                        var recordId = record.Resource.Split('/').Last().Split('(').Last().Split(')').First();
                                        var deleteSql = $"DELETE FROM {integration.DestinationTable} WHERE id = '{recordId}'";
                                        deleteConnection.Open();

                                        _ = await deleteConnection.ExecuteAsync(deleteSql);
                                    }
                                    break;

                                //case "collection":
                                //    var token = await oDataService.GetAccessTokenAsync(Guid.Parse(subscription.ConnectionId));
                                //    var resourceData = await oDataService.PullDataFromOData(record.Resource, token);
                                //    List<Dictionary<string, object>> data = new() { resourceData };

                                //    var targetDataStore = dataStoreFactory.GetDataStore(integration.GetDestinationConnectionIntegration());

                                //    var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);

                                //    var transformedData = TransformData(data, mappedColumns);

                                //    await targetDataStore.SaveDataAsync(transformedData, integration.DestinationTable, integration.DestinationPrimaryKey);
                                //    break;

                                default:
                                    break;
                            }
                            logger.LogInformation($"{record.ChangeType} Record to sql successfully");
                            // Update record status to 'Processed' after successful processing
                            var updateProcessedQuery = "UPDATE BCWebhookNotifications SET Status = 'Processed' WHERE Id = @Id";
                            await connection.ExecuteAsync(updateProcessedQuery, new { Id = record.Id });
                        }
                        else
                        {
                            // If no subscription is found, update the record status to 'Error'
                            var updateErrorQuery = "UPDATE BCWebhookNotifications SET Status = 'Error' WHERE Id = @Id";
                            await connection.ExecuteAsync(updateErrorQuery, new { Id = record.Id });
                            logger.LogInformation("Updated all records to Error in BCWebHookNotification");

                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, $"Error processing record: {ex.Message}");
                        // Update record status to 'Error' if an exception occurs
                        var updateErrorQuery = "UPDATE BCWebhookNotifications SET Status = 'Error' WHERE Id = @Id";
                        await connection.ExecuteAsync(updateErrorQuery, new { Id = record.Id });
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error in BC to SQL update: {ex.Message}");
                throw;
            }
        }
    }

    [AutomaticRetry(Attempts = 0)]
    public async Task ProcessNavToSqlUpdate()
    {
        using (logger.BeginScope(new Dictionary<string, object> { { "Name", "Nav to sql" } }))
        {
            try
            {
                using var connection = new SqlConnection(configuration.GetConnectionString("AdminConnection"));
                await connection.OpenAsync();

                // Query to select records now with status 'Processing'
                var query = "SELECT top 1000 * FROM NavToSqlUpdateQueue WHERE Status = 'New'";
                var records = (await connection.QueryAsync<NavToSqlUpdateQueue>(query)).ToList();

                if (records.Any())
                {
                    // Update status of all records with status 'New' to 'Processing'
                    var updateProcessingQuery = "UPDATE NavToSqlUpdateQueue SET Status = 'Processing' WHERE QueueId = @Id";

                    // Execute the update for each record individually
                    foreach (var record in records)
                    {
                        await connection.ExecuteAsync(updateProcessingQuery, new { Id = record.QueueId });
                    }
                }

                // Group records by DWServiceName
                var groupedRecords = records.GroupBy(r => new { r.DWServiceName, r.Company }).ToList();

                foreach (var group in groupedRecords)
                {
                    // Build the list of FilterCondition for each group
                    List<FilterCondition> filters = new List<FilterCondition>();
                    var queueIds = new List<int>(); // Collect QueueIds for the group
                    var deletedQueueIds = new List<int>(); // Collect QueueIds for the group

                    var recordsToDelete = group.Where(record => record.TypeofChange == "Deletion").ToList();
                    var otherRecords = group.Where(record => record.TypeofChange != "Deletion").ToList();
                    queueIds.AddRange(otherRecords.Select(record => record.QueueId));
                    deletedQueueIds.AddRange(recordsToDelete.Select(record => record.QueueId));

                    filters.AddRange(group.Select(record => new FilterCondition //pull all including deleted from sql side
                    {
                        Field = record.DWKeyFieldName,
                        Operator = "eq",
                        Value = record.PrimaryKeyFieldValue.ToString()
                    }));

                    List<FilterCondition> sqlFilter = new List<FilterCondition>();
                    if (group.Any())
                    {
                        sqlFilter.Add(new FilterCondition
                        {
                            Field = group.First().DWKeyFieldName,
                            Operator = "IN",
                            Value = string.Join(",", group.Select(g => $"{g.PrimaryKeyFieldValue}")) //pull all including deleted from sql side
                        });
                    }

                    List<FilterCondition> sqlDeleteFilter = new List<FilterCondition>();
                    if (recordsToDelete.Any())
                    {
                        sqlDeleteFilter.Add(new FilterCondition
                        {
                            Field = group.First().DWKeyFieldName,
                            Operator = "IN",
                            Value = string.Join(",", recordsToDelete.Select(g => $"{g.PrimaryKeyFieldValue}"))
                        });
                    }

                    try
                    {
                        // Pull the IntegrationId based on a condition (e.g., matching SourceDatabase)
                        var getIntegrationQuery = @"SELECT [Guid] FROM [ConnectionIntegrations] WHERE [SourceDatabase]=@SourceDatabase AND [SourceTable] = @DWServiceName";
                        var integration = await connection.QuerySingleOrDefaultAsync<Guid>(getIntegrationQuery, new
                        {
                            DWServiceName = group.Key.DWServiceName,
                            SourceDatabase = group.Key.Company
                        });

                        if (integration != Guid.Empty)
                        {
                            

                            // Proceed with the rest of your logic based on the IntegrationId
                            var integrationSql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @IntegrationId";
                            var integrationDetails = await connection.QuerySingleOrDefaultAsync<ConnectionIntegration>(integrationSql, new { IntegrationId = integration });

                            if (integrationDetails != null)
                            {
                                // Now update the NavToSqlUpdateQueue with the DWServiceName for the entire group
                                var updateSourceDatabaseDeletionQuery = "UPDATE NavToSqlUpdateQueue SET Status = 'Processed' WHERE QueueId IN @QueueIds";
                                await connection.ExecuteAsync(updateSourceDatabaseDeletionQuery, new { QueueIds = deletedQueueIds });
                                //process delete first
                                await DeleteSqlRecords(integrationDetails, sqlDeleteFilter);

                                const int MaxFiltersPerRequest = 100;
                                if (filters.Count > 0)
                                {
                                    List<List<FilterCondition>> filterChunks = ChunkFilters(filters, MaxFiltersPerRequest);

                                    foreach (var filterChunk in filterChunks)
                                    {
                                        // Retrieve data using the filters
                                        List<Dictionary<string, object>> transformedData = await RetrieveData(integrationDetails, filterChunk);

                                        var targetDataStore = (await dataStoreFactory.CreateDataStoreBuilder(integrationDetails.GetDestinationConnectionIntegration())).Build();

                                        //var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integrationDetails.MappedColumns);
                                        //var transformedData = TransformData(data, mappedColumns);

                                        await targetDataStore.SaveDataAsync(transformedData, integrationDetails.DestinationTable, integrationDetails.SourceTable, integrationDetails.DestinationPrimaryKey);

                                        logger.LogInformation($"{group.First().TypeofChange} records processed successfully to SQL");
                                    }
                                }
                                //process downstream integrations
                                var getDownstreamIntegrationQuery = @"SELECT * FROM [ConnectionIntegrations] WHERE [SourceDatabase]=@DestinationDatabase AND [SourceTable] = @DestinationTable";
                                var downStreamIntegrations = await connection.QueryAsync<ConnectionIntegration>(getDownstreamIntegrationQuery, new
                                {
                                    integrationDetails.DestinationDatabase,
                                    DestinationTable = ReMapDestinationTable(integrationDetails.DestinationTable)
                                });

                                foreach (var downIntegration in downStreamIntegrations)
                                {
                                    //process delete first
                                    await DeleteSqlRecords(downIntegration, sqlDeleteFilter);
                                    if (sqlFilter.Count > 0)
                                    {
                                        List<Dictionary<string, object>> sqlData = await RetrieveData(downIntegration, sqlFilter);

                                        var sqlTargetDataStore = (await dataStoreFactory.CreateDataStoreBuilder(downIntegration.GetDestinationConnectionIntegration())).Build();

                                        //var sqlMappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(downIntegration.MappedColumns);
                                        //var sqlTransformedData = TransformData(sqlData, sqlMappedColumns);

                                        await sqlTargetDataStore.SaveDataAsync(sqlData, downIntegration.DestinationTable, downIntegration.SourceTable, downIntegration.DestinationPrimaryKey);

                                        logger.LogInformation($"{sqlData.Count} records processed successfully to SQL");
                                    }
                                }

                                // Now update the NavToSqlUpdateQueue with the DWServiceName for the entire group
                                var updateSourceDatabaseQuery = "UPDATE NavToSqlUpdateQueue SET Status = 'Processed' WHERE QueueId IN @QueueIds";
                                await connection.ExecuteAsync(updateSourceDatabaseQuery, new { QueueIds = queueIds });
                            }
                            else
                            {
                                logger.LogError($"Integration not found for IntegrationId: {integration}");
                            }
                        }
                        else
                        {
                            logger.LogError("Integration not found for DWServiceName: " + group.Key.DWServiceName);
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, $"Error processing records for group {group.Key.DWServiceName}: {ex.Message}");

                        // Update record status to 'Error' for all records in the group if an exception occurs
                        var updateErrorQuery = "UPDATE NavToSqlUpdateQueue SET Status = 'Error' WHERE Status<>'Processed' AND QueueId IN @QueueIds";
                        await connection.ExecuteAsync(updateErrorQuery, new { QueueIds = group.Select(record => record.QueueId) });
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error in Nav to SQL update: {ex.Message}");
                throw;
            }
        }

        static string ReMapDestinationTable(string destinationTable)
        {
            return destinationTable switch
            {
                "DII_DWSalesPrice" => "DII$Sales Price",
                "DIIUK_DWSalesPrice" => "DIIUK$Sales Price",
                "DIIJAPAN_DWSalesPrice" => "DIIJAPAN$Sales Price",
                "DIIMX_DWSalesPrice" => "DIIMX$Sales Price",
                _ => destinationTable,
            };
        }
    }

    List<List<FilterCondition>> ChunkFilters(List<FilterCondition> filters, int maxFiltersPerRequest)
    {
        var chunks = new List<List<FilterCondition>>();
        for (int i = 0; i < filters.Count; i += maxFiltersPerRequest)
        {
            chunks.Add(filters.Skip(i).Take(maxFiltersPerRequest).ToList());
        }
        return chunks;
    }

    private async Task DeleteSqlRecords(ConnectionIntegration integration, List<FilterCondition> filters)
    {
        if (filters.Count == 0) return;

        var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);
        var filter = filters.FirstOrDefault(f => mappedColumns.ContainsKey(f.Field));

        if (filter != null)
        {
            var destinationColName = mappedColumns[filter.Field];
            //var deleteFilter = new FilterCondition { Field = destinationColName, Operator = filter.Operator, Value = filter.Value };

            try
            {
                var deleteConnectionInfo = await dbConnectionRepository.GetConnection(integration.DestinationConnectionGuid);
                var connectionCreds = JsonSerializer.Deserialize<MssqlServerCredDto>(deleteConnectionInfo.ConnectionCredJson);
                var deleteConnectionString = $"Server={connectionCreds.ServerName};" +
                        (integration.DestinationDatabase != null ? $"Database={integration.DestinationDatabase};" : "") +
                        $"User Id={connectionCreds.UserId};" +
                        $"Password={connectionCreds.Password};" +
                        $"TrustServerCertificate={(connectionCreds.TrustedCertificate ? "yes" : "no")};";

                using var deleteConnection = new SqlConnection(deleteConnectionString);

                var escapedValues = filter.Value.Split(',').Select(v => $"'{v.Trim().Replace("'", "''")}'").ToArray();
                var whereClause = $"{destinationColName} IN ({string.Join(", ", escapedValues)})"; ;
                if (!string.IsNullOrWhiteSpace(whereClause))
                {
                    var deleteSql = $"DELETE FROM [{integration.DestinationTable}] Where {whereClause}";
                    deleteConnection.Open();

                    _ = await deleteConnection.ExecuteAsync(deleteSql);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error in Nav to SQL update: {ex.Message}| {integration.DestinationDatabase}| {integration.DestinationTable}");
            }

        }
        else
        {
            logger.LogError($"Delete fieldname not found in Integration mapped column:{integration.DestinationDatabase} | {integration.DestinationTable}");
            return;
        }

        
    }

    //private string BuildSqlWhereClause(FilterCondition filter)
    //{
    //    var values = filter.Value.Split(',').Select(v => v.Trim()).ToArray();
    //    var escapedValues = values.Select(v => $"'{v.Replace("'", "''")}'").ToArray();
    //    return $"{filter.Field} IN ({string.Join(", ", escapedValues)})";
    //}


    // Mapping EDM types to .NET types
    Dictionary<string, Type> edmTypeToDotNetType = new Dictionary<string, Type>
                                    {
                                        { "Edm.String", typeof(string) },
                                        { "Edm.Int32", typeof(int) },
                                        { "Edm.Int64", typeof(long) },
                                        { "Edm.Decimal", typeof(decimal) },
                                        { "Edm.Boolean", typeof(bool) },
                                        { "Edm.DateTimeOffset", typeof(DateTimeOffset) },
                                        { "Edm.Date", typeof(DateTime) },
                                        { "Edm.Guid", typeof(Guid) },
                                        { "Edm.Stream", typeof(byte[]) } // Assuming stream is a byte array
                                    };

    public Dictionary<string, object> FindUpdatedProperties(Dictionary<string, object> changeData,
                                                            Dictionary<string, object> oldData,
                                                            Dictionary<string, string> propertyMetadata)
    {
        if (oldData == null)
        {
            return changeData;
        }

        if (changeData == null)
        {
            return oldData;
        }

        var updatedProperties = new Dictionary<string, object>();



        foreach (var kvp in changeData)
        {
            // Check if the key exists in oldData and if metadata exists for the property
            if (oldData.TryGetValue(kvp.Key, out var oldValue) && propertyMetadata.ContainsKey(kvp.Key))
            {
                string edmType = propertyMetadata[kvp.Key]; // EDM type as string
                if (!edmTypeToDotNetType.ContainsKey(edmType))
                {
                    logger.LogWarning("Unsupported EDM type: {edmType}", edmType);
                    continue;
                }

                Type propertyType = edmTypeToDotNetType[edmType];

                string newValue = kvp.Value.ToString();
                string oldValueString = oldValue.ToString();

                // Check if both values are null or empty
                bool isNewValueNullOrEmpty = string.IsNullOrEmpty(newValue);
                bool isOldValueNullOrEmpty = string.IsNullOrEmpty(oldValueString);

                // Compare based on type and handle nulls/empties
                if (isNewValueNullOrEmpty && isOldValueNullOrEmpty)
                {
                    continue; // Both are considered equal
                }
                else if (!isNewValueNullOrEmpty && !isOldValueNullOrEmpty)
                {
                    // Compare the values directly
                    if (newValue.ToString() != oldValue.ToString())
                    {
                        updatedProperties[kvp.Key] = ConvertToType(kvp.Value, propertyType);
                    }
                }
                else
                {
                    // One is empty and the other is not, mark as updated
                    updatedProperties[kvp.Key] = ConvertToType(kvp.Value, propertyType);
                }

            }
        }

        return updatedProperties;
    }

    public List<Dictionary<string, object>> TransformData(List<Dictionary<string, object>> data, Dictionary<string, string> mapping)
    {
        data = data.Select(dict => dict.ToDictionary(kvp => kvp.Key.Length > 0 && char.IsLower(kvp.Key[0]) ? char.ToUpper(kvp.Key[0]) + kvp.Key.Substring(1) : kvp.Key, kvp => kvp.Value)).ToList();

        var transformedData = new List<Dictionary<string, object>>();

        // Get the keys from the mapping dictionary to preserve the order
        var mappingKeys = mapping.Keys.ToList();

        foreach (var row in data)
        {
            var transformedRow = new Dictionary<string, object>();

            // Map the keys based on the mapping and preserve the order from mappingKeys
            foreach (var key in mappingKeys)
            {
                if (row.ContainsKey(key))
                {
                    var newKey = mapping[key];
                    transformedRow[newKey] = row[key];
                }
            }

            transformedData.Add(transformedRow);
        }

        return transformedData;
    }

    private object ConvertToType(object value, Type targetType)
    {
        try
        {
            //logger.LogInformation("Converting to type");
            // Handle JsonElement case first
            if (value is JsonElement jsonElement)
            {
                switch (jsonElement.ValueKind)
                {
                    case JsonValueKind.String:
                        // If the value is a string, we can directly convert it
                        return targetType switch
                        {
                            Type t when t == typeof(string) => jsonElement.GetString(),
                            Type t when t == typeof(int) => Convert.ToInt32(jsonElement.GetString()),
                            Type t when t == typeof(long) => Convert.ToInt64(jsonElement.GetString()),
                            Type t when t == typeof(decimal) => Convert.ToDecimal(jsonElement.GetString()),
                            Type t when t == typeof(bool) => Convert.ToBoolean(jsonElement.GetString()),
                            Type t when t == typeof(DateTime) => DateTime.Parse(jsonElement.GetString()),
                            Type t when t == typeof(DateTimeOffset) => DateTimeOffset.Parse(jsonElement.GetString()),
                            Type t when t == typeof(Guid) => Guid.Parse(jsonElement.GetString()),
                            Type t when t == typeof(byte[]) => Convert.FromBase64String(jsonElement.GetString()), // Assuming string represents base64-encoded byte[]
                            _ => jsonElement.GetString() // Default case: return as string
                        };
                    case JsonValueKind.Number:
                        // If the value is a number (integer, decimal, etc.), convert accordingly
                        return targetType switch
                        {
                            Type t when t == typeof(int) => jsonElement.GetInt32(),
                            Type t when t == typeof(long) => jsonElement.GetInt64(),
                            Type t when t == typeof(decimal) => jsonElement.GetDecimal(),
                            Type t when t == typeof(bool) => jsonElement.GetBoolean(),
                            Type t when t == typeof(DateTime) => DateTime.Parse(jsonElement.ToString()), // Number can be parsed to DateTime
                            Type t when t == typeof(DateTimeOffset) => DateTimeOffset.Parse(jsonElement.ToString()), // Same for DateTimeOffset
                            Type t when t == typeof(Guid) => Guid.Parse(jsonElement.ToString()), // Guid from string (if numeric form can parse to GUID)
                            _ => jsonElement.ToString() // Default case: treat as string
                        };

                    case JsonValueKind.Object:
                    case JsonValueKind.Array:
                        throw new InvalidOperationException($"Cannot convert {jsonElement.ValueKind} to {targetType.FullName}");
                    default:
                        throw new InvalidOperationException($"Unsupported JSON value kind: {jsonElement.ValueKind}");
                }
            }

            // Otherwise, handle non-JsonElement cases (the original switch block)
            return targetType switch
            {
                Type t when t == typeof(string) => value.ToString(),
                Type t when t == typeof(int) => Convert.ToInt32(value),
                Type t when t == typeof(long) => Convert.ToInt64(value),
                Type t when t == typeof(decimal) => Convert.ToDecimal(value),
                Type t when t == typeof(bool) => Convert.ToBoolean(value),
                Type t when t == typeof(DateTime) => value is DateTime dtValue ? dtValue : Convert.ToDateTime(value),
                Type t when t == typeof(DateTimeOffset) => Convert.ToDateTime(value), // Assumes DateTimeOffset in string form
                Type t when t == typeof(Guid) => Guid.Parse(value.ToString()),
                Type t when t == typeof(byte[]) => (byte[])value, // Assuming stream is byte array
                _ => value.ToString() // Default case: convert the value to a string
            };
        }
        catch (Exception ex)
        {
            var message = $"Error converting type: {ex.Message}";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogError(ex, message);
            throw;
        }
    }

    public class SettingsDto
    {
        public string BcToSql { get; set; }
        public bool SqlToBc { get; set; }
    }
}
