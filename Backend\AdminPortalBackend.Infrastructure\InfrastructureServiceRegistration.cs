﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using AdminPortalBackend.Infrastructure.Repositories;
using AdminPortalBackend.Core.Repositiories;
using System.Data.SqlClient;
using System.Data;
using Hangfire;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.DataStore;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.KernelMemory;
using Microsoft.KernelMemory.MemoryDb.SQLServer;
using AdminPortalBackend.Infrastructure.SignalRHub;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel.ChatCompletion;

namespace AdminPortalBackend.Infrastructure
{
    public static class InfrastructureServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("AdminConnection");

            // Register IDbConnection for Dapper
            services.AddScoped<IDbConnection>(provider => new SqlConnection(connectionString));

            // Add repository services
            services.AddScoped<IDbConnectionRepository, DbConnectionRepository>();
            services.AddScoped<IConnectionIntegrationRepository, ConnectionIntegrationRepository>();
            //services.AddScoped<IDataTransferingRepositoy, DataTransferingRepositoy>();
            services.AddScoped<SyncManager>();
            services.AddScoped<DataStoreFactory>();
            services.AddScoped<ODataService>();
            services.AddScoped<EncryptionHelper>();
            services.AddScoped<ITestConnectionRepository, TestConnectionRepository>();
            services.AddScoped<IAppSettingRepository, AppSettingRepository>();

            services.AddHttpClient();

            // Add Hangfire services

            services.AddHangfire(configuration =>
                configuration.UseSqlServerStorage(connectionString));

            services.AddHangfireServer();

            services.AddSignalR();

            // Retrieve the OpenAI API settings from configuration
            var openAIConfig = configuration.GetSection("OpenAI");
            //services.AddOpenAIChatCompletion(openAIConfig["ModelId"], openAIConfig["ApiKey"]);

#pragma warning disable
            // Register Semantic Kernel
            services.AddTransient<Kernel>(serviceProvider =>
            {
                var kernel = Kernel.CreateBuilder()
                    .AddOpenAIChatCompletion(openAIConfig["ChatModelId"], openAIConfig["ApiKey"])  
                    //.AddOpenAIChatCompletion("mistral-nemo", new Uri("https://localai.3dbotics.com/v1"), "")
                    //.AddOpenAITextEmbeddingGeneration(openAIConfig["EmbeddingModelId"], openAIConfig["ApiKey"])
                    .Build();

                return kernel;
            });

            services.AddKernelMemory(kmBuilder =>
            {
                kmBuilder.WithOpenAITextEmbeddingGeneration(new OpenAIConfig { EmbeddingModel = openAIConfig["EmbeddingModelId"], APIKey = openAIConfig["ApiKey"] })
                .WithOpenAITextGeneration(new OpenAIConfig { TextModel = openAIConfig["ChatModelId"], APIKey = openAIConfig["ApiKey"] })
                .WithSqlServerMemoryDb(new SqlServerConfig
                {
                    ConnectionString = connectionString,
                    EmbeddingsTableName = "KM_Embedding",
                    MemoryCollectionTableName = "KM_Collection",
                    MemoryTableName = "KM_Memory",
                    Schema = "dbo",
                    TagsTableName = "KM_Tag",
                })
                .Build<MemoryServerless>();
            });

            services.AddScoped<AIService>();
            // Chat completion service that kernels will use
            services.AddSingleton<IChatCompletionService>(sp =>
            {
                return new OpenAIChatCompletionService(openAIConfig["ChatModelId"], openAIConfig["ApiKey"]);
            });

            return services;
        }
    }
}
