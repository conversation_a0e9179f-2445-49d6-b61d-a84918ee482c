.chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.chat-header {
  font-size: 24px;
  margin-bottom: 20px;
}

.input-container {
  display: flex;
  align-items: center;
  border-radius: 30px;
  padding: 10px;
  width: 60%;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.1);
}

.attachment-icon {
  cursor: pointer;
  margin-right: 10px;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 16px;
  padding: 10px;
  resize: none; /* Disable manual resizing */
  max-height: 200px; /* Set maximum height */
  overflow-y: auto; /* Add scrolling when content exceeds height */
}

.send-button {
  background-color: var(--bg-color);
  border: none;
  outline: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50px;
  width: 36px;
}

.send-button i {
  color: white;
}

.send-button:disabled {
  background-color: #b3b3b3;
  cursor: not-allowed;
}

.send-button:disabled i {
  color: #8c8c8c;
}

.attachment-icon i {
  font-size: 18px;
  color: gray;
}
