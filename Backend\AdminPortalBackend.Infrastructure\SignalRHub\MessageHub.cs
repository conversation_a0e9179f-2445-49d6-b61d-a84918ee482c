﻿using Microsoft.AspNetCore.SignalR;
using AdminPortalBackend.Core.Contracts;

namespace AdminPortalBackend.Infrastructure.SignalRHub;

public class MessageHub : Hub<IMessageClient>
{
    private readonly AIService _aiService;
    
    public MessageHub(AIService aiService)
    {
        _aiService = aiService;
    }

    public async Task SendMessage(string message)
    {
        await Clients.All.SendMessage(message);
    }

    public async Task StartAIConversation(string question)
    {
        await foreach (var chunk in _aiService.GetAIAnswerStream(question))
        {
            if (!string.IsNullOrEmpty(chunk))
            {
                await Clients.Caller.ReceiveAIChunk(chunk);
            }
        }
    }
}

