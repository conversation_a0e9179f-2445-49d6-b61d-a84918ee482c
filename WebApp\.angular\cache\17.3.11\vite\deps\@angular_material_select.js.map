{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["import { Overlay, CdkOverlayOrigin, CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';\nimport { NgClass, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Self, Attribute, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { _countGroupLabelsBeforeOption, _getOptionScrollPosition, _ErrorStateTracker, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nexport { MatOptgroup, MatOption } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON><PERSON>r, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, distinctUntilChanged, takeUntil, take } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nfunction MatSelect_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.triggerValue);\n  }\n}\nfunction MatSelect_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtemplate(1, MatSelect_Conditional_5_Conditional_1_Template, 1, 0)(2, MatSelect_Conditional_5_Conditional_2_Template, 2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.customTrigger ? 1 : 2);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"@transformPanel.done\", function MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._panelDoneAnimatingStream.next($event.toState));\n    })(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r1._getPanelTheme(), \"\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelClass)(\"@transformPanel\", \"showing\");\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-panel\")(\"aria-multiselectable\", ctx_r1.multiple)(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby());\n  }\n}\nconst matSelectAnimations = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: trigger('transformPanelWrap', [transition('* => void', query('@transformPanel', [animateChild()], {\n    optional: true\n  }))]),\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: trigger('transformPanel', [state('void', style({\n    opacity: 0,\n    transform: 'scale(1, 0.8)'\n  })), transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1, 1)'\n  }))), transition('* => void', animate('100ms linear', style({\n    opacity: 0\n  })))])\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  constructor( /** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\nclass MatSelect {\n  /** Scrolls a particular option into the view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  /** Called when the panel has been opened and the overlay has settled on its final position. */\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  /** Creates a change event object that should be emitted by the select. */\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether the component is required. */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = value;\n  }\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  /** Object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  /** Whether the select is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor(_viewportRuler, _changeDetectorRef,\n  /**\n   * @deprecated Unused param, will be removed.\n   * @breaking-change 19.0.0\n   */\n  _unusedNgZone, defaultErrorStateMatcher, _elementRef, _dir, parentForm, parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n    this._viewportRuler = _viewportRuler;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    this._parentFormField = _parentFormField;\n    this.ngControl = ngControl;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._defaultOptions = _defaultOptions;\n    /**\n     * This position config ensures that the top \"start\" corner of the overlay\n     * is aligned with with the top \"start\" of the origin by default (overlapping\n     * the trigger completely). If the panel cannot fit below the trigger, it\n     * will fall back to a position above the trigger.\n     */\n    this._positions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }];\n    /** Whether or not the overlay panel is open. */\n    this._panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    this._compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    this._uid = `mat-select-${nextUniqueId++}`;\n    /** Current `aria-labelledby` value for the select trigger. */\n    this._triggerAriaLabelledBy = null;\n    /** Emits whenever the component is destroyed. */\n    this._destroy = new Subject();\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n    /**\n     * Disable the automatic labeling to avoid issues like #27241.\n     * @docs-private\n     */\n    this.disableAutomaticLabeling = true;\n    /** `View -> model callback called when value changes` */\n    this._onChange = () => {};\n    /** `View -> model callback called when select has been touched` */\n    this._onTouched = () => {};\n    /** ID for the DOM node containing the select's value. */\n    this._valueId = `mat-select-value-${nextUniqueId++}`;\n    /** Emits when the panel element is finished transforming in. */\n    this._panelDoneAnimatingStream = new Subject();\n    this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    this._focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    this.controlType = 'mat-select';\n    /** Whether the select is disabled. */\n    this.disabled = false;\n    /** Whether ripples in the select are disabled. */\n    this.disableRipple = false;\n    /** Tab index of the select. */\n    this.tabIndex = 0;\n    this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    this._multiple = false;\n    /** Whether to center the active option over the trigger. */\n    this.disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /** Aria label of the select. */\n    this.ariaLabel = '';\n    /**\n     * Width of the panel. If set to `auto`, the panel will match the trigger width.\n     * If set to null or an empty string, the panel will grow to match the longest option's text.\n     */\n    this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n    this._initialized = new Subject();\n    /** Combined stream of all of the child options' change events. */\n    this.optionSelectionChanges = defer(() => {\n      const options = this.options;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    this.openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the select has been closed. */\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the selected value has been changed by the user. */\n    this.selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    this.valueChange = new EventEmitter();\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    this._trackedModal = null;\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    this._skipPredicate = option => {\n      if (this.panelOpen) {\n        // Support keyboard focusing disabled options in an ARIA listbox.\n        return false;\n      }\n      // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n      // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n      // closed.\n      return option.disabled;\n    };\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (_defaultOptions?.typeaheadDebounceInterval != null) {\n      this.typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    // We need `distinctUntilChanged` here, because some browsers will\n    // fire the animation end event twice for the same animation. See:\n    // https://github.com/angular/angular/issues/24084\n    this._panelDoneAnimatingStream.pipe(distinctUntilChanged(), takeUntil(this._destroy)).subscribe(() => this._panelDoneAnimating(this.panelOpen));\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngAfterContentInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by the input, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    if (!this._canOpen()) {\n      return;\n    }\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    this._applyModalPanelOwnership();\n    this._panelOpen = true;\n    this._keyManager.withHorizontalOrientation(null);\n    this._highlightCorrectOption();\n    this._changeDetectorRef.markForCheck();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n  }\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Refreshes the error state of the select. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Callback that is invoked when the overlay panel has been attached.\n   */\n  _onAttached() {\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return option.value != null && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth(preferredOrigin) {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    let value = (labelId ? labelId + ' ' : '') + this._valueId;\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    return value;\n  }\n  /** Called when the overlay panel is done animating. */\n  _panelDoneAnimating(isOpen) {\n    this.openedChange.emit(isOpen);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n  static {\n    this.ɵfac = function MatSelect_Factory(t) {\n      return new (t || MatSelect)(i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ErrorStateMatcher), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NgForm, 8), i0.ɵɵdirectiveInject(i4.FormGroupDirective, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i4.NgControl, 10), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SELECT_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.LiveAnnouncer), i0.ɵɵdirectiveInject(MAT_SELECT_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSelect,\n      selectors: [[\"mat-select\"]],\n      contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n        }\n      },\n      viewQuery: function MatSelect_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n      hostVars: 19,\n      hostBindings: function MatSelect_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n            return ctx._onFocus();\n          })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n            return ctx._onBlur();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n          i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n        }\n      },\n      inputs: {\n        userAriaDescribedBy: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"userAriaDescribedBy\"],\n        panelClass: \"panelClass\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        hideSingleSelectionIndicator: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n        placeholder: \"placeholder\",\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n        multiple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiple\", \"multiple\", booleanAttribute],\n        disableOptionCentering: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute],\n        compareWith: \"compareWith\",\n        value: \"value\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        errorStateMatcher: \"errorStateMatcher\",\n        typeaheadDebounceInterval: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute],\n        sortComparator: \"sortComparator\",\n        id: \"id\",\n        panelWidth: \"panelWidth\"\n      },\n      outputs: {\n        openedChange: \"openedChange\",\n        _openedStream: \"opened\",\n        _closedStream: \"closed\",\n        selectionChange: \"selectionChange\",\n        valueChange: \"valueChange\"\n      },\n      exportAs: [\"matSelect\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 11,\n      vars: 8,\n      consts: [[\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [\"panel\", \"\"], [\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [1, \"mat-mdc-select-value\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"backdropClick\", \"attach\", \"detach\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\"], [1, \"mat-mdc-select-value-text\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"keydown\", \"ngClass\"]],\n      template: function MatSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.open());\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSelect_Conditional_4_Template, 2, 1, \"span\", 4)(5, MatSelect_Conditional_5_Template, 3, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 7);\n          i0.ɵɵelement(9, \"path\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 9, \"ng-template\", 9);\n          i0.ɵɵlistener(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.close());\n          })(\"attach\", function MatSelect_Template_ng_template_attach_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onAttached());\n          })(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.close());\n          });\n        }\n        if (rf & 2) {\n          const fallbackOverlayOrigin_r4 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"id\", ctx._valueId);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.empty ? 4 : 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || fallbackOverlayOrigin_r4)(\"cdkConnectedOverlayOpen\", ctx.panelOpen)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth);\n        }\n      },\n      dependencies: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSelectAnimations.transformPanel]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-autocomplete': 'none',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      animations: [matSelectAnimations.transformPanel],\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      standalone: true,\n      imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"]\n    }]\n  }], () => [{\n    type: i1.ViewportRuler\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.ErrorStateMatcher\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NgForm,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.FormGroupDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i6.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_FORM_FIELD]\n    }]\n  }, {\n    type: i4.NgControl,\n    decorators: [{\n      type: Self\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SELECT_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: i5.LiveAnnouncer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SELECT_CONFIG]\n    }]\n  }], {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableOptionCentering: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n  static {\n    this.ɵfac = function MatSelectTrigger_Factory(t) {\n      return new (t || MatSelectTrigger)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSelectTrigger,\n      selectors: [[\"mat-select-trigger\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass MatSelectModule {\n  static {\n    this.ɵfac = function MatSelectModule_Factory(t) {\n      return new (t || MatSelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSelectModule,\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, matSelectAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,GAAG;AAC1C,IAAM,MAAM,CAAC,sBAAsB,GAAG;AACtC,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,WAAW;AAAA,EACzC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY;AAAA,EAC1C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,gDAAgD,GAAG,CAAC,EAAE,GAAG,gDAAgD,GAAG,CAAC;AAC9H,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,gBAAgB,IAAI,CAAC;AAAA,EAClD;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,wBAAwB,SAAS,+EAA+E,QAAQ;AACpI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,0BAA0B,KAAK,OAAO,OAAO,CAAC;AAAA,IAC7E,CAAC,EAAE,WAAW,SAAS,yDAAyD,QAAQ;AACtF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,uBAAuB,iEAAiE,OAAO,eAAe,GAAG,EAAE;AACtH,IAAG,WAAW,WAAW,OAAO,UAAU,EAAE,mBAAmB,SAAS;AACxE,IAAG,YAAY,MAAM,OAAO,KAAK,QAAQ,EAAE,wBAAwB,OAAO,QAAQ,EAAE,cAAc,OAAO,aAAa,IAAI,EAAE,mBAAmB,OAAO,wBAAwB,CAAC;AAAA,EACjL;AACF;AACA,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,oBAAoB,QAAQ,sBAAsB,CAAC,WAAW,aAAa,MAAM,mBAAmB,CAAC,aAAa,CAAC,GAAG;AAAA,IACpH,UAAU;AAAA,EACZ,CAAC,CAAC,CAAC,CAAC;AAAA;AAAA,EAEJ,gBAAgB,QAAQ,kBAAkB,CAAC,MAAM,QAAQ,MAAM;AAAA,IAC7D,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,WAAW,mBAAmB,QAAQ,oCAAoC,MAAM;AAAA,IACnF,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,gBAAgB,MAAM;AAAA,IAC1D,SAAS;AAAA,EACX,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AASA,SAAS,mCAAmC;AAC1C,SAAO,MAAM,+DAA+D;AAC9E;AAOA,SAAS,iCAAiC;AACxC,SAAO,MAAM,oDAAoD;AACnE;AAMA,SAAS,oCAAoC;AAC3C,SAAO,MAAM,mCAAmC;AAClD;AACA,IAAI,eAAe;AAEnB,IAAM,6BAA6B,IAAI,eAAe,8BAA8B;AAAA,EAClF,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAED,SAAS,4CAA4C,SAAS;AAC5D,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AACnD;AAEA,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAEhE,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAMA,IAAM,qBAAqB,IAAI,eAAe,kBAAkB;AAEhE,IAAM,kBAAN,MAAsB;AAAA,EACpB,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA,EAEd,sBAAsB,OAAO;AAC3B,UAAM,SAAS,KAAK,QAAQ,QAAQ,EAAE,KAAK;AAC3C,QAAI,QAAQ;AACV,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,aAAa,8BAA8B,OAAO,KAAK,SAAS,KAAK,YAAY;AACvF,YAAM,UAAU,OAAO,gBAAgB;AACvC,UAAI,UAAU,KAAK,eAAe,GAAG;AAInC,cAAM,YAAY;AAAA,MACpB,OAAO;AACL,cAAM,YAAY,yBAAyB,QAAQ,WAAW,QAAQ,cAAc,MAAM,WAAW,MAAM,YAAY;AAAA,MACzH;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,sBAAsB,KAAK,YAAY,mBAAmB,CAAC;AAAA,EAClE;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,WAAO,IAAI,gBAAgB,MAAM,KAAK;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA;AAAA,EAEA,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,OAAO;AACtC,SAAK,gCAAgC;AACrC,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,WAAW,SAAS,aAAa,WAAW,QAAQ,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,KAAK,oBAAoB,OAAO,cAAc,eAAe,YAAY;AAC3E,YAAM,iCAAiC;AAAA,IACzC;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,IAAI;AAClB,QAAI,OAAO,OAAO,eAAe,OAAO,cAAc,eAAe,YAAY;AAC/E,YAAM,kCAAkC;AAAA,IAC1C;AACA,SAAK,eAAe;AACpB,QAAI,KAAK,iBAAiB;AAExB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,UAAM,cAAc,KAAK,aAAa,QAAQ;AAC9C,QAAI,aAAa;AACf,WAAK,UAAU,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,GAAG,OAAO;AACZ,SAAK,MAAM,SAAS,KAAK;AACzB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,YAAY,gBAAgB,oBAK5B,eAAe,0BAA0B,aAAa,MAAM,YAAY,iBAAiB,kBAAkB,WAAW,UAAU,uBAAuB,gBAAgB,iBAAiB;AACtL,SAAK,iBAAiB;AACtB,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AAOvB,SAAK,aAAa,CAAC;AAAA,MACjB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAED,SAAK,aAAa;AAElB,SAAK,eAAe,CAAC,IAAI,OAAO,OAAO;AAEvC,SAAK,OAAO,cAAc,cAAc;AAExC,SAAK,yBAAyB;AAE9B,SAAK,WAAW,IAAI,QAAQ;AAM5B,SAAK,eAAe,IAAI,QAAQ;AAKhC,SAAK,2BAA2B;AAEhC,SAAK,YAAY,MAAM;AAAA,IAAC;AAExB,SAAK,aAAa,MAAM;AAAA,IAAC;AAEzB,SAAK,WAAW,oBAAoB,cAAc;AAElD,SAAK,4BAA4B,IAAI,QAAQ;AAC7C,SAAK,qBAAqB,KAAK,iBAAiB,qBAAqB;AACrE,SAAK,WAAW;AAEhB,SAAK,cAAc;AAEnB,SAAK,WAAW;AAEhB,SAAK,gBAAgB;AAErB,SAAK,WAAW;AAChB,SAAK,gCAAgC,KAAK,iBAAiB,gCAAgC;AAC3F,SAAK,YAAY;AAEjB,SAAK,yBAAyB,KAAK,iBAAiB,0BAA0B;AAE9E,SAAK,YAAY;AAKjB,SAAK,aAAa,KAAK,mBAAmB,OAAO,KAAK,gBAAgB,eAAe,cAAc,KAAK,gBAAgB,aAAa;AACrI,SAAK,eAAe,IAAI,QAAQ;AAEhC,SAAK,yBAAyB,MAAM,MAAM;AACxC,YAAM,UAAU,KAAK;AACrB,UAAI,SAAS;AACX,eAAO,QAAQ,QAAQ,KAAK,UAAU,OAAO,GAAG,UAAU,MAAM,MAAM,GAAG,QAAQ,IAAI,YAAU,OAAO,iBAAiB,CAAC,CAAC,CAAC;AAAA,MAC5H;AACA,aAAO,KAAK,aAAa,KAAK,UAAU,MAAM,KAAK,sBAAsB,CAAC;AAAA,IAC5E,CAAC;AAED,SAAK,eAAe,IAAI,aAAa;AAErC,SAAK,gBAAgB,KAAK,aAAa,KAAK,OAAO,OAAK,CAAC,GAAG,IAAI,MAAM;AAAA,IAAC,CAAC,CAAC;AAEzE,SAAK,gBAAgB,KAAK,aAAa,KAAK,OAAO,OAAK,CAAC,CAAC,GAAG,IAAI,MAAM;AAAA,IAAC,CAAC,CAAC;AAE1E,SAAK,kBAAkB,IAAI,aAAa;AAMxC,SAAK,cAAc,IAAI,aAAa;AAMpC,SAAK,gBAAgB;AAerB,SAAK,iBAAiB,YAAU;AAC9B,UAAI,KAAK,WAAW;AAElB,eAAO;AAAA,MACT;AAIA,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,KAAK,WAAW;AAGlB,WAAK,UAAU,gBAAgB;AAAA,IACjC;AAGA,QAAI,iBAAiB,6BAA6B,MAAM;AACtD,WAAK,4BAA4B,gBAAgB;AAAA,IACnD;AACA,SAAK,qBAAqB,IAAI,mBAAmB,0BAA0B,WAAW,iBAAiB,YAAY,KAAK,YAAY;AACpI,SAAK,yBAAyB;AAC9B,SAAK,kBAAkB,KAAK,uBAAuB;AACnD,SAAK,WAAW,SAAS,QAAQ,KAAK;AAEtC,SAAK,KAAK,KAAK;AAAA,EACjB;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,IAAI,eAAe,KAAK,QAAQ;AACvD,SAAK,aAAa,KAAK;AAIvB,SAAK,0BAA0B,KAAK,qBAAqB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,oBAAoB,KAAK,SAAS,CAAC;AAC9I,SAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1E,UAAI,KAAK,WAAW;AAClB,aAAK,gBAAgB,KAAK,iBAAiB,KAAK,uBAAuB;AACvE,aAAK,mBAAmB,cAAc;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,SAAS;AAC3B,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC7E,YAAM,MAAM,QAAQ,YAAU,OAAO,OAAO,CAAC;AAC7C,YAAM,QAAQ,QAAQ,YAAU,OAAO,SAAS,CAAC;AAAA,IACnD,CAAC;AACD,SAAK,QAAQ,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACnF,WAAK,cAAc;AACnB,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,UAAM,oBAAoB,KAAK,0BAA0B;AACzD,UAAM,YAAY,KAAK;AAIvB,QAAI,sBAAsB,KAAK,wBAAwB;AACrD,YAAM,UAAU,KAAK,YAAY;AACjC,WAAK,yBAAyB;AAC9B,UAAI,mBAAmB;AACrB,gBAAQ,aAAa,mBAAmB,iBAAiB;AAAA,MAC3D,OAAO;AACL,gBAAQ,gBAAgB,iBAAiB;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,WAAW;AAEb,UAAI,KAAK,qBAAqB,UAAU,SAAS;AAC/C,YAAI,KAAK,qBAAqB,UAAa,UAAU,aAAa,QAAQ,UAAU,aAAa,KAAK,UAAU;AAC9G,eAAK,WAAW,UAAU;AAAA,QAC5B;AACA,aAAK,mBAAmB,UAAU;AAAA,MACpC;AACA,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AAGnB,QAAI,QAAQ,UAAU,KAAK,QAAQ,qBAAqB,GAAG;AACzD,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,QAAI,QAAQ,2BAA2B,KAAK,KAAK,aAAa;AAC5D,WAAK,YAAY,cAAc,KAAK,yBAAyB;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ;AAC1B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,aAAa,SAAS;AAC3B,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,YAAY,KAAK,MAAM,IAAI,KAAK,KAAK;AAAA,EAC5C;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,SAAS,GAAG;AACpB;AAAA,IACF;AAIA,QAAI,KAAK,kBAAkB;AACzB,WAAK,0BAA0B,KAAK,iBAAiB,0BAA0B;AAAA,IACjF;AACA,SAAK,gBAAgB,KAAK,iBAAiB,KAAK,uBAAuB;AACvE,SAAK,0BAA0B;AAC/B,SAAK,aAAa;AAClB,SAAK,YAAY,0BAA0B,IAAI;AAC/C,SAAK,wBAAwB;AAC7B,SAAK,mBAAmB,aAAa;AAErC,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,4BAA4B;AAO1B,UAAM,QAAQ,KAAK,YAAY,cAAc,QAAQ,mDAAmD;AACxG,QAAI,CAAC,OAAO;AAEV;AAAA,IACF;AACA,UAAM,UAAU,GAAG,KAAK,EAAE;AAC1B,QAAI,KAAK,eAAe;AACtB,6BAAuB,KAAK,eAAe,aAAa,OAAO;AAAA,IACjE;AACA,wBAAoB,OAAO,aAAa,OAAO;AAC/C,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,CAAC,KAAK,eAAe;AAEvB;AAAA,IACF;AACA,UAAM,UAAU,GAAG,KAAK,EAAE;AAC1B,2BAAuB,KAAK,eAAe,aAAa,OAAO;AAC/D,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa;AAClB,WAAK,YAAY,0BAA0B,KAAK,OAAO,IAAI,QAAQ,KAAK;AACxE,WAAK,mBAAmB,aAAa;AACrC,WAAK,WAAW;AAEhB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,mBAAmB,aAAa;AACrC,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,iBAAiB,YAAY,CAAC,IAAI,KAAK,iBAAiB,SAAS,CAAC;AAAA,EAChG;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,QAAI,KAAK,OAAO;AACd,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW;AAClB,YAAM,kBAAkB,KAAK,gBAAgB,SAAS,IAAI,YAAU,OAAO,SAAS;AACpF,UAAI,KAAK,OAAO,GAAG;AACjB,wBAAgB,QAAQ;AAAA,MAC1B;AAEA,aAAO,gBAAgB,KAAK,IAAI;AAAA,IAClC;AACA,WAAO,KAAK,gBAAgB,SAAS,CAAC,EAAE;AAAA,EAC1C;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,OAAO,KAAK,KAAK,UAAU,QAAQ;AAAA,EACjD;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,mBAAmB,KAAK,IAAI,KAAK,qBAAqB,KAAK;AAAA,IACnF;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,YAAY,cAAc,YAAY,YAAY,YAAY,cAAc,YAAY;AAC3G,UAAM,YAAY,YAAY,SAAS,YAAY;AACnD,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,QAAQ,SAAS,KAAK,aAAa,CAAC,eAAe,KAAK,MAAM,KAAK,YAAY,MAAM,WAAW,YAAY;AAC/G,YAAM,eAAe;AACrB,WAAK,KAAK;AAAA,IACZ,WAAW,CAAC,KAAK,UAAU;AACzB,YAAM,2BAA2B,KAAK;AACtC,cAAQ,UAAU,KAAK;AACvB,YAAM,iBAAiB,KAAK;AAE5B,UAAI,kBAAkB,6BAA6B,gBAAgB;AAGjE,aAAK,eAAe,SAAS,eAAe,WAAW,GAAK;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,YAAY,cAAc,YAAY;AACzD,UAAM,WAAW,QAAQ,SAAS;AAClC,QAAI,cAAc,MAAM,QAAQ;AAE9B,YAAM,eAAe;AACrB,WAAK,MAAM;AAAA,IAGb,WAAW,CAAC,aAAa,YAAY,SAAS,YAAY,UAAU,QAAQ,cAAc,CAAC,eAAe,KAAK,GAAG;AAChH,YAAM,eAAe;AACrB,cAAQ,WAAW,sBAAsB;AAAA,IAC3C,WAAW,CAAC,YAAY,KAAK,aAAa,YAAY,KAAK,MAAM,SAAS;AACxE,YAAM,eAAe;AACrB,YAAM,uBAAuB,KAAK,QAAQ,KAAK,SAAO,CAAC,IAAI,YAAY,CAAC,IAAI,QAAQ;AACpF,WAAK,QAAQ,QAAQ,YAAU;AAC7B,YAAI,CAAC,OAAO,UAAU;AACpB,iCAAuB,OAAO,OAAO,IAAI,OAAO,SAAS;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,YAAM,yBAAyB,QAAQ;AACvC,cAAQ,UAAU,KAAK;AACvB,UAAI,KAAK,aAAa,cAAc,MAAM,YAAY,QAAQ,cAAc,QAAQ,oBAAoB,wBAAwB;AAC9H,gBAAQ,WAAW,sBAAsB;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,WAAW;AAChB,SAAK,aAAa,gBAAgB;AAClC,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AACrC,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AACrC,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,YAAY,eAAe,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC5D,WAAK,mBAAmB,cAAc;AACtC,WAAK,oBAAoB;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,mBAAmB,OAAO,KAAK,iBAAiB,KAAK,KAAK;AAAA,EACxE;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,QAAQ;AAAA,EAC/D;AAAA,EACA,uBAAuB;AAGrB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,UAAI,KAAK,WAAW;AAClB,aAAK,SAAS,KAAK,UAAU;AAAA,MAC/B;AACA,WAAK,qBAAqB,KAAK,MAAM;AACrC,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,SAAK,QAAQ,QAAQ,YAAU,OAAO,kBAAkB,CAAC;AACzD,SAAK,gBAAgB,MAAM;AAC3B,QAAI,KAAK,YAAY,OAAO;AAC1B,UAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,OAAO,cAAc,eAAe,YAAY;AAC5E,cAAM,+BAA+B;AAAA,MACvC;AACA,YAAM,QAAQ,kBAAgB,KAAK,qBAAqB,YAAY,CAAC;AACrE,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,YAAM,sBAAsB,KAAK,qBAAqB,KAAK;AAG3D,UAAI,qBAAqB;AACvB,aAAK,YAAY,iBAAiB,mBAAmB;AAAA,MACvD,WAAW,CAAC,KAAK,WAAW;AAG1B,aAAK,YAAY,iBAAiB,EAAE;AAAA,MACtC;AAAA,IACF;AACA,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,UAAM,sBAAsB,KAAK,QAAQ,KAAK,YAAU;AAGtD,UAAI,KAAK,gBAAgB,WAAW,MAAM,GAAG;AAC3C,eAAO;AAAA,MACT;AACA,UAAI;AAEF,eAAO,OAAO,SAAS,QAAQ,KAAK,aAAa,OAAO,OAAO,KAAK;AAAA,MACtE,SAAS,OAAO;AACd,YAAI,OAAO,cAAc,eAAe,WAAW;AAEjD,kBAAQ,KAAK,KAAK;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,QAAI,qBAAqB;AACvB,WAAK,gBAAgB,OAAO,mBAAmB;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,aAAa,UAAU;AAErB,QAAI,aAAa,KAAK,UAAU,KAAK,aAAa,MAAM,QAAQ,QAAQ,GAAG;AACzE,UAAI,KAAK,SAAS;AAChB,aAAK,qBAAqB,QAAQ;AAAA,MACpC;AACA,WAAK,SAAS;AACd,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,iBAAiB,iBAAiB;AAChC,QAAI,KAAK,eAAe,QAAQ;AAC9B,YAAM,eAAe,2BAA2B,mBAAmB,gBAAgB,aAAa,mBAAmB,KAAK;AACxH,aAAO,aAAa,cAAc,sBAAsB,EAAE;AAAA,IAC5D;AACA,WAAO,KAAK,eAAe,OAAO,KAAK,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,SAAS;AAChB,iBAAW,UAAU,KAAK,SAAS;AACjC,eAAO,mBAAmB,aAAa;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,cAAc,IAAI,2BAA2B,KAAK,OAAO,EAAE,cAAc,KAAK,yBAAyB,EAAE,wBAAwB,EAAE,0BAA0B,KAAK,OAAO,IAAI,QAAQ,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,wBAAwB,CAAC,UAAU,CAAC,EAAE,cAAc,KAAK,cAAc;AAC1S,SAAK,YAAY,OAAO,UAAU,MAAM;AACtC,UAAI,KAAK,WAAW;AAGlB,YAAI,CAAC,KAAK,YAAY,KAAK,YAAY,YAAY;AACjD,eAAK,YAAY,WAAW,sBAAsB;AAAA,QACpD;AAGA,aAAK,MAAM;AACX,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AACD,SAAK,YAAY,OAAO,UAAU,MAAM;AACtC,UAAI,KAAK,cAAc,KAAK,OAAO;AACjC,aAAK,sBAAsB,KAAK,YAAY,mBAAmB,CAAC;AAAA,MAClE,WAAW,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,KAAK,YAAY,YAAY;AAC5E,aAAK,YAAY,WAAW,sBAAsB;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,qBAAqB,MAAM,KAAK,QAAQ,SAAS,KAAK,QAAQ;AACpE,SAAK,uBAAuB,KAAK,UAAU,kBAAkB,CAAC,EAAE,UAAU,WAAS;AACjF,WAAK,UAAU,MAAM,QAAQ,MAAM,WAAW;AAC9C,UAAI,MAAM,eAAe,CAAC,KAAK,YAAY,KAAK,YAAY;AAC1D,aAAK,MAAM;AACX,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AAGD,UAAM,GAAG,KAAK,QAAQ,IAAI,YAAU,OAAO,aAAa,CAAC,EAAE,KAAK,UAAU,kBAAkB,CAAC,EAAE,UAAU,MAAM;AAI7G,WAAK,mBAAmB,cAAc;AACtC,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,UAAU,QAAQ,aAAa;AAC7B,UAAM,cAAc,KAAK,gBAAgB,WAAW,MAAM;AAC1D,QAAI,OAAO,SAAS,QAAQ,CAAC,KAAK,WAAW;AAC3C,aAAO,SAAS;AAChB,WAAK,gBAAgB,MAAM;AAC3B,UAAI,KAAK,SAAS,MAAM;AACtB,aAAK,kBAAkB,OAAO,KAAK;AAAA,MACrC;AAAA,IACF,OAAO;AACL,UAAI,gBAAgB,OAAO,UAAU;AACnC,eAAO,WAAW,KAAK,gBAAgB,OAAO,MAAM,IAAI,KAAK,gBAAgB,SAAS,MAAM;AAAA,MAC9F;AACA,UAAI,aAAa;AACf,aAAK,YAAY,cAAc,MAAM;AAAA,MACvC;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,YAAY;AACjB,YAAI,aAAa;AAKf,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,QAAI,gBAAgB,KAAK,gBAAgB,WAAW,MAAM,GAAG;AAC3D,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,UAAU;AACjB,YAAM,UAAU,KAAK,QAAQ,QAAQ;AACrC,WAAK,gBAAgB,KAAK,CAAC,GAAG,MAAM;AAClC,eAAO,KAAK,iBAAiB,KAAK,eAAe,GAAG,GAAG,OAAO,IAAI,QAAQ,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,MAC1G,CAAC;AACD,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,eAAe;AAC/B,QAAI;AACJ,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,SAAS,IAAI,YAAU,OAAO,KAAK;AAAA,IACxD,OAAO;AACL,oBAAc,KAAK,WAAW,KAAK,SAAS,QAAQ;AAAA,IACtD;AACA,SAAK,SAAS;AACd,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,UAAU,WAAW;AAC1B,SAAK,gBAAgB,KAAK,KAAK,gBAAgB,WAAW,CAAC;AAC3D,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,QAAI,KAAK,aAAa;AACpB,UAAI,KAAK,OAAO;AAId,YAAI,0BAA0B;AAC9B,iBAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,QAAQ,SAAS;AACxD,gBAAM,SAAS,KAAK,QAAQ,IAAI,KAAK;AACrC,cAAI,CAAC,OAAO,UAAU;AACpB,sCAA0B;AAC1B;AAAA,UACF;AAAA,QACF;AACA,aAAK,YAAY,cAAc,uBAAuB;AAAA,MACxD,OAAO;AACL,aAAK,YAAY,cAAc,KAAK,gBAAgB,SAAS,CAAC,CAAC;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,KAAK,SAAS,SAAS;AAAA,EACtE;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,YAAY,cAAc,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,0BAA0B;AACxB,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,kBAAkB,WAAW;AAClD,UAAM,kBAAkB,UAAU,UAAU,MAAM;AAClD,WAAO,KAAK,iBAAiB,kBAAkB,KAAK,iBAAiB;AAAA,EACvE;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,aAAa,KAAK,eAAe,KAAK,YAAY,YAAY;AACrE,aAAO,KAAK,YAAY,WAAW;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,4BAA4B;AAC1B,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,kBAAkB,WAAW;AAClD,QAAI,SAAS,UAAU,UAAU,MAAM,MAAM,KAAK;AAClD,QAAI,KAAK,gBAAgB;AACvB,eAAS,MAAM,KAAK;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,oBAAoB,QAAQ;AAC1B,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,QAAI,IAAI,QAAQ;AACd,WAAK,YAAY,cAAc,aAAa,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,IAC/E,OAAO;AACL,WAAK,YAAY,cAAc,gBAAgB,kBAAkB;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,MAAM;AACX,SAAK,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AAGrB,WAAO,KAAK,aAAa,CAAC,KAAK,SAAS,KAAK,WAAW,CAAC,CAAC,KAAK;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,GAAG;AACxC,aAAO,KAAK,KAAK,YAAc,kBAAqB,aAAa,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,QAAQ,CAAC,GAAM,kBAAqB,oBAAoB,CAAC,GAAM,kBAAkB,gBAAgB,CAAC,GAAM,kBAAqB,WAAW,EAAE,GAAM,kBAAkB,UAAU,GAAM,kBAAkB,0BAA0B,GAAM,kBAAqB,aAAa,GAAM,kBAAkB,mBAAmB,CAAC,CAAC;AAAA,IAC3lB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,gBAAgB,SAAS,yBAAyB,IAAI,KAAK,UAAU;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,oBAAoB,CAAC;AACjD,UAAG,eAAe,UAAU,WAAW,CAAC;AACxC,UAAG,eAAe,UAAU,cAAc,CAAC;AAAA,QAC7C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAC3D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe;AAAA,QAClE;AAAA,MACF;AAAA,MACA,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,qBAAqB,CAAC;AAAA,QACvC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,YAAY,qBAAqB,QAAQ,iBAAiB,WAAW,GAAG,gBAAgB;AAAA,MAC5G,UAAU;AAAA,MACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,qCAAqC,QAAQ;AAC7E,mBAAO,IAAI,eAAe,MAAM;AAAA,UAClC,CAAC,EAAE,SAAS,SAAS,qCAAqC;AACxD,mBAAO,IAAI,SAAS;AAAA,UACtB,CAAC,EAAE,QAAQ,SAAS,oCAAoC;AACtD,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,WAAW,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,YAAY,IAAI,KAAK,WAAW,IAAI,EAAE,iBAAiB,IAAI,SAAS,EAAE,cAAc,IAAI,aAAa,IAAI,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,gBAAgB,IAAI,UAAU,EAAE,yBAAyB,IAAI,yBAAyB,CAAC;AACtX,UAAG,YAAY,2BAA2B,IAAI,QAAQ,EAAE,0BAA0B,IAAI,UAAU,EAAE,2BAA2B,IAAI,QAAQ,EAAE,wBAAwB,IAAI,KAAK,EAAE,2BAA2B,IAAI,QAAQ;AAAA,QACvN;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,qBAAqB,CAAI,WAAa,MAAM,oBAAoB,qBAAqB;AAAA,QACrF,YAAY;AAAA,QACZ,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,QAC/F,eAAe,CAAI,WAAa,4BAA4B,iBAAiB,iBAAiB,gBAAgB;AAAA,QAC9G,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,QAClI,8BAA8B,CAAI,WAAa,4BAA4B,gCAAgC,gCAAgC,gBAAgB;AAAA,QAC3J,aAAa;AAAA,QACb,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,QAC/F,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,QAC/F,wBAAwB,CAAI,WAAa,4BAA4B,0BAA0B,0BAA0B,gBAAgB;AAAA,QACzI,aAAa;AAAA,QACb,OAAO;AAAA,QACP,WAAW,CAAI,WAAa,MAAM,cAAc,WAAW;AAAA,QAC3D,gBAAgB,CAAI,WAAa,MAAM,mBAAmB,gBAAgB;AAAA,QAC1E,mBAAmB;AAAA,QACnB,2BAA2B,CAAI,WAAa,4BAA4B,6BAA6B,6BAA6B,eAAe;AAAA,QACjJ,gBAAgB;AAAA,QAChB,IAAI;AAAA,QACJ,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,QACd,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,sBAAyB,mBAAmB;AAAA,MACjF,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,yBAAyB,oBAAoB,WAAW,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,sBAAsB,IAAI,GAAG,0BAA0B,GAAG,OAAO,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,8BAA8B,yBAAyB,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,WAAW,aAAa,SAAS,QAAQ,UAAU,QAAQ,aAAa,SAAS,eAAe,MAAM,GAAG,CAAC,KAAK,gBAAgB,GAAG,CAAC,yBAAyB,IAAI,mCAAmC,IAAI,kCAAkC,IAAI,oCAAoC,oCAAoC,GAAG,iBAAiB,UAAU,UAAU,iCAAiC,qCAAqC,6BAA6B,2BAA2B,gCAAgC,0BAA0B,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,QAAQ,WAAW,YAAY,MAAM,GAAG,WAAW,SAAS,CAAC;AAAA,MACj9B,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,KAAK,CAAC;AAAA,UAClC,CAAC;AACD,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,kCAAkC,GAAG,CAAC;AAC7G,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,eAAe;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa,EAAE,EAAE,EAAE;AACtB,UAAG,WAAW,IAAI,mCAAmC,GAAG,GAAG,eAAe,CAAC;AAC3E,UAAG,WAAW,iBAAiB,SAAS,2DAA2D;AACjG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,MAAM,CAAC;AAAA,UACnC,CAAC,EAAE,UAAU,SAAS,oDAAoD;AACxE,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,YAAY,CAAC;AAAA,UACzC,CAAC,EAAE,UAAU,SAAS,oDAAoD;AACxE,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,MAAM,CAAC;AAAA,UACnC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,2BAA8B,YAAY,CAAC;AACjD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,MAAM,IAAI,QAAQ;AACjC,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,QAAQ,IAAI,CAAC;AACrC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,iCAAiC,IAAI,kBAAkB,EAAE,qCAAqC,IAAI,eAAe,EAAE,6BAA6B,IAAI,2BAA2B,wBAAwB,EAAE,2BAA2B,IAAI,SAAS,EAAE,gCAAgC,IAAI,UAAU,EAAE,4BAA4B,IAAI,aAAa;AAAA,QAChW;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,qBAAqB,OAAO;AAAA,MAC7D,QAAQ,CAAC,o2HAAs2H;AAAA,MAC/2H,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,oBAAoB,cAAc;AAAA,MAChD;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,YAAY,CAAC,oBAAoB,cAAc;AAAA,MAC/C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,SAAS,CAAC,kBAAkB,qBAAqB,OAAO;AAAA,MACxD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,o2HAAs2H;AAAA,IACj3H,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc,eAAe,iBAAiB,iBAAiB,WAAW,gBAAgB;AAAA,MACpG,SAAS,CAAC,qBAAqB,oBAAoB,WAAW,kBAAkB,iBAAiB,eAAe;AAAA,IAClH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,mCAAmC;AAAA,MAC/C,SAAS,CAAC,cAAc,eAAe,iBAAiB,iBAAiB,qBAAqB,oBAAoB,iBAAiB,eAAe;AAAA,IACpJ,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,eAAe,iBAAiB,iBAAiB,WAAW,gBAAgB;AAAA,MACpG,SAAS,CAAC,qBAAqB,oBAAoB,WAAW,kBAAkB,iBAAiB,eAAe;AAAA,MAChH,WAAW,CAAC,mCAAmC;AAAA,IACjD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}