<div class="heading  d-flex justify-content-between   px-3 py-4 align-items-center">
  <div style="font-size: 1.2rem;">
    Load type:- {{data.settings.BcToSql}}
  </div>
  <div class="position-absolute d-flex gap-3" style="right: 1rem">
    <button (click)="executeJob(data.integrationId)" class="btn btn-primary" style="
        background-color: var(--bg-color);
        display: flex;
        gap: 5px;
        align-items: center;
      " [disabled]="isExecutingOnly">
      Execute Now<span *ngIf="isExecutingOnly" class="loader"><i class="pi pi-spin pi-spinner"
          style="font-size: 1.2rem"></i></span>
    </button>
  </div>
</div>
@if (joblogsData.length) {

<div class="px-3 pb-4">
  <table style="width: 100%; border: 1px solid black; border-collapse: collapse; text-align: left;">
    <thead>
      <tr style="border-bottom: 1px solid black;">
        <th style="padding: 8px; border-right: 1px solid black;">Start Time</th>
        <th style="padding: 8px; border-right: 1px solid black;">End Time</th>
        <th style="padding: 8px; border-right: 1px solid black;">Record Count</th>
        <th style="padding: 8px;border-right: 1px solid black;">Status</th>
        <th style="padding: 8px; ">Time Taken</th>
      </tr>
    </thead>
    @for (item of joblogsData; track $index) {

    <tbody>
      <tr [ngClass]="{
        'errorRow': item.status === 'Error',
        'processingRow': item.status === 'Processing'
      }">
        <td style="padding: 8px; border-right: 1px solid black;">{{item.startTime| date:'dd MMM yyyy HH:mm:ss'}}</td>
        <td style="padding: 8px; border-right: 1px solid black;">{{item.endTime | date:'dd MMM yyyy HH:mm:ss'}}</td>
        <td style="padding: 8px; border-right: 1px solid black;">{{item.recordCount}}</td>
        <td style="padding: 8px; border-right: 1px solid black;">{{item.status}}</td>
        <td style="padding: 8px 0; text-align: center;">


          
          @if(item.status=='Processing') {
          <span
          >Processing</span>
          }@else {
          <span>{{timeDifferenceFormatted(item.startTime,item.endTime)}}</span>

          }


        </td>
      </tr>
    </tbody>
    }
  </table>
</div>
}@else {
<div class="d-flex align-items-center justify-content-center" style="height: 15rem;">
  <span style="font-size: 1.5rem;">This job has not run yet.</span>
</div>
}
