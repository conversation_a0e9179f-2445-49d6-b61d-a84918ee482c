<nav class="navbar">

  <div class="logo ">
    <!-- <button (click)="toggleMenu()" class=" toogleBtn" [ngClass]="{'isMenuOpen':isMenuOpen} "> -->
      <!-- <button (click)="toggleMenu()" class="toggleBtn" [ngClass]="{'isMenuOpen': isMenuOpen}"> -->

      <!-- <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 24 24">
        <path fill="none" stroke="#085e81" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
          d="M12 6.826A.913.913 0 1 0 12 5a.913.913 0 0 0 0 1.826m6.088 0a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826m-12.176 0a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826M12 12.913a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826m6.088 0a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826m-12.176 0a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826M12 19a.913.913 0 1 0 0-1.826A.913.913 0 0 0 12 19m6.088 0a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826M5.912 19a.913.913 0 1 0 0-1.826a.913.913 0 0 0 0 1.826" />
      </svg> -->
    <!-- </button> -->
    <span [routerLink]="['/sync-hub']" style="cursor: pointer;">Admin Hub</span>
    <span style="font-size: 0.8em; color: gray; display: inline-block; margin-top: 7px; margin-left: -8px;">v2.1.8</span>
  </div>

  <div *ngIf="isMenuOpen" class="app-menu p-3">
    <!-- Main App Grid -->
    <div class="d-flex gap-3">
      <div class="app-item" *ngFor="let app of apps" [routerLink]="app.url" routerLinkActive="active"
        (click)="toggleMenu()">
        <i [ngClass]="app.icon"></i>
        <span>{{ app.name }}</span>
      </div>
    </div>

  </div>

  <!-- <p-autoComplete [(ngModel)]="selectedItems" [suggestions]="items" (completeMethod)="search($event)" [multiple]="true"
    placeholder="Search items..."></p-autoComplete> -->


  <div class="nav-icons">
    <div class="notifications">
      <i class="fa-brands fa-rocketchat " (click)="toggleChat()"></i>
    </div>
    <div class="notifications" style="margin-right: 0;">
      <a  [routerLink]="['/sync-hub/settings']">

        <i class="fa-solid fa-gear"></i>
      </a>
    </div>

    <!-- <div class="notifications">
      <i class="fa-brands fa-square-snapchat" (click)="notificationDialog()"></i>
      <span class="notification-count">5</span>
    </div> -->
    <!-- @if (user) {
    <div class="user-profile" (click)="toggleProfileMenu()">
      <i class="fa-solid fa-circle-user userLogo"></i>
      <span>{{user.name}}</span>
    </div>
    }@else{
    <li [routerLink]="['/login']" routerLinkActive="active" class="nav-link">Login</li>
    } -->
  </div>
</nav>
