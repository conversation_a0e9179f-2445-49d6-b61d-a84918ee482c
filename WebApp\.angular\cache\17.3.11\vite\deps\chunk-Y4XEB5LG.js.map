{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-ripple.mjs"], "sourcesContent": ["import { isPlatformBrowser, DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, Directive, Inject, Optional, NgModule } from '@angular/core';\nimport { DomHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\n\n/**\n * Ripple directive adds ripple effect to the host element.\n * @group Components\n */\nclass Ripple {\n  document;\n  platformId;\n  renderer;\n  el;\n  zone;\n  config;\n  constructor(document, platformId, renderer, el, zone, config) {\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n  }\n  animationListener;\n  mouseDownListener;\n  timeout;\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.config && this.config.ripple) {\n        this.zone.runOutsideAngular(() => {\n          this.create();\n          this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n        });\n      }\n    }\n  }\n  onMouseDown(event) {\n    let ink = this.getInk();\n    if (!ink || this.document.defaultView?.getComputedStyle(ink, null).display === 'none') {\n      return;\n    }\n    DomHandler.removeClass(ink, 'p-ink-active');\n    if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n      let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n      ink.style.height = d + 'px';\n      ink.style.width = d + 'px';\n    }\n    let offset = DomHandler.getOffset(this.el.nativeElement);\n    let x = event.pageX - offset.left + this.document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n    let y = event.pageY - offset.top + this.document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n    this.renderer.setStyle(ink, 'top', y + 'px');\n    this.renderer.setStyle(ink, 'left', x + 'px');\n    DomHandler.addClass(ink, 'p-ink-active');\n    this.timeout = setTimeout(() => {\n      let ink = this.getInk();\n      if (ink) {\n        DomHandler.removeClass(ink, 'p-ink-active');\n      }\n    }, 401);\n  }\n  getInk() {\n    const children = this.el.nativeElement.children;\n    for (let i = 0; i < children.length; i++) {\n      if (typeof children[i].className === 'string' && children[i].className.indexOf('p-ink') !== -1) {\n        return children[i];\n      }\n    }\n    return null;\n  }\n  resetInk() {\n    let ink = this.getInk();\n    if (ink) {\n      DomHandler.removeClass(ink, 'p-ink-active');\n    }\n  }\n  onAnimationEnd(event) {\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n    }\n    DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n  }\n  create() {\n    let ink = this.renderer.createElement('span');\n    this.renderer.addClass(ink, 'p-ink');\n    this.renderer.appendChild(this.el.nativeElement, ink);\n    this.renderer.setAttribute(ink, 'aria-hidden', 'true');\n    this.renderer.setAttribute(ink, 'role', 'presentation');\n    if (!this.animationListener) {\n      this.animationListener = this.renderer.listen(ink, 'animationend', this.onAnimationEnd.bind(this));\n    }\n  }\n  remove() {\n    let ink = this.getInk();\n    if (ink) {\n      this.mouseDownListener && this.mouseDownListener();\n      this.animationListener && this.animationListener();\n      this.mouseDownListener = null;\n      this.animationListener = null;\n      DomHandler.removeElement(ink);\n    }\n  }\n  ngOnDestroy() {\n    if (this.config && this.config.ripple) {\n      this.remove();\n    }\n  }\n  static ɵfac = function Ripple_Factory(t) {\n    return new (t || Ripple)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Ripple,\n    selectors: [[\"\", \"pRipple\", \"\"]],\n    hostAttrs: [1, \"p-ripple\", \"p-element\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Ripple, [{\n    type: Directive,\n    args: [{\n      selector: '[pRipple]',\n      standalone: true,\n      host: {\n        class: 'p-ripple p-element'\n      }\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.PrimeNGConfig,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nclass RippleModule {\n  static ɵfac = function RippleModule_Factory(t) {\n    return new (t || RippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RippleModule,\n    imports: [Ripple],\n    exports: [Ripple]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Ripple],\n      exports: [Ripple]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,UAAU,YAAY,UAAU,IAAI,MAAM,QAAQ;AAC5D,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,UAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,aAAK,KAAK,kBAAkB,MAAM;AAChC,eAAK,OAAO;AACZ,eAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,GAAG,eAAe,aAAa,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,QAC/G,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,CAAC,OAAO,KAAK,SAAS,aAAa,iBAAiB,KAAK,IAAI,EAAE,YAAY,QAAQ;AACrF;AAAA,IACF;AACA,eAAW,YAAY,KAAK,cAAc;AAC1C,QAAI,CAAC,WAAW,UAAU,GAAG,KAAK,CAAC,WAAW,SAAS,GAAG,GAAG;AAC3D,UAAI,IAAI,KAAK,IAAI,WAAW,cAAc,KAAK,GAAG,aAAa,GAAG,WAAW,eAAe,KAAK,GAAG,aAAa,CAAC;AAClH,UAAI,MAAM,SAAS,IAAI;AACvB,UAAI,MAAM,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,SAAS,WAAW,UAAU,KAAK,GAAG,aAAa;AACvD,QAAI,IAAI,MAAM,QAAQ,OAAO,OAAO,KAAK,SAAS,KAAK,YAAY,WAAW,SAAS,GAAG,IAAI;AAC9F,QAAI,IAAI,MAAM,QAAQ,OAAO,MAAM,KAAK,SAAS,KAAK,aAAa,WAAW,UAAU,GAAG,IAAI;AAC/F,SAAK,SAAS,SAAS,KAAK,OAAO,IAAI,IAAI;AAC3C,SAAK,SAAS,SAAS,KAAK,QAAQ,IAAI,IAAI;AAC5C,eAAW,SAAS,KAAK,cAAc;AACvC,SAAK,UAAU,WAAW,MAAM;AAC9B,UAAIA,OAAM,KAAK,OAAO;AACtB,UAAIA,MAAK;AACP,mBAAW,YAAYA,MAAK,cAAc;AAAA,MAC5C;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAAA,EACA,SAAS;AACP,UAAM,WAAW,KAAK,GAAG,cAAc;AACvC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,OAAO,SAAS,CAAC,EAAE,cAAc,YAAY,SAAS,CAAC,EAAE,UAAU,QAAQ,OAAO,MAAM,IAAI;AAC9F,eAAO,SAAS,CAAC;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,KAAK;AACP,iBAAW,YAAY,KAAK,cAAc;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,SAAS;AAChB,mBAAa,KAAK,OAAO;AAAA,IAC3B;AACA,eAAW,YAAY,MAAM,eAAe,cAAc;AAAA,EAC5D;AAAA,EACA,SAAS;AACP,QAAI,MAAM,KAAK,SAAS,cAAc,MAAM;AAC5C,SAAK,SAAS,SAAS,KAAK,OAAO;AACnC,SAAK,SAAS,YAAY,KAAK,GAAG,eAAe,GAAG;AACpD,SAAK,SAAS,aAAa,KAAK,eAAe,MAAM;AACrD,SAAK,SAAS,aAAa,KAAK,QAAQ,cAAc;AACtD,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,gBAAgB,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,IACnG;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,KAAK;AACP,WAAK,qBAAqB,KAAK,kBAAkB;AACjD,WAAK,qBAAqB,KAAK,kBAAkB;AACjD,WAAK,oBAAoB;AACzB,WAAK,oBAAoB;AACzB,iBAAW,cAAc,GAAG;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,GAAG;AACvC,WAAO,KAAK,KAAK,SAAW,kBAAkB,QAAQ,GAAM,kBAAkB,WAAW,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,eAAe,CAAC,CAAC;AAAA,EACjP;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,YAAY,WAAW;AAAA,IACtC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,GAAG;AAC7C,WAAO,KAAK,KAAK,eAAc;AAAA,EACjC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,MAAM;AAAA,IAChB,SAAS,CAAC,MAAM;AAAA,EAClB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,MAAM;AAAA,MAChB,SAAS,CAAC,MAAM;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ink"]}