﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Repositiories
{
    public interface IDbConnectionRepository
    {
        Task<List<DbConnectionDto>> GetAllConnections();
        Task<DbConnectionDto> GetConnection(Guid guid);
        Task<DbConnectionDto> CreateOrEdit(DbConnectionDto request);
        Task<ResponseMessage> Delete(Guid guid);
        Task<ODataConnectionDetailDto> GetODataConnectionDetailAsync(Guid guid);
        Dictionary<string, string> GetLastModifiedFilter(string integrationId);
        int InsertJobLog(string processId);
        void UpdateJobLog(int logId, int recordCount, long maxTimestamp, string status);
        long GetLastModifiedTimestamp(string integrationId);
        Task<List<JobLogEntry>> GetTop10JobLogs(string integrationId);
    }
}
