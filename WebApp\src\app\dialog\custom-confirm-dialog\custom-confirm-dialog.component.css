.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-content {
  padding: 2em; 
  width: 400px;
  max-width: 90%;
  text-align: center;
}

.dialog-content h2 {
  font-size: 1.5em;
  margin-bottom: 1em;
  color: #333;
}

.dialog-content p {
  font-size: 1em;
  margin-bottom: 1.5em;
  color: #666;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  gap: 1em;
}

.dialog-actions button {
  padding: 0.75em 1.5em;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.3s, color 0.3s;
}

.dialog-actions button:hover {
  opacity: 0.9;
}

.dialog-actions button:first-child {
  background-color: #f5f5f5;
  color: #333;
}

.dialog-actions button:last-child {
  background-color: #007bff;
  color: white;
}

.dialog-actions button:last-child:hover {
  background-color: #0056b3;
}
