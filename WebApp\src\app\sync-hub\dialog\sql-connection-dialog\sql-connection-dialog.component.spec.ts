import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SqlConnectionDialogComponent } from './sql-connection-dialog.component';

describe('SqlConnectionDialogComponent', () => {
  let component: SqlConnectionDialogComponent;
  let fixture: ComponentFixture<SqlConnectionDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SqlConnectionDialogComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(SqlConnectionDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
