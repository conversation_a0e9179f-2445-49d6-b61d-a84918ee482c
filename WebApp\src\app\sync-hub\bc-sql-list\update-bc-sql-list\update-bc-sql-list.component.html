<div class="card flex justify-content-center p-2">
<div class="d-flex align-items-center ">

  <button class="backBtn btn" (click)="goBack()"><i class="fa-solid fa-arrow-left"></i> </button>
  <h2 style="color: rgb(75, 85, 99);">{{intergrationData.integrationName}}</h2>
</div>
  <p-stepper style="flex-basis: auto" [activeStep]="3">
    <p-stepperPanel header="Busicess central" >
      <ng-template pTemplate="content" let-nextCallback="nextCallback" let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium w-100">
            <div class="form-section d-flex flex-column gap-4">
              <div>
                <h2>Select Source</h2>
                <p-dropdown [style]="{ width: '100%' }" class="dropdown" [options]="bcConnections"
                  optionLabel="sourceDatabase" [(ngModel)]="selectedSourceConnection" optionLabel="connectionName"
                  placeholder="Select Source" (onChange)="sourceConnectionChange()" />
              </div>
              <div>
                <h2>Select Companies</h2>
                <p-dropdown [style]="{ width: '100%' }" class="dropdown" [options]="companyNames"
                  [(ngModel)]="intergrationData.sourceDatabase" placeholder="Select Companies" />
              </div>
              <div>
                <h2>Select Entities</h2>
                <p-dropdown [style]="{ width: '100%' }" class="dropdown" [options]="entityNames"
                  [(ngModel)]="intergrationData.sourceTable" placeholder="Select Entities"  filter="true"
                  filterPlaceholder="Search..."  />
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-end w-100">
          <button class="btn btn-primary" style="background-color: var(--bg-color)" (click)="nextCallback.emit()">
            Next <i class="pi pi-arrow-right"></i>
          </button>
        </div>
      </ng-template>
    </p-stepperPanel>
    <p-stepperPanel header="Destination">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-nextCallback="nextCallback"
        let-index="index">
        <div class="d-flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium gap-2">
            <div class="form-section d-flex flex-column gap-2">
              <div>
                <h2>Select Destination Connection</h2>
                <p-dropdown [style]="{ width: '100%' }" class="dropdown" [options]="sqlConnections"
                  [(ngModel)]="selectedDestinationConnection" (onChange)="destinationConnectionChange()"
                  optionLabel="connectionName" placeholder="Select a City" />
              </div>
              <div>
                <h2>Select Destination Database</h2>
                <p-dropdown [style]="{ width: '100%' }" class="dropdown" [options]="destinationDatabases"
                  [(ngModel)]="intergrationData.destinationDatabase" placeholder="Select a City" />
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-between w-100">
          <button class="btn btn-secondary" (click)="prevCallback.emit()">
            <i class="pi pi-arrow-left"></i> Back
          </button>
          <button class="btn btn-primary" style="background-color: var(--bg-color)" (click)="nextCallback.emit()">
            Next <i class="pi pi-arrow-right"></i>
          </button>
        </div>
      </ng-template>
    </p-stepperPanel>
    <p-stepperPanel header="Mapping Columns">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-nextCallback="nextCallback"
        let-index="index">
        <div class="flex flex-column h-12rem">
          <div class="border-2 border-dashed surface-border border-round surface-ground flex-auto">
            <div class="form-section">
              <h2>Map Columns</h2>
              <table class="p-table m-2 w-100 map-col">
                <thead>
                  <tr>
                    <th>Source Column</th>
                    <th>Destination Column</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let mapping of columnMappings; let i = index">
                    <td>
                      <p-dropdown [options]="filteredSourceColumns(i)" (onFocus)="columnFocus(mapping.source)"
                      [(ngModel)]="mapping.source" (ngModelChange)="onSourceChange($event, i)"
                        placeholder="Select Source Column" [style]="{ width: '100%' }"
                        filter="true"
                        filterPlaceholder="Search..." >
                      </p-dropdown>
                    </td>
                    <td>
                      <p-dropdown [options]="filteredDestinationColumns(i)" (onFocus)="columnFocus(mapping.source)"
                      [(ngModel)]="mapping.destination" (ngModelChange)="onDestinationChange($event, i)"
                        placeholder="Select Destination Column" [style]="{ width: '100%' }"
                        filter="true"
                        filterPlaceholder="Search..." >
                      </p-dropdown>

                        <i (click)="removeMapping(i)" class="fa-solid fa-delete-left" style="color: red; margin-top: 8px; font-size: 1rem;"></i>
                    </td>
                  </tr>
                </tbody>
              </table>
              <p-button (click)="addMapping()" class="mt-2" [style]="{ backgroundColor: 'var(--bg-color)' }">
                <i class="fa-solid fa-plus fs-5"></i>
              </p-button>
            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-between">
          <button class="btn btn-secondary" (click)="prevCallback.emit()">
            <i class="pi pi-arrow-left"></i>Back
          </button>
          <button class="btn btn-primary" style="background-color: var(--bg-color)" (click)="nextCallback.emit()">
            Next <i class="pi pi-arrow-right"></i>
          </button>
        </div>
      </ng-template>
    </p-stepperPanel>
    <p-stepperPanel header="Settings">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-index="index">
        <div class="flex flex-column h-auto gap-5">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
            <div class="form-section d-flex flex-column gap-3">
              <div class="position-relative">
                <h2>Change Tracking</h2>
                <div class="d-flex gap-5">
                  <div style="background-color: #f3f3f3" class="p-3 rounded">
                    <h3>BC To SQL</h3>
                    <div class="d-flex flex-column gap-2">
                      <div class="v-cen" style="gap: 10px">
                        <p-radioButton name="Real-time" value="Real-time" [(ngModel)]="integrationSettings.BcToSql"
                          inputId="Real-time" (onClick)="onBcToSqlSelect('Real-time')" [ngClass]="{
                            disabled: selectedConnectionType === 'BCODataWebService'
                          }" />
                        <label for="Real-time" class="ml-2" [ngClass]="{
                          disabled: selectedConnectionType === 'BCODataWebService'
                        }"> Real-time </label>
                      </div>
                      <div class="v-cen" style="gap: 10px">
                        <p-radioButton name="Clean Load" value="Clean Load" [(ngModel)]="integrationSettings.BcToSql"
                          inputId="Clean Load" (onClick)="onBcToSqlSelect('Clean Load')" />
                        <label for="Clean Load" class="ml-2">
                          Clean Load
                        </label>
                      </div>
                      <div class="v-cen" style="gap: 10px">
                        <p-radioButton name="Differential Load" value="Differential Load"
                          [(ngModel)]="integrationSettings.BcToSql" inputId="Differential Load"
                          (onClick)="onBcToSqlSelect('Differential Load')" />
                        <label for="Differential Load" class="ml-2">
                          Differential Load
                        </label>
                      </div>
                    </div>
                  </div>
                  <div style="background-color: #f3f3f3" class="p-3 rounded px-5">
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 10px;
                        align-items: center;
                      ">
                      <h4>SQL To BC</h4>

                      <p-inputSwitch [(ngModel)]="integrationSettings.SqlToBc" />
                    </div>
                  </div>
                  <div class="position-absolute d-flex gap-3" style="right: 0">


                    <button (click)="executeJob(intergrationData.guid)" class="btn btn-primary" style="
                        background-color: var(--bg-color);
                        display: flex;
                        gap: 5px;
                        align-items: center;
                      " [disabled]="isExecutingOnly">
                      Execute<span *ngIf="isExecutingOnly" class="loader"><i class="pi pi-spin pi-spinner"
                          style="font-size: 1.2rem"></i></span>
                    </button>
                    <button class="custom-dropdown-toggle d-flex align-items-center gap-2" (click)="refreshEntities()"
                    style="background-color: var(--bg-color); color: white; border-radius: 5px;">
                   Refresh Metadata
                   <span *ngIf="isRefreshing" class="loader"><i class="pi pi-spin pi-spinner"
                    style="font-size: 1.2rem"></i></span>
                  </button>
                    <button class="custom-dropdown-toggle d-flex align-items-center gap-2" (click)="onAddView()"
                      style="background-color: var(--bg-color); color: white; border-radius: 5px;">
                      Add View
                    </button>
                  </div>
                </div>
                <div style="width: 74%;">

                  <h2>Schedule Time</h2>
                  <p-dropdown class="dropdown w-50" [options]="jobTypes"
                    [(ngModel)]="this.intergrationData.jobFrequency" placeholder="Select Job Frequency"
                    [disabled]="isRealTimeChecked" />
                </div>
              </div>
              <div *ngIf="!(existingEntities.length == 0)" class="my-2 text-danger">
                @for (existingEntitie of existingEntities; track $index) {
                <p>
                  Companies = {{ existingEntitie.split("_")[0] }}, Entities =
                  {{ existingEntitie.split("_")[1] }}
                </p>
                } is already exist
              </div>
            </div>

            <div class="form-group mt-3 d-flex justify-content-between align-items-center">
              <div class="flex justify-content-start">
                <button class="btn btn-secondary" (click)="prevCallback.emit()">
                  <i class="pi pi-arrow-left"></i>
                  Back
                </button>
              </div>
              <div class="custom-dropdown-container">
                <button class="custom-dropdown-toggle d-flex align-items-center gap-2" (click)="toggleDropdown()" style="
                    background-color: var(--bg-color);
                    color: white;
                    border-radius: 5px;
                  ">
                  Save & execute <i class="pi pi-angle-down"></i>
                </button>
                <ul class="custom-dropdown-menu d-flex flex-column gap-1" *ngIf="isDropdownOpen">
                  <li class="custom-dropdown-item" (click)="onSaveAndExecute()"
                    style="background-color: var(--bg-color); color: white">
                    Save & execute
                    <span *ngIf="isExecuting" class="loader"><i class="pi pi-spin pi-spinner"
                        style="font-size: 1.2rem"></i></span>
                  </li>
                  <li class="custom-dropdown-item" (click)="onSave()"
                    style="background-color: var(--bg-color); color: white">
                    Save
                    <span *ngIf="isSaving" class="loader"><i class="pi pi-spin pi-spinner"
                        style="font-size: 1.2rem"></i></span>
                  </li>
                </ul>
              </div>
              <!-- <select
                class="btn btn-primary text-start"
                style="background-color: var(--bg-color)"
                [disabled]="isSaving"
                (change)="saveData($event)"
                value="save"
              >
                <option value="save-exquite" style="border: 2px solid white" >
                  Save and exquite
                  <span *ngIf="isSaving" class="loader"
                    ><i
                      class="pi pi-spin pi-spinner"
                      style="font-size: 1.2rem"
                    ></i
                  ></span>
                </option>
                <option value="save">
                  Save
                  <span *ngIf="isSaving" class="loader"
                    ><i
                      class="pi pi-spin pi-spinner"
                      style="font-size: 1.2rem"
                    ></i
                  ></span>
                </option>
              </select> -->
            </div>
          </div>
        </div>

      </ng-template>
    </p-stepperPanel>
  </p-stepper>
  <div class="ms-4" *ngIf="singlarMessage.length">
    <div class="row">
      <div class="col-9">

        Message: <button class="action-button delete-button btn" (click)="clearMessage()" title="Delete"
        style="margin-left: 5px;">
        <i class="fas fa-trash-alt"></i>
      </button>
        @for (message of singlarMessage; track $index) {

        <div [ngClass]="
        message.includes('Success: ')
        ? 'text-success'
        : message.includes('Error:')
        ? 'text-danger'
        : 'text-black'
        " style="margin-top: 5px;">{{ message }}</div>
        }
      </div>

    </div>

  </div>

</div>
