C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\appsettings.Development.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\appsettings.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.WebApi.exe
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.WebApi.deps.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.WebApi.runtimeconfig.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.WebApi.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AutoMapper.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.AI.ContentSafety.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.AI.FormRecognizer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.AI.OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.Identity.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.Search.Documents.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.Storage.Blobs.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.Storage.Common.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Azure.Storage.Queues.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ClosedXML.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ClosedXML.Parser.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Dapper.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\DnsClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\DocumentFormat.OpenXml.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\DocumentFormat.OpenXml.Framework.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Elastic.Clients.Elasticsearch.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Elastic.Transport.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ExcelNumberFormat.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\FluentValidation.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Google.Protobuf.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Hangfire.AspNetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Hangfire.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Hangfire.NetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Hangfire.SqlServer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\HtmlAgilityPack.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\LLamaSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MediatR.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MediatR.Contracts.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Bcl.HashCode.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Bcl.TimeProvider.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.AI.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Diagnostics.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Http.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.EventLog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Logging.EventSource.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Extensions.VectorData.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.All.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.AI.Anthropic.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.AI.AzureOpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.AI.LlamaSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.AI.Ollama.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.AI.Onnx.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.AI.OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.DocumentStorage.AWSS3.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.Postgres.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.MemoryDb.Qdrant.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.MemoryDb.Redis.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.MemoryDb.SQLServer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.MongoDbAtlas.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.Orchestration.AzureQueues.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.Orchestration.RabbitMQ.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.SemanticKernelPlugin.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.KernelMemory.WebClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.ML.OnnxRuntimeGenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.ML.Tokenizers.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.OData.Client.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.OData.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.OData.Edm.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SemanticKernel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SemanticKernel.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SemanticKernel.Connectors.OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SemanticKernel.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SemanticKernel.Plugins.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Spatial.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MongoDB.Driver.GridFS.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\NetTopologySuite.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Npgsql.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\NRedisStack.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\OllamaSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.DocumentLayoutAnalysis.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.Fonts.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.Package.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.Tokenization.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.Tokens.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\UglyToad.PdfPig.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Pgvector.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Polly.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\RBush.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\SendGrid.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Sinks.MSSqlServer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\SharpCompress.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Snappier.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\StackExchange.Redis.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\StarkbankEcdsa.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Stripe.net.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.ClientModel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.IO.Hashing.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.IO.Packaging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Linq.Async.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Memory.Data.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Numerics.Tensors.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ZstdSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\de\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\es\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\fr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\it\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ja\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ko\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\pt-BR\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\ru\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\zh-Hans\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\zh-Hant\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\onnxruntime.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\onnxruntime.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\mongocrypt.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\sni.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.Infrastructure.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.Core.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.Infrastructure.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.AssemblyInfo.cs
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.sourcelink.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets.build.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets.development.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets\msbuild.AdminPortalBackend.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets\msbuild.build.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets\msbuild.buildMultiTargeting.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets\msbuild.buildTransitive.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets.pack.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\scopedcss\bundle\AdminPortalBackend.WebApi.styles.css
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPor.E4811F96.Up2Date
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\refint\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\AdminPortalBackend.WebApi.genruntimeconfig.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\ref\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\Hangfire.MaximumConcurrentExecutions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Release\net8.0\win-x64\AdminPortalBackend.WebApi.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets\msbuild.AdminPortalBackend.WebApi.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Release\net8.0\win-x64\staticwebassets.upToDateCheck.txt
