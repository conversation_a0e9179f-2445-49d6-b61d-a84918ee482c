<div class="card p-2">
  <div class="d-flex align-items-center ">

    <button class="backBtn btn" (click)="goBack()"><i class="fa-solid fa-arrow-left"></i> </button>
    <h2 style="color: rgb(75, 85, 99);
    font-size: 16px;
    margin: 0;">{{connectionData.integrationName}}</h2>
  </div>
  <p-stepper [linear]="false" [activeStep]="3">
    <p-stepperPanel header="Source">
      <ng-template pTemplate="content" let-nextCallback="nextCallback" let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
            <div class="form-section">
              <div class="form-section">
                <p-floatLabel>
                  <p style="font-weight: bold;">Integration Name</p>
                  <input id="username" type="text" pInputText [(ngModel)]="connectionData.integrationName"
                    [style]="{'width':'100%'}" />
                </p-floatLabel>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Source Connection</p>
                <p-dropdown [options]="connections" optionLabel="connectionName" [(ngModel)]="sourceConnection"
                  [style]="{'width':'100%'}" (onChange)="onSourceConnectionSelected($event.value)"
                  placeholder="Select Source Connection">
                </p-dropdown>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Source Database</p>
                <p-dropdown [options]="databases" [(ngModel)]="sourceDatase" [style]="{'width':'100%'}"
                  (onChange)="onSourceDatabaseSelected($event.value)" placeholder="Select Source Database" filter="true"
                  filterPlaceholder="Search...">
                </p-dropdown>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Source Tables</p>
                <p-dropdown [options]="tables" [(ngModel)]="sourceTable" [style]="{'width':'100%'}"
                  (onChange)="onSourceTableSelected($event.value)" placeholder="Select Source Tables" filter="true"
                  filterPlaceholder="Search...">
                </p-dropdown>
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-end">
          <button class="btn btn-primary" style="background-color: var(--bg-color);" (click)="nextCallback.emit()">Next
            <i class="pi pi-arrow-right"></i></button>
        </div>
      </ng-template>
    </p-stepperPanel>
    <p-stepperPanel header="Destination">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-nextCallback="nextCallback"
        let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
            <div class="form-section">

              <div class="form-section">
                <p style="font-weight: bold;">Select Destination Connection</p>
                <p-dropdown [options]="connections" optionLabel="connectionName" [(ngModel)]="destinationConnection"
                  (onChange)="onDestinationConnectionSelected($event.value)" placeholder="Select Connection"
                  [style]="{'width':'100%'}">
                </p-dropdown>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Database</p>
                <p-dropdown [options]="destinationDatabases" [(ngModel)]="destinationDatase"
                  (onChange)="onDestinationDatabaseSelected($event.value)" placeholder="Select Database"
                  [style]="{'width':'100%'}" filter="true" filterPlaceholder="Search...">
                </p-dropdown>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Destination Table</p>
                <p-dropdown [options]="destinationTables" [(ngModel)]="destinationTable"
                  (onChange)="onDestinationTableSelected($event.value)" placeholder="Select Destination Table"
                  [style]="{'width':'100%'}" filter="true" filterPlaceholder="Search...">
                </p-dropdown>
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-between">
          <button class="btn btn-secondary" (click)="prevCallback.emit()"><i class="pi pi-arrow-left"></i>Back</button>
          <button class="btn btn-primary" style="background-color: var(--bg-color);" (click)="nextCallback.emit()">Next
            <i class="pi pi-arrow-right"></i></button>
        </div>
      </ng-template>
    </p-stepperPanel>
    <p-stepperPanel header="Mapping Columns">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-nextCallback="nextCallback"
        let-index="index">
        <div class="flex flex-column h-12rem" id="mapping">
          <div class="border-2 border-dashed surface-border border-round surface-ground flex-auto">
            <div class="form-section">
              <h2>Map Columns</h2>
              <div class="mapping-section mt-2 w-100">
                <div class="p-field p-col-12  " style="width: 33rem;">
                  <p style="font-weight: bold; margin-bottom: 2px;">Source PrimaryKey</p>
                  <div class="mapping-cell same-width">
                    <!-- PrimeNG MultiSelect -->
                    <p-multiSelect [options]="sourceColumns" [(ngModel)]="sourcePrimaryKey"
                      placeholder="Select Source Tables" class="w-100" (onChange)="addSourcePrimaryKey($event.value)">
                    </p-multiSelect>

                    <!-- PrimeNG Chips to display selected tables -->
                    <p-chips [(ngModel)]="selectedSourcePrimaryKey" [removable]="true"
                      (onRemove)="removeSourcePrimaryKey($event)" [style]="{'margin-top': '10px'}">
                    </p-chips>
                  </div>
                </div>

                <!-- Destination Primary Key Dropdown -->
                <div class="p-field p-col-12 " style="width: 33rem;">

                  <p style="font-weight: bold; margin-bottom:2px;">Destination PrimaryKey</p>

                  <div class="mapping-cell same-width">
                    <!-- PrimeNG MultiSelect -->
                    <p-multiSelect [options]="destinationColumns" [(ngModel)]="destinationPrimaryKey"
                      placeholder="Select Source Tables" class="w-100"
                      (onChange)="addDestinationPrimaryKey($event.value)">
                    </p-multiSelect>

                    <!-- PrimeNG Chips to display selected tables -->
                    <p-chips [(ngModel)]="selectedDestinationPrimaryKey" [removable]="true"
                      (onRemove)="removeDestinationPrimaryKey($event)" [style]="{'margin-top': '10px'}">
                    </p-chips>
                  </div>
                </div>
              </div>
              <table class="p-table m-2 w-100">
                <thead>
                  <tr>
                    <th>Source Column</th>
                    <th>Destination Column</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let mapping of columnMappings; let i = index">
                    <td  style="width: 33rem;">
                      <p-dropdown [options]="filteredSourceColumns(i)" (onFocus)="columnFocus(mapping.source)"
                        [(ngModel)]="mapping.source" (ngModelChange)="onSourceChange($event, i)"
                        placeholder="Select Source Column" [style]="{'width':'100%'}" filter="true"
                        filterPlaceholder="Search...">
                      </p-dropdown>

                    </td>
                    <td  style="width: 33rem;">
                      <p-dropdown [options]="filteredDestinationColumns(i)" (onFocus)="columnFocus(mapping.source)"
                        [(ngModel)]="mapping.destination" (ngModelChange)="onDestinationChange($event, i)"
                        placeholder="Select Destination Column" [style]="{'width':'100%'}" filter="true"
                        filterPlaceholder="Search...">
                      </p-dropdown>
                    </td>
                    <td>
                      <button pButton class="btn" (click)="removeMapping(i)" style="color: red;  font-size: 1rem;">
                        <i class="fa-solid fa-delete-left"></i>
                      </button>
                    </td>
                  </tr>

                </tbody>
              </table>
              <p-button (click)="addMapping()" class="mt-2">
                <i class="fa-solid fa-plus fs-5"></i>
              </p-button>





            </div>
          </div>
        </div>




        <div class="d-flex pt-4 justify-content-between">
          <button class="btn btn-secondary" (click)="prevCallback.emit()"><i class="pi pi-arrow-left"></i> Back</button>
          <button class="btn btn-primary" style="background-color: var(--bg-color);" (click)="nextCallback.emit()">Next
            <i class="pi pi-arrow-right"></i></button>
        </div>
      </ng-template>
    </p-stepperPanel>

    <p-stepperPanel header="Schedule Job" style="flex-basis: 18rem;">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
            <p style="font-weight: bold;">Change Tracking</p>
            <div style="background-color: #f3f3f3; width: 40%;" class="p-3 rounded ">
              <div class="d-flex flex-column gap-2">
                <div class="v-cen" style="gap: 10px">
                  <p-radioButton name="Clean Load" value="Clean Load" inputId="Clean Load"
                    [(ngModel)]="integrationSettings.BcToSql" />
                  <!-- <p-radioButton name="Clean Load" value="Clean Load" [(ngModel)]="integrationSettings.BcToSql"
                      inputId="Clean Load" (onClick)="onBcToSqlSelect('Clean Load')" /> -->
                  <label for="Clean Load" class="ml-2">
                    Clean Load
                  </label>
                </div>
                <div class="v-cen" style="gap: 10px">
                  <p-radioButton name="Differential Load" value="Differential Load" inputId="Differential Load"
                    [(ngModel)]="integrationSettings.BcToSql" />
                  <label for="Differential Load" class="ml-2">
                    Differential Load
                  </label>
                </div>
              </div>
            </div>
            <div class="form-section d-flex align-items-center justify-content-between mt-3">

              <div class="w-50">

                <p style="font-weight: bold;">Job Frequency</p>
                <p-dropdown [options]="jobTypes" [(ngModel)]="connectionData.jobFrequency"
                  placeholder="Select Job Frequency" [style]="{'width':'100%'}">
                </p-dropdown>

              </div>
              <div>
                <button (click)="executeJob(connectionData.guid)" class="btn btn-primary" style="
                  background-color: var(--bg-color);
                  display: flex;
                  gap: 5px;
                  align-items: center;
                " [disabled]="isExecutingOnly">
                  Execute<span *ngIf="isExecutingOnly" class="loader"><i class="pi pi-spin pi-spinner"
                      style="font-size: 1.2rem"></i></span>
                </button>
              </div>
            </div>

            <div class="form-group mt-3 d-flex justify-content-between align-items-center">
              <div class="flex justify-content-start">
                <button class="btn btn-secondary" (click)="prevCallback.emit()">
                  <i class="pi pi-arrow-left"></i>
                  Back
                </button>
              </div>
              <div class="custom-dropdown-container">
                <button class="custom-dropdown-toggle" (click)="toggleDropdown()"
                  style="background-color: var(--bg-color); color: white; border-radius: 5px; display: flex; gap: 5px ; align-items: center;">
                  Save & execute<i class="pi pi-angle-down"></i>
                </button>
                <ul class="custom-dropdown-menu d-flex flex-column gap-1" *ngIf="isDropdownOpen">
                  <li class="custom-dropdown-item" (click)="onSaveAndExecute()"
                    style="background-color: var(--bg-color); color: white; border-radius: 5px;">
                    Save & execute <span *ngIf="isExecuting" class="loader"><i class="pi pi-spin pi-spinner"
                        style="font-size: 1.2rem"></i></span>
                  </li>
                  <li class="custom-dropdown-item" (click)="onSave()"
                    style="background-color: var(--bg-color); color: white; border-radius: 5px;">
                    Save <span *ngIf="isSaving" class="loader"><i class="pi pi-spin pi-spinner"
                        style="font-size: 1.2rem"></i></span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

      </ng-template>
    </p-stepperPanel>
  </p-stepper>

  <div class="ms-4" *ngIf="singlarMessage.length">
    <div class="row">
      <div class="col-9">
        <div>

          Message:
          <button class="action-button delete-button btn" (click)="clearMessage()" title="Delete"
            style="margin-left: 5px; transform: scale(1);">
            <i class="fas fa-trash-alt" style="transform: scale(1);"></i>
          </button>

        </div>
        @for (message of singlarMessage; track $index) {

        <div [ngClass]="
        message.includes('Success: ')
        ? 'text-success'
        : message.includes('Error:')
        ? 'text-danger'
        : 'text-black'
        " style="margin-top: 5px;">{{ message }}</div>
        }
      </div>

    </div>

  </div>

</div>
