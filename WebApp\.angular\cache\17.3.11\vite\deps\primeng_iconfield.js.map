{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-iconfield.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * IconField wraps an input and an icon.\n * @group Components\n */\nconst _c0 = [\"*\"];\nclass IconField {\n  /**\n   * Position of the icon.\n   * @group Props\n   */\n  iconPosition = 'left';\n  get containerClass() {\n    return {\n      'p-icon-field-left': this.iconPosition === 'left',\n      'p-icon-field-right': this.iconPosition === 'right'\n    };\n  }\n  static ɵfac = function IconField_Factory(t) {\n    return new (t || IconField)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: IconField,\n    selectors: [[\"p-iconField\"]],\n    inputs: {\n      iconPosition: \"iconPosition\"\n    },\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"p-icon-field\", 3, \"ngClass\"]],\n    template: function IconField_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass);\n      }\n    },\n    dependencies: [i1.NgClass],\n    styles: [\"@layer primeng{.p-icon-field{position:relative}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconField, [{\n    type: Component,\n    args: [{\n      selector: 'p-iconField',\n      template: ` <span class=\"p-icon-field\" [ngClass]=\"containerClass\"><ng-content></ng-content> </span>`,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\"@layer primeng{.p-icon-field{position:relative}}\\n\"]\n    }]\n  }], null, {\n    iconPosition: [{\n      type: Input\n    }]\n  });\n})();\nclass IconFieldModule {\n  static ɵfac = function IconFieldModule_Factory(t) {\n    return new (t || IconFieldModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: IconFieldModule,\n    declarations: [IconField],\n    imports: [CommonModule],\n    exports: [IconField, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [IconField, SharedModule],\n      declarations: [IconField]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IconField, IconFieldModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,eAAe;AAAA,EACf,IAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,qBAAqB,KAAK,iBAAiB;AAAA,MAC3C,sBAAsB,KAAK,iBAAiB;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,GAAG;AAC1C,WAAO,KAAK,KAAK,YAAW;AAAA,EAC9B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,QAAQ;AAAA,MACN,cAAc;AAAA,IAChB;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,SAAS,CAAC;AAAA,IAC1C,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,IAAI,cAAc;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,cAAc,CAAI,OAAO;AAAA,IACzB,QAAQ,CAAC,oDAAoD;AAAA,IAC7D,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,oDAAoD;AAAA,IAC/D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAiB;AAAA,EACpC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,SAAS;AAAA,IACxB,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,WAAW,YAAY;AAAA,EACnC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,YAAY;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,WAAW,YAAY;AAAA,MACjC,cAAc,CAAC,SAAS;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}