// signalr.service.ts
import { Inject, Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder } from '@microsoft/signalr';
import { Subject } from 'rxjs';
import { MessageType } from './message-types.enum';
import { API_BASE_URL } from '../shared/service-proxies/service-proxies';

@Injectable({
  providedIn: 'root',
})
export class SignalRService {
  private hubConnection: HubConnection;
  private messageSubject = new Subject<{ type: MessageType, message: string }>(); // For multiple types of messages
  public message$ = this.messageSubject.asObservable(); // Observable for subscribers

  constructor(@Inject(API_BASE_URL) baseUrl?: string) {
    this.hubConnection = new HubConnectionBuilder()
      .withUrl(baseUrl + '/messageHub') // Ensure correct URL to your SignalR Hub
      .build();

    this.startConnection();
    this.listenForMessages();
  }

  // Start the SignalR connection
  private async startConnection() {
    try {
      await this.hubConnection.start();
      console.log('SignalR connection established');
    } catch (err) {
      console.error('Error establishing SignalR connection:', err);
      setTimeout(() => this.startConnection(), 5000); // Retry connection after 5 seconds
    }
  }

  // Listen for different types of messages
  private listenForMessages() {
    // Listen for different types of messages by type
    this.hubConnection.on(MessageType.SendMessage, (message: string) => {
      console.log('Received message:', message);
      this.messageSubject.next({ type: MessageType.SendMessage, message });
    });

    this.hubConnection.on(MessageType.ReceiveNotification, (message: string) => {
      console.log('Received notification:', message);
      this.messageSubject.next({ type: MessageType.ReceiveNotification, message });
    });

    this.hubConnection.on(MessageType.ReceiveDataUpdate, (message: string) => {
      console.log('Received data update:', message);
      this.messageSubject.next({ type: MessageType.ReceiveDataUpdate, message });
    });

    // Add new listener for AI chunks
    this.hubConnection.on(MessageType.ReceiveAIChunk, (chunk: string) => {
      console.log('Received AI chunk:', chunk);
      this.messageSubject.next({ type: MessageType.ReceiveAIChunk, message: chunk });
    });
  }

  // Send a general message to the hub
  public sendMessage(message: string) {
    this.hubConnection.send(MessageType.SendMessage, message)
      .then(() => console.log('Message sent to server'))
      .catch(err => console.error('Error sending message:', err));
  }

  // Send a notification message to the hub
  public sendNotification(notification: string) {
    this.hubConnection.send(MessageType.ReceiveNotification, notification)
      .then(() => console.log('Notification sent to server'))
      .catch(err => console.error('Error sending notification:', err));
  }

  // Send updated data to the hub
  public sendDataUpdate(data: string) {
    this.hubConnection.send(MessageType.ReceiveDataUpdate, data)
      .then(() => console.log('Data update sent to server'))
      .catch(err => console.error('Error sending data update:', err));
  }

  // Add method to start AI conversation
  public startAIConversation(question: string) {
    this.hubConnection.send('StartAIConversation', question)
      .then(() => console.log('AI conversation started'))
      .catch(err => console.error('Error starting AI conversation:', err));
  }
}
