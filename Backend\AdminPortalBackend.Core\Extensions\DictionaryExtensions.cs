﻿using System.Data;

namespace AdminPortalBackend.Core.Extensions;

public static class DictionaryExtensions
{
    public static DataTable ToDataTable(this List<Dictionary<string, object>> list, List<ColumnInfo> colInfo)
    {
        var colList = colInfo.Select(c=>c.ColumnName).ToList();
        var dataTable = new DataTable();

        if (list == null || list.Count == 0)
            return dataTable;

        // Add columns in the order specified by colList
        foreach (var col in colList)
        {
            // Ensure that only the columns that exist in the list of dictionaries are added
            if (list[0].ContainsKey(col))
            {
                dataTable.Columns.Add(col);
            }
        }

        // Populate rows with data
        foreach (var dict in list)
        {
            var row = dataTable.NewRow();
            foreach (var key in colList)
            {
                // Check if the dictionary contains the key
                if (dict.ContainsKey(key))
                {
                    var colInfoType = colInfo.Where(c=>c.ColumnName == key).FirstOrDefault();
                    var val = dict[key];
                    if (colInfoType != null && !string.IsNullOrEmpty(colInfoType.DataType) && sqlTypeToDotNetType.ContainsKey(colInfoType.DataType))
                    {
                        val = ConvertToType(dict[key], sqlTypeToDotNetType[colInfoType.DataType]);
                    }
                    row[key] = val ?? DBNull.Value; // Handle null values
                }
                //else
                //{
                //    row[key] = DBNull.Value; // Handle missing keys
                //}
            }
            dataTable.Rows.Add(row);
        }

        return dataTable;
    }

    static Dictionary<string, Type> sqlTypeToDotNetType = new Dictionary<string, Type>
            {
                                    
                { "datetime", typeof(DateTime) },
                { "smalldatetime", typeof(DateTime) },
                // { "bit", typeof(bool) },
                //{ "float", typeof(double) },                     
                //{ "image", typeof(byte[]) },                     
                //{ "int", typeof(int) },                          
                //{ "ntext", typeof(string) },                     
                //{ "nvarchar", typeof(string) },                  
                //{ "varchar", typeof(string) }                    
            };


    static object ConvertToType(object value, Type targetType)
    {
        try
        {
            return targetType switch
            {
                Type t when t == typeof(DateTime) => value switch
                {
                    DateTime dtValue => dtValue, // If the value is already DateTime, return it
                    DateTimeOffset dtoValue => ConvertToDateTime(dtoValue.DateTime), // Convert DateTimeOffset to DateTime and check range
                    _ => ConvertToDateTime(value) // Otherwise, try converting to DateTime and check range
                },
                _ => value // For other types, return the original value
            };
        }
        catch (Exception ex)
        {
            var message = $"Error converting type: {ex.Message}";
            // Log the message or handle the error as needed
            return value; // Return the original value if conversion fails
        }
    }

    // Helper method to handle DateTime conversion and check for valid SQL range
    static DateTime? ConvertToDateTime(object value)
    {
        try
        {
            // First try to convert the value to DateTime
            DateTime convertedValue = Convert.ToDateTime(value);

            // Check if the value is within the valid SQL DateTime range
            if (convertedValue < new DateTime(1753, 1, 1) || convertedValue > new DateTime(9999, 12, 31, 23, 59, 59))
            {
                // If it is outside the valid range, return null
                return null;
            }

            return convertedValue;
        }
        catch
        {
            // If the conversion fails for any reason, return null
            return null;
        }
    }

}


public class ColumnInfo
{
    public string ColumnName { get; set; }
    public string DataType { get; set; }
}
