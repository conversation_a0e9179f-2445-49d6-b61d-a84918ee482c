.dialog-title {
  font-size: 24px;
  font-weight: 600;
  color: #3f51b5;
  margin-bottom: 5px;
  margin-top: 10px;
  text-align: center;
}

.video-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.full-width {
  width: 100%;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

button[mat-button] {
  min-width: 100px;
}

button[mat-flat-button] {
  background-color: #3f51b5;
  color: #fff;
}

button[mat-flat-button]:hover {
  background-color: #303f9f;
}

button[mat-button]:hover {
  background-color: rgba(0, 0, 0, 0.04);
}


.dialog-title {
  font-weight: bold;
}

.form-field {
  margin-bottom: 16px; /* Add spacing between form fields */
}

.video-form {
  display: flex;
  flex-direction: column;
}

.full-width {
  width: 100%; /* Full width for input fields */
}
