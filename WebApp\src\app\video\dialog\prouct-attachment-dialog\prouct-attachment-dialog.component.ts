import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-prouct-attachment-dialog',
  standalone: true,
  templateUrl: './prouct-attachment-dialog.component.html',
  styleUrls: ['./prouct-attachment-dialog.component.css']
})
export class ProuctAttachmentDialogComponent {
  compilationDate: string;

  constructor(
    public dialogRef: MatDialogRef<ProuctAttachmentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { expectedDate: string }
  ) {
    this.compilationDate = data.expectedDate; 
  }

  // Close the dialog
  closeDialog() {
    this.dialogRef.close();
  }
}
