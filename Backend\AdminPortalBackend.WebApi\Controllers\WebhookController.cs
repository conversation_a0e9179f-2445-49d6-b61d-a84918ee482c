﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.OData;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient; // or System.Data.SqlClient
using MongoDB.Driver.Core.Connections;
using System.Collections.Generic;
using System.Data;
using System.Text.Json;

namespace AdminPortalBackend.WebApi.Controllers;

[ApiController]
[Route("api/webhooks")]
public class WebhookController : ControllerBase
{
    private readonly string _connectionString;
    private readonly IDbConnectionRepository dbConnectionRepository;
    private readonly ODataService oDataService;

    public WebhookController(IConfiguration configuration, IDbConnectionRepository dbConnectionRepository, ODataService oDataService)
    {
        _connectionString = configuration.GetConnectionString("AdminConnection");
        this.dbConnectionRepository = dbConnectionRepository;
        this.oDataService = oDataService;
    }

    [HttpPost]
    [Route("register")]
    public async Task<IActionResult> RegisterWebhook(SubscriptionRequest request)
    {
        var notificationUrl = String.Empty;
        using (var connection = new SqlConnection(_connectionString))
        {
            await connection.OpenAsync();

            var existingSubscription = await connection.QueryFirstOrDefaultAsync<SubscriptionResponse>(
                    "SELECT * FROM BCSubscriptions WHERE IntegrationId = @IntegrationId AND ConnectionId = @ConnectionId",
                    new { request.IntegrationId, ConnectionId = request.SoruceConnectionId }
            );

            if (existingSubscription != null)
            {
                // If subscription already exists, return a message or handle as necessary
                return Ok(existingSubscription);
            }

            var query = "SELECT Value FROM AppSettings WHERE [key] = 'WebhookNotificationUrl'";

            // Assuming 'SettingKey' is the column that holds the specific configuration you're looking for
            var url = await connection.QueryFirstOrDefaultAsync<string>(query);

            notificationUrl = url;
        }
            var requestData = JsonSerializer.Deserialize<Dictionary<string, object>>(JsonSerializer.Serialize(new
        {
            notificationUrl,
            request.Resource,
            request.ClientState
        }));

        var connectionDetails = await dbConnectionRepository.GetODataConnectionDetailAsync(Guid.Parse(request.SoruceConnectionId));

        var postUrl = connectionDetails.ConnectionCreds.EndpointUrl + "subscriptions";
        var accessToken = await oDataService.GetAccessTokenAsync(Guid.Parse(request.SoruceConnectionId));
        var result = await oDataService.PostDataAsync(postUrl, requestData, accessToken);

        var subscriptionResponse = new SubscriptionResponse
        {
            ETag = result.GetValueOrDefault("@odata.etag")?.ToString(),
            SubscriptionId = result.GetValueOrDefault("subscriptionId")?.ToString(),
            NotificationUrl = result.GetValueOrDefault("notificationUrl")?.ToString(),
            Resource = result.GetValueOrDefault("resource")?.ToString(),
            ClientState = result.GetValueOrDefault("clientState")?.ToString(),
            LastModifiedDateTime = DateTime.TryParse(result.GetValueOrDefault("lastModifiedDateTime")?.ToString(), out DateTime lastModified)
                                    ? lastModified
                                    : default(DateTime),

            ExpirationDateTime = DateTime.TryParse(result.GetValueOrDefault("expirationDateTime")?.ToString(), out DateTime expiration)
                                    ? expiration
                                    : default(DateTime),
            IntegrationId = request.IntegrationId,
            ConnectionId = request.SoruceConnectionId,
        };

        //var subscriptionResponse = JsonSerializer.Deserialize<SubscriptionResponse>(JsonSerializer.Serialize(result));

        using (var connection = new SqlConnection(_connectionString))
        {
            await connection.OpenAsync();

            // Define the SQL Insert query
            var sql = @"
                INSERT INTO BCSubscriptions
                (ETag, SubscriptionId, NotificationUrl, Resource, ClientState, LastModifiedDateTime, ExpirationDateTime, IntegrationId, ConnectionId)
                VALUES
                (@ETag, @SubscriptionId, @NotificationUrl, @Resource, @ClientState, @LastModifiedDateTime, @ExpirationDateTime, @IntegrationId, @ConnectionId);
            ";

            // Execute the insert using Dapper
            await connection.ExecuteAsync(sql, subscriptionResponse);
        }

        return Ok(result);
    }

    [HttpPost]
    [Route("bc-notifications")]
    public async Task<IActionResult> ReceiveWebhook([FromQuery] string validationToken, [FromBody] WebhookRequest request)
    {
        if (!string.IsNullOrWhiteSpace(validationToken))
        {
            return Ok(validationToken); // Respond with the validation token first registration
        }

        if (request == null || request.Value == null || request.Value.Count == 0)
        {
            return BadRequest("Invalid webhook request.");
        }

        foreach (var notification in request.Value)
        {
            await SaveNotification(notification);
        }

        return Ok();
    }

    [HttpDelete]
    [Route("delete-subscription/{connectionId}/{integrationId}")]
    public async Task<IActionResult> DeleteSubscription(string connectionId, string integrationId)
    {
        using (IDbConnection dbConnection = new SqlConnection(_connectionString))
        {
            string sql = $"SELECT * FROM [BCSubscriptions] Where ConnectionId = '{connectionId}' AND IntegrationId = '{integrationId}'";
            var subscription = dbConnection.QuerySingleOrDefault<SubscriptionResponse>(sql);

            if (subscription != null)
            {
                var connectionDetails = await dbConnectionRepository.GetODataConnectionDetailAsync(Guid.Parse(subscription.ConnectionId));
                var deleteUrl = connectionDetails.ConnectionCreds.EndpointUrl + "subscriptions('" + subscription.SubscriptionId +"')";
                var token = await oDataService.GetAccessTokenAsync(Guid.Parse(subscription.ConnectionId));
                await oDataService.DeleteDataAsync(deleteUrl, subscription.ETag, token);

                string deleteSql = $"DELETE FROM [BCSubscriptions] Where ConnectionId = '{connectionId}' AND IntegrationId = '{integrationId}'";
                dbConnection.Execute(deleteSql);
            }
        }
        return Ok();
    }

    [HttpGet("IsSubscription")]
    public async Task<ActionResult<ResponseMessage>> IsSubscription(string connectionId, string integrationId)
    {
        using (IDbConnection dbConnection = new SqlConnection(_connectionString))
        {
            string sql = $"SELECT * FROM [BCSubscriptions] Where ConnectionId = '{connectionId}' And IntegrationId = '{integrationId}'";
            var subscription = dbConnection.QuerySingleOrDefault<SubscriptionResponse>(sql);

            if (subscription != null)
            {
                return new ResponseMessage { IsError = true, Message = "Already Exist" };
            }
            else
            {
                return new ResponseMessage { IsError = false, Message = "Not Exist" };
            }
        }
    }
    private async Task SaveNotification(WebhookNotification notification)
    {
        try
        {
            using (IDbConnection dbConnection = new SqlConnection(_connectionString))
            {
                const string sql = @"
                    INSERT INTO BCWebhookNotifications (SubscriptionId, ClientState, ExpirationDateTime, Resource, ChangeType, LastModifiedDateTime, Status)
                    VALUES (@SubscriptionId, @ClientState, @ExpirationDateTime, @Resource, @ChangeType, @LastModifiedDateTime, @Status)";

                var parameters = new
                {
                    notification.SubscriptionId,
                    notification.ClientState,
                    notification.ExpirationDateTime,
                    notification.Resource,
                    notification.ChangeType,
                    notification.LastModifiedDateTime,
                    Status="New"
                };

                await dbConnection.ExecuteAsync(sql, parameters);
            }
        }
        catch (Exception ex)
        {
            // Log the exception (you can use a logging framework of your choice)
            Console.WriteLine($"Error processing notification: {ex.Message}");
        }
    }
}
