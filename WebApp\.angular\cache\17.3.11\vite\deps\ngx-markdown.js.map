{"version": 3, "sources": ["../../../../../node_modules/marked/lib/marked.esm.js", "../../../../../node_modules/ngx-markdown/fesm2022/ngx-markdown.mjs"], "sourcesContent": ["/**\n * marked v9.1.6 - a markdown parser\n * Copyright (c) 2011-2023, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n/**\n * Gets the original marked default options.\n */\nfunction _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null\n    };\n}\nlet _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n\n/**\n * Helpers\n */\nconst escapeTest = /[&<>\"']/;\nconst escapeReplace = new RegExp(escapeTest.source, 'g');\nconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\nconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;'\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n    if (encode) {\n        if (escapeTest.test(html)) {\n            return html.replace(escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (escapeTestNoEncode.test(html)) {\n            return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nconst unescapeTest = /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig;\nfunction unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nconst caret = /(^|[^\\[])\\^/g;\nfunction edit(regex, opt) {\n    regex = typeof regex === 'string' ? regex : regex.source;\n    opt = opt || '';\n    const obj = {\n        replace: (name, val) => {\n            val = typeof val === 'object' && 'source' in val ? val.source : val;\n            val = val.replace(caret, '$1');\n            regex = regex.replace(name, val);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(regex, opt);\n        }\n    };\n    return obj;\n}\nfunction cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(/%25/g, '%');\n    }\n    catch (e) {\n        return null;\n    }\n    return href;\n}\nconst noopTest = { exec: () => null };\nfunction splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(/\\|/g, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(/ \\|/);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells[cells.length - 1].trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(/\\\\\\|/g, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nfunction rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n\nfunction outputLink(cap, link, raw, lexer) {\n    const href = link.href;\n    const title = link.title ? escape(link.title) : null;\n    const text = cap[1].replace(/\\\\([\\[\\]])/g, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text)\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text: escape(text)\n    };\n}\nfunction indentCodeCompensation(raw, text) {\n    const matchIndentToCode = raw.match(/^(\\s+)(?:```)/);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(/^\\s+/);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nclass _Tokenizer {\n    options;\n    // TODO: Fix this rules type\n    rules;\n    lexer;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0]\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(/^ {1,4}/gm, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '');\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline._escapes, '$1') : cap[2],\n                text\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (/#$/.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || / $/.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text)\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: cap[0]\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            const text = rtrim(cap[0].replace(/^ *>[ \\t]?/gm, ''), '\\n');\n            const top = this.lexer.state.top;\n            this.lexer.state.top = true;\n            const tokens = this.lexer.blockTokens(text);\n            this.lexer.state.top = top;\n            return {\n                type: 'blockquote',\n                raw: cap[0],\n                tokens,\n                text\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: []\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`);\n            let raw = '';\n            let itemContents = '';\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(/^\\t+/, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else {\n                    indent = cap[2].search(/[^ ]/); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                let blankLine = false;\n                if (!line && /^ *$/.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`);\n                    const hrRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`);\n                    const fencesBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`);\n                    const headingBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(/^ {1,4}(?=( {4})*[^ ])/g, '  ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(src)) {\n                            break;\n                        }\n                        if (nextLine.search(/[^ ]/) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLine.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.search(/[^ ]/) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLine.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (/\\n *\\n *$/.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = /^\\[[ xX]\\] /.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(/^\\[[ xX]\\] +/, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: []\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            list.items[list.items.length - 1].raw = raw.trimEnd();\n            list.items[list.items.length - 1].text = itemContents.trimEnd();\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => /\\n.*\\n/.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0]\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(/\\s+/g, ' ');\n            const href = cap[2] ? cap[2].replace(/^<(.*)>$/, '$1').replace(this.rules.inline._escapes, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline._escapes, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (cap) {\n            if (!/[:|]/.test(cap[2])) {\n                // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n                return;\n            }\n            const item = {\n                type: 'table',\n                raw: cap[0],\n                header: splitCells(cap[1]).map(c => {\n                    return { text: c, tokens: [] };\n                }),\n                align: cap[2].replace(/^\\||\\| *$/g, '').split('|'),\n                rows: cap[3] && cap[3].trim() ? cap[3].replace(/\\n[ \\t]*$/, '').split('\\n') : []\n            };\n            if (item.header.length === item.align.length) {\n                let l = item.align.length;\n                let i, j, k, row;\n                for (i = 0; i < l; i++) {\n                    const align = item.align[i];\n                    if (align) {\n                        if (/^ *-+: *$/.test(align)) {\n                            item.align[i] = 'right';\n                        }\n                        else if (/^ *:-+: *$/.test(align)) {\n                            item.align[i] = 'center';\n                        }\n                        else if (/^ *:-+ *$/.test(align)) {\n                            item.align[i] = 'left';\n                        }\n                        else {\n                            item.align[i] = null;\n                        }\n                    }\n                }\n                l = item.rows.length;\n                for (i = 0; i < l; i++) {\n                    item.rows[i] = splitCells(item.rows[i], item.header.length).map(c => {\n                        return { text: c, tokens: [] };\n                    });\n                }\n                // parse child tokens inside headers and cells\n                // header child tokens\n                l = item.header.length;\n                for (j = 0; j < l; j++) {\n                    item.header[j].tokens = this.lexer.inline(item.header[j].text);\n                }\n                // cell child tokens\n                l = item.rows.length;\n                for (j = 0; j < l; j++) {\n                    row = item.rows[j];\n                    for (k = 0; k < row.length; k++) {\n                        row[k].tokens = this.lexer.inline(row[k].text);\n                    }\n                }\n                return item;\n            }\n        }\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1])\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text)\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0])\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: escape(cap[1])\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && /^<a /i.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && /^<\\/a>/i.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && /^<\\/(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0]\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && /^</.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(/>$/.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (/^</.test(href)) {\n                if (this.options.pedantic && !(/>$/.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline._escapes, '$1') : href,\n                title: title ? title.replace(this.rules.inline._escapes, '$1') : title\n            }, cap[0], this.lexer);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            let link = (cap[2] || cap[1]).replace(/\\s+/g, ' ');\n            link = links[link.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrong.lDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(/[\\p{L}\\p{N}]/u))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrong.rDelimAst : this.rules.inline.emStrong.rDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text)\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text)\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(/\\n/g, ' ');\n            const hasNonSpaceChars = /[^ ]/.test(text);\n            const hasSpaceCharsOnBothEnds = /^ /.test(text) && / $/.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            text = escape(text, true);\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0]\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2])\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = escape(cap[1]);\n                href = 'mailto:' + text;\n            }\n            else {\n                text = escape(cap[1]);\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text\n                    }\n                ]\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = escape(cap[0]);\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])[0];\n                } while (prevCapZero !== cap[0]);\n                text = escape(cap[0]);\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text\n                    }\n                ]\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            let text;\n            if (this.lexer.state.inRawBlock) {\n                text = cap[0];\n            }\n            else {\n                text = escape(cap[0]);\n            }\n            return {\n                type: 'text',\n                raw: cap[0],\n                text\n            };\n        }\n    }\n}\n\n/**\n * Block-Level Grammar\n */\n// Not all rules are defined in the object literal\n// @ts-expect-error\nconst block = {\n    newline: /^(?: *(?:\\n|$))+/,\n    code: /^( {4}[^\\n]+(?:\\n(?: *(?:\\n|$))*)?)+/,\n    fences: /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/,\n    hr: /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/,\n    heading: /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/,\n    blockquote: /^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/,\n    list: /^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/,\n    html: '^ {0,3}(?:' // optional indentation\n        + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n        + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n        + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n        + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n        + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n        + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (6)\n        + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) open tag\n        + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) closing tag\n        + ')',\n    def: /^ {0,3}\\[(label)\\]: *(?:\\n *)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n *)?| *\\n *)(title))? *(?:\\n+|$)/,\n    table: noopTest,\n    lheading: /^(?!bull )((?:.|\\n(?!\\s*?\\n|bull ))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    // regex template, placeholders will be replaced according to different paragraph\n    // interruption rules of commonmark and the original markdown spec:\n    _paragraph: /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/,\n    text: /^[^\\n]+/\n};\nblock._label = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nblock._title = /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/;\nblock.def = edit(block.def)\n    .replace('label', block._label)\n    .replace('title', block._title)\n    .getRegex();\nblock.bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nblock.listItemStart = edit(/^( *)(bull) */)\n    .replace('bull', block.bullet)\n    .getRegex();\nblock.list = edit(block.list)\n    .replace(/bull/g, block.bullet)\n    .replace('hr', '\\\\n+(?=\\\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$))')\n    .replace('def', '\\\\n+(?=' + block.def.source + ')')\n    .getRegex();\nblock._tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr'\n    + '|track|ul';\nblock._comment = /<!--(?!-?>)[\\s\\S]*?(?:-->|$)/;\nblock.html = edit(block.html, 'i')\n    .replace('comment', block._comment)\n    .replace('tag', block._tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nblock.lheading = edit(block.lheading)\n    .replace(/bull/g, block.bullet) // lists can interrupt\n    .getRegex();\nblock.paragraph = edit(block._paragraph)\n    .replace('hr', block.hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setex headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', block._tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nblock.blockquote = edit(block.blockquote)\n    .replace('paragraph', block.paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nblock.normal = { ...block };\n/**\n * GFM Block Grammar\n */\nblock.gfm = {\n    ...block.normal,\n    table: '^ *([^\\\\n ].*)\\\\n' // Header\n        + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n        + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)' // Cells\n};\nblock.gfm.table = edit(block.gfm.table)\n    .replace('hr', block.hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', ' {4}[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', block._tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nblock.gfm.paragraph = edit(block._paragraph)\n    .replace('hr', block.hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setex headings don't interrupt commonmark paragraphs\n    .replace('table', block.gfm.table) // interrupt paragraphs with table\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', block._tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nblock.pedantic = {\n    ...block.normal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', block._comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest,\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(block.normal._paragraph)\n        .replace('hr', block.hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', block.lheading)\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .getRegex()\n};\n/**\n * Inline-Level Grammar\n */\n// Not all rules are defined in the object literal\n// @ts-expect-error\nconst inline = {\n    escape: /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/,\n    autolink: /^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/,\n    url: noopTest,\n    tag: '^comment'\n        + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n        + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n        + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n        + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n        + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>',\n    link: /^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/,\n    reflink: /^!?\\[(label)\\]\\[(ref)\\]/,\n    nolink: /^!?\\[(ref)\\](?:\\[\\])?/,\n    reflinkSearch: 'reflink|nolink(?!\\\\()',\n    emStrong: {\n        lDelim: /^(?:\\*+(?:((?!\\*)[punct])|[^\\s*]))|^_+(?:((?!_)[punct])|([^\\s_]))/,\n        //         (1) and (2) can only be a Right Delimiter. (3) and (4) can only be Left.  (5) and (6) can be either Left or Right.\n        //         | Skip orphan inside strong      | Consume to delim | (1) #***              | (2) a***#, a***                    | (3) #***a, ***a                  | (4) ***#                 | (5) #***#                         | (6) a***a\n        rDelimAst: /^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])/,\n        rDelimUnd: /^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])/ // ^- Not allowed for _\n    },\n    code: /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/,\n    br: /^( {2,}|\\\\)\\n(?!\\s*$)/,\n    del: noopTest,\n    text: /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/,\n    punctuation: /^((?![*_])[\\spunctuation])/\n};\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\ninline._punctuation = '\\\\p{P}$+<=>`^|~';\ninline.punctuation = edit(inline.punctuation, 'u').replace(/punctuation/g, inline._punctuation).getRegex();\n// sequences em should skip over [title](link), `code`, <html>\ninline.blockSkip = /\\[[^[\\]]*?\\]\\([^\\(\\)]*?\\)|`[^`]*?`|<[^<>]*?>/g;\ninline.anyPunctuation = /\\\\[punct]/g;\ninline._escapes = /\\\\([punct])/g;\ninline._comment = edit(block._comment).replace('(?:-->|$)', '-->').getRegex();\ninline.emStrong.lDelim = edit(inline.emStrong.lDelim, 'u')\n    .replace(/punct/g, inline._punctuation)\n    .getRegex();\ninline.emStrong.rDelimAst = edit(inline.emStrong.rDelimAst, 'gu')\n    .replace(/punct/g, inline._punctuation)\n    .getRegex();\ninline.emStrong.rDelimUnd = edit(inline.emStrong.rDelimUnd, 'gu')\n    .replace(/punct/g, inline._punctuation)\n    .getRegex();\ninline.anyPunctuation = edit(inline.anyPunctuation, 'gu')\n    .replace(/punct/g, inline._punctuation)\n    .getRegex();\ninline._escapes = edit(inline._escapes, 'gu')\n    .replace(/punct/g, inline._punctuation)\n    .getRegex();\ninline._scheme = /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;\ninline._email = /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;\ninline.autolink = edit(inline.autolink)\n    .replace('scheme', inline._scheme)\n    .replace('email', inline._email)\n    .getRegex();\ninline._attribute = /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/;\ninline.tag = edit(inline.tag)\n    .replace('comment', inline._comment)\n    .replace('attribute', inline._attribute)\n    .getRegex();\ninline._label = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\ninline._href = /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/;\ninline._title = /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/;\ninline.link = edit(inline.link)\n    .replace('label', inline._label)\n    .replace('href', inline._href)\n    .replace('title', inline._title)\n    .getRegex();\ninline.reflink = edit(inline.reflink)\n    .replace('label', inline._label)\n    .replace('ref', block._label)\n    .getRegex();\ninline.nolink = edit(inline.nolink)\n    .replace('ref', block._label)\n    .getRegex();\ninline.reflinkSearch = edit(inline.reflinkSearch, 'g')\n    .replace('reflink', inline.reflink)\n    .replace('nolink', inline.nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\ninline.normal = { ...inline };\n/**\n * Pedantic Inline Grammar\n */\ninline.pedantic = {\n    ...inline.normal,\n    strong: {\n        start: /^__|\\*\\*/,\n        middle: /^__(?=\\S)([\\s\\S]*?\\S)__(?!_)|^\\*\\*(?=\\S)([\\s\\S]*?\\S)\\*\\*(?!\\*)/,\n        endAst: /\\*\\*(?!\\*)/g,\n        endUnd: /__(?!_)/g\n    },\n    em: {\n        start: /^_|\\*/,\n        middle: /^()\\*(?=\\S)([\\s\\S]*?\\S)\\*(?!\\*)|^_(?=\\S)([\\s\\S]*?\\S)_(?!_)/,\n        endAst: /\\*(?!\\*)/g,\n        endUnd: /_(?!_)/g\n    },\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', inline._label)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', inline._label)\n        .getRegex()\n};\n/**\n * GFM Inline Grammar\n */\ninline.gfm = {\n    ...inline.normal,\n    escape: edit(inline.escape).replace('])', '~|])').getRegex(),\n    _extended_email: /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,\n    url: /^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/,\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])([\\s\\S]*?[^\\s~])\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\ninline.gfm.url = edit(inline.gfm.url, 'i')\n    .replace('email', inline.gfm._extended_email)\n    .getRegex();\n/**\n * GFM + Line Breaks Inline Grammar\n */\ninline.breaks = {\n    ...inline.gfm,\n    br: edit(inline.br).replace('{2,}', '*').getRegex(),\n    text: edit(inline.gfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex()\n};\n\n/**\n * Block Lexer\n */\nclass _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        // @ts-expect-error\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true\n        };\n        const rules = {\n            block: block.normal,\n            inline: inline.normal\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src\n            .replace(/\\r\\n|\\r/g, '\\n');\n        this.blockTokens(src, this.tokens);\n        let next;\n        while (next = this.inlineQueue.shift()) {\n            this.inlineTokens(next.src, next.tokens);\n        }\n        return this.tokens;\n    }\n    blockTokens(src, tokens = []) {\n        if (this.options.pedantic) {\n            src = src.replace(/\\t/g, '    ').replace(/^ +$/gm, '');\n        }\n        else {\n            src = src.replace(/^( *)(\\t+)/gm, (_, leading, tabs) => {\n                return leading + '    '.repeat(tabs.length);\n            });\n        }\n        let token;\n        let lastToken;\n        let cutSrc;\n        let lastParagraphClipped;\n        while (src) {\n            if (this.options.extensions\n                && this.options.extensions.block\n                && this.options.extensions.block.some((extTokenizer) => {\n                    if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                        src = src.substring(token.raw.length);\n                        tokens.push(token);\n                        return true;\n                    }\n                    return false;\n                })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.length === 1 && tokens.length > 0) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    tokens[tokens.length - 1].raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            cutSrc = src;\n            if (this.options.extensions && this.options.extensions.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                lastToken = tokens[tokens.length - 1];\n                if (lastParagraphClipped && lastToken.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = (cutSrc.length !== src.length);\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && lastToken.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        let token, lastToken, cutSrc;\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match;\n        let keepPrevChar, prevChar;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            // extensions\n            if (this.options.extensions\n                && this.options.extensions.inline\n                && this.options.extensions.inline.some((extTokenizer) => {\n                    if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                        src = src.substring(token.raw.length);\n                        tokens.push(token);\n                        return true;\n                    }\n                    return false;\n                })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            cutSrc = src;\n            if (this.options.extensions && this.options.extensions.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                lastToken = tokens[tokens.length - 1];\n                if (lastToken && lastToken.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n\n/**\n * Renderer\n */\nclass _Renderer {\n    options;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    code(code, infostring, escaped) {\n        const lang = (infostring || '').match(/^\\S*/)?.[0];\n        code = code.replace(/\\n$/, '') + '\\n';\n        if (!lang) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(lang)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote(quote) {\n        return `<blockquote>\\n${quote}</blockquote>\\n`;\n    }\n    html(html, block) {\n        return html;\n    }\n    heading(text, level, raw) {\n        // ignore IDs\n        return `<h${level}>${text}</h${level}>\\n`;\n    }\n    hr() {\n        return '<hr>\\n';\n    }\n    list(body, ordered, start) {\n        const type = ordered ? 'ol' : 'ul';\n        const startatt = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startatt + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(text, task, checked) {\n        return `<li>${text}</li>\\n`;\n    }\n    checkbox(checked) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph(text) {\n        return `<p>${text}</p>\\n`;\n    }\n    table(header, body) {\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow(content) {\n        return `<tr>\\n${content}</tr>\\n`;\n    }\n    tablecell(content, flags) {\n        const type = flags.header ? 'th' : 'td';\n        const tag = flags.align\n            ? `<${type} align=\"${flags.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong(text) {\n        return `<strong>${text}</strong>`;\n    }\n    em(text) {\n        return `<em>${text}</em>`;\n    }\n    codespan(text) {\n        return `<code>${text}</code>`;\n    }\n    br() {\n        return '<br>';\n    }\n    del(text) {\n        return `<del>${text}</del>`;\n    }\n    link(href, title, text) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + title + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image(href, title, text) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${title}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(text) {\n        return text;\n    }\n}\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nclass _TextRenderer {\n    // no need for block level renderers\n    strong(text) {\n        return text;\n    }\n    em(text) {\n        return text;\n    }\n    codespan(text) {\n        return text;\n    }\n    del(text) {\n        return text;\n    }\n    html(text) {\n        return text;\n    }\n    text(text) {\n        return text;\n    }\n    link(href, title, text) {\n        return '' + text;\n    }\n    image(href, title, text) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n\n/**\n * Parsing & Compiling\n */\nclass _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const token = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[token.type]) {\n                const genericToken = token;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            switch (token.type) {\n                case 'space': {\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr();\n                    continue;\n                }\n                case 'heading': {\n                    const headingToken = token;\n                    out += this.renderer.heading(this.parseInline(headingToken.tokens), headingToken.depth, unescape(this.parseInline(headingToken.tokens, this.textRenderer)));\n                    continue;\n                }\n                case 'code': {\n                    const codeToken = token;\n                    out += this.renderer.code(codeToken.text, codeToken.lang, !!codeToken.escaped);\n                    continue;\n                }\n                case 'table': {\n                    const tableToken = token;\n                    let header = '';\n                    // header\n                    let cell = '';\n                    for (let j = 0; j < tableToken.header.length; j++) {\n                        cell += this.renderer.tablecell(this.parseInline(tableToken.header[j].tokens), { header: true, align: tableToken.align[j] });\n                    }\n                    header += this.renderer.tablerow(cell);\n                    let body = '';\n                    for (let j = 0; j < tableToken.rows.length; j++) {\n                        const row = tableToken.rows[j];\n                        cell = '';\n                        for (let k = 0; k < row.length; k++) {\n                            cell += this.renderer.tablecell(this.parseInline(row[k].tokens), { header: false, align: tableToken.align[k] });\n                        }\n                        body += this.renderer.tablerow(cell);\n                    }\n                    out += this.renderer.table(header, body);\n                    continue;\n                }\n                case 'blockquote': {\n                    const blockquoteToken = token;\n                    const body = this.parse(blockquoteToken.tokens);\n                    out += this.renderer.blockquote(body);\n                    continue;\n                }\n                case 'list': {\n                    const listToken = token;\n                    const ordered = listToken.ordered;\n                    const start = listToken.start;\n                    const loose = listToken.loose;\n                    let body = '';\n                    for (let j = 0; j < listToken.items.length; j++) {\n                        const item = listToken.items[j];\n                        const checked = item.checked;\n                        const task = item.task;\n                        let itemBody = '';\n                        if (item.task) {\n                            const checkbox = this.renderer.checkbox(!!checked);\n                            if (loose) {\n                                if (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n                                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                                        item.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n                                    }\n                                }\n                                else {\n                                    item.tokens.unshift({\n                                        type: 'text',\n                                        text: checkbox + ' '\n                                    });\n                                }\n                            }\n                            else {\n                                itemBody += checkbox + ' ';\n                            }\n                        }\n                        itemBody += this.parse(item.tokens, loose);\n                        body += this.renderer.listitem(itemBody, task, !!checked);\n                    }\n                    out += this.renderer.list(body, ordered, start);\n                    continue;\n                }\n                case 'html': {\n                    const htmlToken = token;\n                    out += this.renderer.html(htmlToken.text, htmlToken.block);\n                    continue;\n                }\n                case 'paragraph': {\n                    const paragraphToken = token;\n                    out += this.renderer.paragraph(this.parseInline(paragraphToken.tokens));\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = textToken.tokens ? this.parseInline(textToken.tokens) : textToken.text;\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + (textToken.tokens ? this.parseInline(textToken.tokens) : textToken.text);\n                    }\n                    out += top ? this.renderer.paragraph(body) : body;\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer) {\n        renderer = renderer || this.renderer;\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const token = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[token.type]) {\n                const ret = this.options.extensions.renderers[token.type].call({ parser: this }, token);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(token.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            switch (token.type) {\n                case 'escape': {\n                    const escapeToken = token;\n                    out += renderer.text(escapeToken.text);\n                    break;\n                }\n                case 'html': {\n                    const tagToken = token;\n                    out += renderer.html(tagToken.text);\n                    break;\n                }\n                case 'link': {\n                    const linkToken = token;\n                    out += renderer.link(linkToken.href, linkToken.title, this.parseInline(linkToken.tokens, renderer));\n                    break;\n                }\n                case 'image': {\n                    const imageToken = token;\n                    out += renderer.image(imageToken.href, imageToken.title, imageToken.text);\n                    break;\n                }\n                case 'strong': {\n                    const strongToken = token;\n                    out += renderer.strong(this.parseInline(strongToken.tokens, renderer));\n                    break;\n                }\n                case 'em': {\n                    const emToken = token;\n                    out += renderer.em(this.parseInline(emToken.tokens, renderer));\n                    break;\n                }\n                case 'codespan': {\n                    const codespanToken = token;\n                    out += renderer.codespan(codespanToken.text);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br();\n                    break;\n                }\n                case 'del': {\n                    const delToken = token;\n                    out += renderer.del(this.parseInline(delToken.tokens, renderer));\n                    break;\n                }\n                case 'text': {\n                    const textToken = token;\n                    out += renderer.text(textToken.text);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n\nclass _Hooks {\n    options;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess'\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n}\n\nclass Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.#parseMarkdown(_Lexer.lex, _Parser.parse);\n    parseInline = this.#parseMarkdown(_Lexer.lexInline, _Parser.parseInline);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            values = values.concat(this.walkTokens(genericToken[childTokens], callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    const rendererFunc = pack.renderer[prop];\n                    const rendererKey = prop;\n                    const prevRenderer = renderer[rendererKey];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererKey] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    const tokenizerFunc = pack.tokenizer[prop];\n                    const tokenizerKey = prop;\n                    const prevTokenizer = tokenizer[tokenizerKey];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    tokenizer[tokenizerKey] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    const hooksFunc = pack.hooks[prop];\n                    const hooksKey = prop;\n                    const prevHook = hooks[hooksKey];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        hooks[hooksKey] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        hooks[hooksKey] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    #parseMarkdown(lexer, parser) {\n        return (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            // Show warning if an extension set async to true but the parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                if (!opt.silent) {\n                    console.warn('marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored.');\n                }\n                opt.async = true;\n            }\n            const throwError = this.#onError(!!opt.silent, !!opt.async);\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n            }\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                const tokens = lexer(src, opt);\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n    }\n    #onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n\nconst markedInstance = new Marked();\nfunction marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nconst options = marked.options;\nconst setOptions = marked.setOptions;\nconst use = marked.use;\nconst walkTokens = marked.walkTokens;\nconst parseInline = marked.parseInline;\nconst parse = marked;\nconst parser = _Parser.parse;\nconst lexer = _Lexer.lex;\n\nexport { _Hooks as Hooks, _Lexer as Lexer, Marked, _Parser as Parser, _Renderer as Renderer, _TextRenderer as TextRenderer, _Tokenizer as Tokenizer, _defaults as defaults, _getDefaults as getDefaults, lexer, marked, options, parse, parseInline, parser, setOptions, use, walkTokens };\n\n", "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, InjectionToken, Pipe, PLATFORM_ID, Injectable, Inject, Optional, EventEmitter, Input, Output, SecurityContext, NgModule } from '@angular/core';\nimport { Subject, of, timer, merge } from 'rxjs';\nimport { mapTo, switchMap, distinctUntilChanged, shareReplay, startWith, map, takeUntil, first } from 'rxjs/operators';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport { Renderer, marked } from 'marked';\nexport { Renderer as MarkedRenderer } from 'marked';\nimport * as i1$1 from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nconst _c0 = [\"*\"];\nconst BUTTON_TEXT_COPY = 'Copy';\nconst BUTTON_TEXT_COPIED = 'Copied';\nclass ClipboardButtonComponent {\n  constructor() {\n    this._buttonClick$ = new Subject();\n    this.copied$ = this._buttonClick$.pipe(switchMap(() => merge(of(true), timer(3000).pipe(mapTo(false)))), distinctUntilChanged(), shareReplay(1));\n    this.copiedText$ = this.copied$.pipe(startWith(false), map(copied => copied ? BUTTON_TEXT_COPIED : BUTTON_TEXT_COPY));\n  }\n  onCopyToClipboardClick() {\n    this._buttonClick$.next();\n  }\n  static {\n    this.ɵfac = function ClipboardButtonComponent_Factory(t) {\n      return new (t || ClipboardButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ClipboardButtonComponent,\n      selectors: [[\"markdown-clipboard\"]],\n      decls: 4,\n      vars: 7,\n      consts: [[1, \"markdown-clipboard-button\", 3, \"click\"]],\n      template: function ClipboardButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵlistener(\"click\", function ClipboardButtonComponent_Template_button_click_0_listener() {\n            return ctx.onCopyToClipboardClick();\n          });\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"copied\", i0.ɵɵpipeBind1(1, 3, ctx.copied$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 5, ctx.copiedText$));\n        }\n      },\n      dependencies: [i1.AsyncPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardButtonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'markdown-clipboard',\n      template: `\n    <button\n      class=\"markdown-clipboard-button\"\n      [class.copied]=\"copied$ | async\"\n      (click)=\"onCopyToClipboardClick()\"\n    >{{ copiedText$ | async }}</button>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, null);\n})();\nconst CLIPBOARD_OPTIONS = new InjectionToken('CLIPBOARD_OPTIONS');\n\n/* eslint-disable */\nclass KatexSpecificOptions {}\nclass LanguagePipe {\n  transform(value, language) {\n    if (value == null) {\n      value = '';\n    }\n    if (language == null) {\n      language = '';\n    }\n    if (typeof value !== 'string') {\n      console.error(`LanguagePipe has been invoked with an invalid value type [${typeof value}]`);\n      return value;\n    }\n    if (typeof language !== 'string') {\n      console.error(`LanguagePipe has been invoked with an invalid parameter [${typeof language}]`);\n      return value;\n    }\n    return '```' + language + '\\n' + value + '\\n```';\n  }\n  static {\n    this.ɵfac = function LanguagePipe_Factory(t) {\n      return new (t || LanguagePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"language\",\n      type: LanguagePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LanguagePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'language'\n    }]\n  }], null, null);\n})();\nvar PrismPlugin;\n(function (PrismPlugin) {\n  PrismPlugin[\"CommandLine\"] = \"command-line\";\n  PrismPlugin[\"LineHighlight\"] = \"line-highlight\";\n  PrismPlugin[\"LineNumbers\"] = \"line-numbers\";\n})(PrismPlugin || (PrismPlugin = {}));\nconst MARKED_EXTENSIONS = new InjectionToken('MARKED_EXTENSIONS');\nconst MARKED_OPTIONS = new InjectionToken('MARKED_OPTIONS');\n\n/* eslint-disable max-len */\nconst errorJoyPixelsNotLoaded = '[ngx-markdown] When using the `emoji` attribute you *have to* include Emoji-Toolkit files to `angular.json` or use imports. See README for more information';\nconst errorKatexNotLoaded = '[ngx-markdown] When using the `katex` attribute you *have to* include KaTeX files to `angular.json` or use imports. See README for more information';\nconst errorMermaidNotLoaded = '[ngx-markdown] When using the `mermaid` attribute you *have to* include Mermaid files to `angular.json` or use imports. See README for more information';\nconst errorClipboardNotLoaded = '[ngx-markdown] When using the `clipboard` attribute you *have to* include Clipboard files to `angular.json` or use imports. See README for more information';\nconst errorClipboardViewContainerRequired = '[ngx-markdown] When using the `clipboard` attribute you *have to* provide the `viewContainerRef` parameter to `MarkdownService.render()` function';\nconst errorSrcWithoutHttpClient = '[ngx-markdown] When using the `src` attribute you *have to* pass the `HttpClient` as a parameter of the `forRoot` method. See README for more information';\n/* eslint-enable max-len */\nconst SECURITY_CONTEXT = new InjectionToken('SECURITY_CONTEXT');\nclass ExtendedRenderer extends Renderer {\n  constructor() {\n    super(...arguments);\n    this.ɵNgxMarkdownRendererExtendedForExtensions = false;\n    this.ɵNgxMarkdownRendererExtendedForMermaid = false;\n  }\n}\nclass MarkdownService {\n  get options() {\n    return this._options;\n  }\n  set options(value) {\n    this._options = {\n      ...this.DEFAULT_MARKED_OPTIONS,\n      ...value\n    };\n  }\n  get renderer() {\n    return this.options.renderer;\n  }\n  set renderer(value) {\n    this.options.renderer = value;\n  }\n  constructor(clipboardOptions, extensions, options, platform, securityContext, http, sanitizer) {\n    this.clipboardOptions = clipboardOptions;\n    this.extensions = extensions;\n    this.platform = platform;\n    this.securityContext = securityContext;\n    this.http = http;\n    this.sanitizer = sanitizer;\n    this.DEFAULT_MARKED_OPTIONS = {\n      renderer: new Renderer()\n    };\n    this.DEFAULT_KATEX_OPTIONS = {\n      delimiters: [{\n        left: '$$',\n        right: '$$',\n        display: true\n      }, {\n        left: '$',\n        right: '$',\n        display: false\n      }, {\n        left: '\\\\(',\n        right: '\\\\)',\n        display: false\n      }, {\n        left: '\\\\begin{equation}',\n        right: '\\\\end{equation}',\n        display: true\n      }, {\n        left: '\\\\begin{align}',\n        right: '\\\\end{align}',\n        display: true\n      }, {\n        left: '\\\\begin{alignat}',\n        right: '\\\\end{alignat}',\n        display: true\n      }, {\n        left: '\\\\begin{gather}',\n        right: '\\\\end{gather}',\n        display: true\n      }, {\n        left: '\\\\begin{CD}',\n        right: '\\\\end{CD}',\n        display: true\n      }, {\n        left: '\\\\[',\n        right: '\\\\]',\n        display: true\n      }]\n    };\n    this.DEFAULT_MERMAID_OPTIONS = {\n      startOnLoad: false\n    };\n    this.DEFAULT_CLIPBOARD_OPTIONS = {\n      buttonComponent: undefined\n    };\n    this.DEFAULT_PARSE_OPTIONS = {\n      decodeHtml: false,\n      inline: false,\n      emoji: false,\n      mermaid: false,\n      markedOptions: this.DEFAULT_MARKED_OPTIONS,\n      disableSanitizer: false\n    };\n    this.DEFAULT_RENDER_OPTIONS = {\n      clipboard: false,\n      clipboardOptions: undefined,\n      katex: false,\n      katexOptions: undefined,\n      mermaid: false,\n      mermaidOptions: undefined\n    };\n    this._reload$ = new Subject();\n    this.reload$ = this._reload$.asObservable();\n    this.options = options;\n  }\n  parse(markdown, parseOptions = this.DEFAULT_PARSE_OPTIONS) {\n    const {\n      decodeHtml,\n      inline,\n      emoji,\n      mermaid,\n      disableSanitizer\n    } = parseOptions;\n    const markedOptions = {\n      ...this.options,\n      ...parseOptions.markedOptions\n    };\n    const renderer = markedOptions.renderer || this.renderer || new Renderer();\n    if (this.extensions) {\n      this.renderer = this.extendsRendererForExtensions(renderer);\n    }\n    if (mermaid) {\n      this.renderer = this.extendsRendererForMermaid(renderer);\n    }\n    const trimmed = this.trimIndentation(markdown);\n    const decoded = decodeHtml ? this.decodeHtml(trimmed) : trimmed;\n    const emojified = emoji ? this.parseEmoji(decoded) : decoded;\n    const marked = this.parseMarked(emojified, markedOptions, inline);\n    const sanitized = disableSanitizer ? marked : this.sanitizer.sanitize(this.securityContext, marked);\n    return sanitized || '';\n  }\n  render(element, options = this.DEFAULT_RENDER_OPTIONS, viewContainerRef) {\n    const {\n      clipboard,\n      clipboardOptions,\n      katex,\n      katexOptions,\n      mermaid,\n      mermaidOptions\n    } = options;\n    if (clipboard) {\n      this.renderClipboard(element, viewContainerRef, {\n        ...this.DEFAULT_CLIPBOARD_OPTIONS,\n        ...this.clipboardOptions,\n        ...clipboardOptions\n      });\n    }\n    if (katex) {\n      this.renderKatex(element, {\n        ...this.DEFAULT_KATEX_OPTIONS,\n        ...katexOptions\n      });\n    }\n    if (mermaid) {\n      this.renderMermaid(element, {\n        ...this.DEFAULT_MERMAID_OPTIONS,\n        ...mermaidOptions\n      });\n    }\n    this.highlight(element);\n  }\n  reload() {\n    this._reload$.next();\n  }\n  getSource(src) {\n    if (!this.http) {\n      throw new Error(errorSrcWithoutHttpClient);\n    }\n    return this.http.get(src, {\n      responseType: 'text'\n    }).pipe(map(markdown => this.handleExtension(src, markdown)));\n  }\n  highlight(element) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof Prism === 'undefined' || typeof Prism.highlightAllUnder === 'undefined') {\n      return;\n    }\n    if (!element) {\n      element = document;\n    }\n    const noLanguageElements = element.querySelectorAll('pre code:not([class*=\"language-\"])');\n    Array.prototype.forEach.call(noLanguageElements, x => x.classList.add('language-none'));\n    Prism.highlightAllUnder(element);\n  }\n  decodeHtml(html) {\n    if (!isPlatformBrowser(this.platform)) {\n      return html;\n    }\n    const textarea = document.createElement('textarea');\n    textarea.innerHTML = html;\n    return textarea.value;\n  }\n  extendsRendererForExtensions(renderer) {\n    const extendedRenderer = renderer;\n    if (extendedRenderer.ɵNgxMarkdownRendererExtendedForExtensions === true) {\n      return renderer;\n    }\n    if (this.extensions?.length > 0) {\n      marked.use(...this.extensions);\n    }\n    extendedRenderer.ɵNgxMarkdownRendererExtendedForExtensions = true;\n    return renderer;\n  }\n  extendsRendererForMermaid(renderer) {\n    const extendedRenderer = renderer;\n    if (extendedRenderer.ɵNgxMarkdownRendererExtendedForMermaid === true) {\n      return renderer;\n    }\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const defaultCode = renderer.code;\n    renderer.code = function (code, language, isEscaped) {\n      return language === 'mermaid' ? `<div class=\"mermaid\">${code}</div>` : defaultCode.call(this, code, language, isEscaped);\n    };\n    extendedRenderer.ɵNgxMarkdownRendererExtendedForMermaid = true;\n    return renderer;\n  }\n  handleExtension(src, markdown) {\n    const urlProtocolIndex = src.lastIndexOf('://');\n    const urlWithoutProtocol = urlProtocolIndex > -1 ? src.substring(urlProtocolIndex + 4) : src;\n    const lastSlashIndex = urlWithoutProtocol.lastIndexOf('/');\n    const lastUrlSegment = lastSlashIndex > -1 ? urlWithoutProtocol.substring(lastSlashIndex + 1).split('?')[0] : '';\n    const lastDotIndex = lastUrlSegment.lastIndexOf('.');\n    const extension = lastDotIndex > -1 ? lastUrlSegment.substring(lastDotIndex + 1) : '';\n    return !!extension && extension !== 'md' ? '```' + extension + '\\n' + markdown + '\\n```' : markdown;\n  }\n  parseMarked(html, markedOptions, inline = false) {\n    // remove renderer from markedOptions because if renderer\n    // is passed to parse method, it will ignore all extensions\n    if (markedOptions.renderer) {\n      marked.use({\n        renderer: markedOptions.renderer\n      });\n      delete markedOptions.renderer;\n    }\n    return inline ? marked.parseInline(html, markedOptions) : marked.parse(html, markedOptions);\n  }\n  parseEmoji(html) {\n    if (!isPlatformBrowser(this.platform)) {\n      return html;\n    }\n    if (typeof joypixels === 'undefined' || typeof joypixels.shortnameToUnicode === 'undefined') {\n      throw new Error(errorJoyPixelsNotLoaded);\n    }\n    return joypixels.shortnameToUnicode(html);\n  }\n  renderKatex(element, options) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof katex === 'undefined' || typeof renderMathInElement === 'undefined') {\n      throw new Error(errorKatexNotLoaded);\n    }\n    renderMathInElement(element, options);\n  }\n  renderClipboard(element, viewContainerRef, options) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof ClipboardJS === 'undefined') {\n      throw new Error(errorClipboardNotLoaded);\n    }\n    if (!viewContainerRef) {\n      throw new Error(errorClipboardViewContainerRequired);\n    }\n    const {\n      buttonComponent,\n      buttonTemplate\n    } = options;\n    // target every <pre> elements\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      const preElement = preElements.item(i);\n      // create <pre> wrapper element\n      const preWrapperElement = document.createElement('div');\n      preWrapperElement.style.position = 'relative';\n      preElement.parentNode.insertBefore(preWrapperElement, preElement);\n      preWrapperElement.appendChild(preElement);\n      // create toolbar element\n      const toolbarWrapperElement = document.createElement('div');\n      toolbarWrapperElement.style.position = 'absolute';\n      toolbarWrapperElement.style.top = '.5em';\n      toolbarWrapperElement.style.right = '.5em';\n      toolbarWrapperElement.style.opacity = '0';\n      toolbarWrapperElement.style.transition = 'opacity 250ms ease-out';\n      preWrapperElement.insertAdjacentElement('beforeend', toolbarWrapperElement);\n      // register listener to show/hide toolbar\n      preElement.onmouseover = () => toolbarWrapperElement.style.opacity = '1';\n      preElement.onmouseout = () => toolbarWrapperElement.style.opacity = '0';\n      // declare embeddedViewRef holding variable\n      let embeddedViewRef;\n      // use provided component via input property\n      // or provided via ClipboardOptions provider\n      if (buttonComponent) {\n        const componentRef = viewContainerRef.createComponent(buttonComponent);\n        embeddedViewRef = componentRef.hostView;\n      }\n      // use provided template via input property\n      else if (buttonTemplate) {\n        embeddedViewRef = viewContainerRef.createEmbeddedView(buttonTemplate);\n      }\n      // use default component\n      else {\n        const componentRef = viewContainerRef.createComponent(ClipboardButtonComponent);\n        embeddedViewRef = componentRef.hostView;\n      }\n      // declare clipboard instance variable\n      let clipboardInstance;\n      // attach clipboard.js to root node\n      embeddedViewRef.rootNodes.forEach(node => {\n        node.onmouseover = () => toolbarWrapperElement.style.opacity = '1';\n        toolbarWrapperElement.appendChild(node);\n        clipboardInstance = new ClipboardJS(node, {\n          text: () => preElement.innerText\n        });\n      });\n      // destroy clipboard instance when view is destroyed\n      embeddedViewRef.onDestroy(() => clipboardInstance.destroy());\n    }\n  }\n  renderMermaid(element, options = this.DEFAULT_MERMAID_OPTIONS) {\n    if (!isPlatformBrowser(this.platform)) {\n      return;\n    }\n    if (typeof mermaid === 'undefined' || typeof mermaid.initialize === 'undefined') {\n      throw new Error(errorMermaidNotLoaded);\n    }\n    const mermaidElements = element.querySelectorAll('.mermaid');\n    if (mermaidElements.length === 0) {\n      return;\n    }\n    mermaid.initialize(options);\n    mermaid.run({\n      nodes: mermaidElements\n    });\n  }\n  trimIndentation(markdown) {\n    if (!markdown) {\n      return '';\n    }\n    let indentStart;\n    return markdown.split('\\n').map(line => {\n      let lineIdentStart = indentStart;\n      if (line.length > 0) {\n        lineIdentStart = isNaN(lineIdentStart) ? line.search(/\\S|$/) : Math.min(line.search(/\\S|$/), lineIdentStart);\n      }\n      if (isNaN(indentStart)) {\n        indentStart = lineIdentStart;\n      }\n      return lineIdentStart ? line.substring(lineIdentStart) : line;\n    }).join('\\n');\n  }\n  static {\n    this.ɵfac = function MarkdownService_Factory(t) {\n      return new (t || MarkdownService)(i0.ɵɵinject(CLIPBOARD_OPTIONS, 8), i0.ɵɵinject(MARKED_EXTENSIONS, 8), i0.ɵɵinject(MARKED_OPTIONS, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(SECURITY_CONTEXT), i0.ɵɵinject(i1$1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MarkdownService,\n      factory: MarkdownService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CLIPBOARD_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MARKED_EXTENSIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MARKED_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.SecurityContext,\n    decorators: [{\n      type: Inject,\n      args: [SECURITY_CONTEXT]\n    }]\n  }, {\n    type: i1$1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }], null);\n})();\nclass MarkdownComponent {\n  get disableSanitizer() {\n    return this._disableSanitizer;\n  }\n  set disableSanitizer(value) {\n    this._disableSanitizer = this.coerceBooleanProperty(value);\n  }\n  get inline() {\n    return this._inline;\n  }\n  set inline(value) {\n    this._inline = this.coerceBooleanProperty(value);\n  }\n  // Plugin - clipboard\n  get clipboard() {\n    return this._clipboard;\n  }\n  set clipboard(value) {\n    this._clipboard = this.coerceBooleanProperty(value);\n  }\n  // Plugin - emoji\n  get emoji() {\n    return this._emoji;\n  }\n  set emoji(value) {\n    this._emoji = this.coerceBooleanProperty(value);\n  }\n  // Plugin - katex\n  get katex() {\n    return this._katex;\n  }\n  set katex(value) {\n    this._katex = this.coerceBooleanProperty(value);\n  }\n  // Plugin - mermaid\n  get mermaid() {\n    return this._mermaid;\n  }\n  set mermaid(value) {\n    this._mermaid = this.coerceBooleanProperty(value);\n  }\n  // Plugin - lineHighlight\n  get lineHighlight() {\n    return this._lineHighlight;\n  }\n  set lineHighlight(value) {\n    this._lineHighlight = this.coerceBooleanProperty(value);\n  }\n  // Plugin - lineNumbers\n  get lineNumbers() {\n    return this._lineNumbers;\n  }\n  set lineNumbers(value) {\n    this._lineNumbers = this.coerceBooleanProperty(value);\n  }\n  // Plugin - commandLine\n  get commandLine() {\n    return this._commandLine;\n  }\n  set commandLine(value) {\n    this._commandLine = this.coerceBooleanProperty(value);\n  }\n  constructor(element, markdownService, viewContainerRef) {\n    this.element = element;\n    this.markdownService = markdownService;\n    this.viewContainerRef = viewContainerRef;\n    // Event emitters\n    this.error = new EventEmitter();\n    this.load = new EventEmitter();\n    this.ready = new EventEmitter();\n    this._clipboard = false;\n    this._commandLine = false;\n    this._disableSanitizer = false;\n    this._emoji = false;\n    this._inline = false;\n    this._katex = false;\n    this._lineHighlight = false;\n    this._lineNumbers = false;\n    this._mermaid = false;\n    this.destroyed$ = new Subject();\n  }\n  ngOnChanges() {\n    this.loadContent();\n  }\n  loadContent() {\n    if (this.data != null) {\n      this.handleData();\n      return;\n    }\n    if (this.src != null) {\n      this.handleSrc();\n      return;\n    }\n  }\n  ngAfterViewInit() {\n    if (!this.data && !this.src) {\n      this.handleTransclusion();\n    }\n    this.markdownService.reload$.pipe(takeUntil(this.destroyed$)).subscribe(() => this.loadContent());\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  async render(markdown, decodeHtml = false) {\n    const parsedOptions = {\n      decodeHtml,\n      inline: this.inline,\n      emoji: this.emoji,\n      mermaid: this.mermaid,\n      disableSanitizer: this.disableSanitizer\n    };\n    const renderOptions = {\n      clipboard: this.clipboard,\n      clipboardOptions: {\n        buttonComponent: this.clipboardButtonComponent,\n        buttonTemplate: this.clipboardButtonTemplate\n      },\n      katex: this.katex,\n      katexOptions: this.katexOptions,\n      mermaid: this.mermaid,\n      mermaidOptions: this.mermaidOptions\n    };\n    const parsed = await this.markdownService.parse(markdown, parsedOptions);\n    this.element.nativeElement.innerHTML = parsed;\n    this.handlePlugins();\n    this.markdownService.render(this.element.nativeElement, renderOptions, this.viewContainerRef);\n    this.ready.emit();\n  }\n  coerceBooleanProperty(value) {\n    return value != null && `${String(value)}` !== 'false';\n  }\n  handleData() {\n    this.render(this.data);\n  }\n  handleSrc() {\n    this.markdownService.getSource(this.src).subscribe({\n      next: markdown => {\n        this.render(markdown).then(() => {\n          this.load.emit(markdown);\n        });\n      },\n      error: error => this.error.emit(error)\n    });\n  }\n  handleTransclusion() {\n    this.render(this.element.nativeElement.innerHTML, true);\n  }\n  handlePlugins() {\n    if (this.commandLine) {\n      this.setPluginClass(this.element.nativeElement, PrismPlugin.CommandLine);\n      this.setPluginOptions(this.element.nativeElement, {\n        dataFilterOutput: this.filterOutput,\n        dataHost: this.host,\n        dataPrompt: this.prompt,\n        dataOutput: this.output,\n        dataUser: this.user\n      });\n    }\n    if (this.lineHighlight) {\n      this.setPluginOptions(this.element.nativeElement, {\n        dataLine: this.line,\n        dataLineOffset: this.lineOffset\n      });\n    }\n    if (this.lineNumbers) {\n      this.setPluginClass(this.element.nativeElement, PrismPlugin.LineNumbers);\n      this.setPluginOptions(this.element.nativeElement, {\n        dataStart: this.start\n      });\n    }\n  }\n  setPluginClass(element, plugin) {\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      const classes = plugin instanceof Array ? plugin : [plugin];\n      preElements.item(i).classList.add(...classes);\n    }\n  }\n  setPluginOptions(element, options) {\n    const preElements = element.querySelectorAll('pre');\n    for (let i = 0; i < preElements.length; i++) {\n      Object.keys(options).forEach(option => {\n        const attributeValue = options[option];\n        if (attributeValue) {\n          const attributeName = this.toLispCase(option);\n          preElements.item(i).setAttribute(attributeName, attributeValue.toString());\n        }\n      });\n    }\n  }\n  toLispCase(value) {\n    const upperChars = value.match(/([A-Z])/g);\n    if (!upperChars) {\n      return value;\n    }\n    let str = value.toString();\n    for (let i = 0, n = upperChars.length; i < n; i++) {\n      str = str.replace(new RegExp(upperChars[i]), '-' + upperChars[i].toLowerCase());\n    }\n    if (str.slice(0, 1) === '-') {\n      str = str.slice(1);\n    }\n    return str;\n  }\n  static {\n    this.ɵfac = function MarkdownComponent_Factory(t) {\n      return new (t || MarkdownComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MarkdownService), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MarkdownComponent,\n      selectors: [[\"markdown\"], [\"\", \"markdown\", \"\"]],\n      inputs: {\n        data: \"data\",\n        src: \"src\",\n        disableSanitizer: \"disableSanitizer\",\n        inline: \"inline\",\n        clipboard: \"clipboard\",\n        clipboardButtonComponent: \"clipboardButtonComponent\",\n        clipboardButtonTemplate: \"clipboardButtonTemplate\",\n        emoji: \"emoji\",\n        katex: \"katex\",\n        katexOptions: \"katexOptions\",\n        mermaid: \"mermaid\",\n        mermaidOptions: \"mermaidOptions\",\n        lineHighlight: \"lineHighlight\",\n        line: \"line\",\n        lineOffset: \"lineOffset\",\n        lineNumbers: \"lineNumbers\",\n        start: \"start\",\n        commandLine: \"commandLine\",\n        filterOutput: \"filterOutput\",\n        host: \"host\",\n        prompt: \"prompt\",\n        output: \"output\",\n        user: \"user\"\n      },\n      outputs: {\n        error: \"error\",\n        load: \"load\",\n        ready: \"ready\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MarkdownComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownComponent, [{\n    type: Component,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/component-selector\n      selector: 'markdown, [markdown]',\n      template: '<ng-content></ng-content>'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: MarkdownService\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    data: [{\n      type: Input\n    }],\n    src: [{\n      type: Input\n    }],\n    disableSanitizer: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    clipboard: [{\n      type: Input\n    }],\n    clipboardButtonComponent: [{\n      type: Input\n    }],\n    clipboardButtonTemplate: [{\n      type: Input\n    }],\n    emoji: [{\n      type: Input\n    }],\n    katex: [{\n      type: Input\n    }],\n    katexOptions: [{\n      type: Input\n    }],\n    mermaid: [{\n      type: Input\n    }],\n    mermaidOptions: [{\n      type: Input\n    }],\n    lineHighlight: [{\n      type: Input\n    }],\n    line: [{\n      type: Input\n    }],\n    lineOffset: [{\n      type: Input\n    }],\n    lineNumbers: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    commandLine: [{\n      type: Input\n    }],\n    filterOutput: [{\n      type: Input\n    }],\n    host: [{\n      type: Input\n    }],\n    prompt: [{\n      type: Input\n    }],\n    output: [{\n      type: Input\n    }],\n    user: [{\n      type: Input\n    }],\n    error: [{\n      type: Output\n    }],\n    load: [{\n      type: Output\n    }],\n    ready: [{\n      type: Output\n    }]\n  });\n})();\nclass MarkdownPipe {\n  constructor(domSanitizer, elementRef, markdownService, viewContainerRef, zone) {\n    this.domSanitizer = domSanitizer;\n    this.elementRef = elementRef;\n    this.markdownService = markdownService;\n    this.viewContainerRef = viewContainerRef;\n    this.zone = zone;\n  }\n  async transform(value, options) {\n    if (value == null) {\n      return '';\n    }\n    if (typeof value !== 'string') {\n      console.error(`MarkdownPipe has been invoked with an invalid value type [${typeof value}]`);\n      return value;\n    }\n    const markdown = await this.markdownService.parse(value, options);\n    this.zone.onStable.pipe(first()).subscribe(() => this.markdownService.render(this.elementRef.nativeElement, options, this.viewContainerRef));\n    return this.domSanitizer.bypassSecurityTrustHtml(markdown);\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(t) {\n      return new (t || MarkdownPipe)(i0.ɵɵdirectiveInject(i2.DomSanitizer, 16), i0.ɵɵdirectiveInject(i0.ElementRef, 16), i0.ɵɵdirectiveInject(MarkdownService, 16), i0.ɵɵdirectiveInject(i0.ViewContainerRef, 16), i0.ɵɵdirectiveInject(i0.NgZone, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'markdown'\n    }]\n  }], () => [{\n    type: i2.DomSanitizer\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: MarkdownService\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\nconst sharedDeclarations = [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe];\nclass MarkdownModule {\n  static forRoot(markdownModuleConfig) {\n    return {\n      ngModule: MarkdownModule,\n      providers: [MarkdownService, markdownModuleConfig?.loader ?? [], markdownModuleConfig?.clipboardOptions ?? [], markdownModuleConfig?.markedOptions ?? [], {\n        provide: MARKED_EXTENSIONS,\n        useValue: markdownModuleConfig?.markedExtensions ?? []\n      }, {\n        provide: SECURITY_CONTEXT,\n        useValue: markdownModuleConfig?.sanitize ?? SecurityContext.HTML\n      }]\n    };\n  }\n  static forChild() {\n    return {\n      ngModule: MarkdownModule\n    };\n  }\n  static {\n    this.ɵfac = function MarkdownModule_Factory(t) {\n      return new (t || MarkdownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MarkdownModule,\n      declarations: [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe],\n      imports: [CommonModule],\n      exports: [ClipboardButtonComponent, LanguagePipe, MarkdownComponent, MarkdownPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarkdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: sharedDeclarations,\n      declarations: sharedDeclarations\n    }]\n  }], null, null);\n})();\n\n/* eslint-disable */\nvar MermaidAPI;\n(function (MermaidAPI) {\n  let SecurityLevel;\n  (function (SecurityLevel) {\n    /**\n     * (default) tags in text are encoded, click functionality is disabled\n     */\n    SecurityLevel[\"Strict\"] = \"strict\";\n    /**\n     * tags in text are allowed, click functionality is enabled\n     */\n    SecurityLevel[\"Loose\"] = \"loose\";\n    /**\n     * html tags in text are allowed, (only script element is removed), click functionality is enabled\n     */\n    SecurityLevel[\"Antiscript\"] = \"antiscript\";\n    /**\n     * with this security level all rendering takes place in a sandboxed iframe.\n     * This prevent any javascript running in the context.\n     * This may hinder interactive functionality of the diagram like scripts,\n     * popups in sequence diagram or links to other tabs/targets etc.\n     */\n    SecurityLevel[\"Sandbox\"] = \"sandbox\";\n  })(SecurityLevel = MermaidAPI.SecurityLevel || (MermaidAPI.SecurityLevel = {}));\n  let Theme;\n  (function (Theme) {\n    /**\n     * Designed to modified, as the name implies it is supposed to be used as the base for making custom themes.\n     */\n    Theme[\"Base\"] = \"base\";\n    /**\n     * A theme full of light greens that is easy on the eyes.\n     */\n    Theme[\"Forest\"] = \"forest\";\n    /**\n     * A theme that would go well with other dark colored elements.\n     */\n    Theme[\"Dark\"] = \"dark\";\n    /**\n     *  The default theme for all diagrams.\n     */\n    Theme[\"Default\"] = \"default\";\n    /**\n     * The theme to be used for black and white printing\n     */\n    Theme[\"Neutral\"] = \"neutral\";\n  })(Theme = MermaidAPI.Theme || (MermaidAPI.Theme = {}));\n  let LogLevel;\n  (function (LogLevel) {\n    LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\n    LogLevel[LogLevel[\"Info\"] = 2] = \"Info\";\n    LogLevel[LogLevel[\"Warn\"] = 3] = \"Warn\";\n    LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\n    LogLevel[LogLevel[\"Fatal\"] = 5] = \"Fatal\";\n  })(LogLevel = MermaidAPI.LogLevel || (MermaidAPI.LogLevel = {}));\n})(MermaidAPI || (MermaidAPI = {}));\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CLIPBOARD_OPTIONS, ClipboardButtonComponent, ExtendedRenderer, KatexSpecificOptions, LanguagePipe, MARKED_EXTENSIONS, MARKED_OPTIONS, MarkdownComponent, MarkdownModule, MarkdownPipe, MarkdownService, MermaidAPI, PrismPlugin, SECURITY_CONTEXT, errorClipboardNotLoaded, errorClipboardViewContainerRequired, errorJoyPixelsNotLoaded, errorKatexNotLoaded, errorMermaidNotLoaded, errorSrcWithoutHttpClient };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,SAAS,eAAe;AACpB,SAAO;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,EAChB;AACJ;AACA,IAAI,YAAY,aAAa;AAC7B,SAAS,eAAe,aAAa;AACjC,cAAY;AAChB;AAKA,IAAM,aAAa;AACnB,IAAM,gBAAgB,IAAI,OAAO,WAAW,QAAQ,GAAG;AACvD,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB,IAAI,OAAO,mBAAmB,QAAQ,GAAG;AACvE,IAAM,qBAAqB;AAAA,EACvB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACT;AACA,IAAM,uBAAuB,CAAC,OAAO,mBAAmB,EAAE;AAC1D,SAAS,OAAO,MAAM,QAAQ;AAC1B,MAAI,QAAQ;AACR,QAAI,WAAW,KAAK,IAAI,GAAG;AACvB,aAAO,KAAK,QAAQ,eAAe,oBAAoB;AAAA,IAC3D;AAAA,EACJ,OACK;AACD,QAAI,mBAAmB,KAAK,IAAI,GAAG;AAC/B,aAAO,KAAK,QAAQ,uBAAuB,oBAAoB;AAAA,IACnE;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,eAAe;AACrB,SAAS,SAAS,MAAM;AAEpB,SAAO,KAAK,QAAQ,cAAc,CAAC,GAAG,MAAM;AACxC,QAAI,EAAE,YAAY;AAClB,QAAI,MAAM;AACN,aAAO;AACX,QAAI,EAAE,OAAO,CAAC,MAAM,KAAK;AACrB,aAAO,EAAE,OAAO,CAAC,MAAM,MACjB,OAAO,aAAa,SAAS,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,IAChD,OAAO,aAAa,CAAC,EAAE,UAAU,CAAC,CAAC;AAAA,IAC7C;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,IAAM,QAAQ;AACd,SAAS,KAAK,OAAO,KAAK;AACtB,UAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM;AAClD,QAAM,OAAO;AACb,QAAM,MAAM;AAAA,IACR,SAAS,CAAC,MAAM,QAAQ;AACpB,YAAM,OAAO,QAAQ,YAAY,YAAY,MAAM,IAAI,SAAS;AAChE,YAAM,IAAI,QAAQ,OAAO,IAAI;AAC7B,cAAQ,MAAM,QAAQ,MAAM,GAAG;AAC/B,aAAO;AAAA,IACX;AAAA,IACA,UAAU,MAAM;AACZ,aAAO,IAAI,OAAO,OAAO,GAAG;AAAA,IAChC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,SAAS,MAAM;AACpB,MAAI;AACA,WAAO,UAAU,IAAI,EAAE,QAAQ,QAAQ,GAAG;AAAA,EAC9C,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAM,WAAW,EAAE,MAAM,MAAM,KAAK;AACpC,SAAS,WAAW,UAAU,OAAO;AAGjC,QAAM,MAAM,SAAS,QAAQ,OAAO,CAAC,OAAO,QAAQ,QAAQ;AACxD,QAAI,UAAU;AACd,QAAI,OAAO;AACX,WAAO,EAAE,QAAQ,KAAK,IAAI,IAAI,MAAM;AAChC,gBAAU,CAAC;AACf,QAAI,SAAS;AAGT,aAAO;AAAA,IACX,OACK;AAED,aAAO;AAAA,IACX;AAAA,EACJ,CAAC,GAAG,QAAQ,IAAI,MAAM,KAAK;AAC3B,MAAI,IAAI;AAER,MAAI,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG;AAClB,UAAM,MAAM;AAAA,EAChB;AACA,MAAI,MAAM,SAAS,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,EAAE,KAAK,GAAG;AACrD,UAAM,IAAI;AAAA,EACd;AACA,MAAI,OAAO;AACP,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,OAAO,KAAK;AAAA,IACtB,OACK;AACD,aAAO,MAAM,SAAS;AAClB,cAAM,KAAK,EAAE;AAAA,IACrB;AAAA,EACJ;AACA,SAAO,IAAI,MAAM,QAAQ,KAAK;AAE1B,UAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,SAAS,GAAG;AAAA,EACnD;AACA,SAAO;AACX;AASA,SAAS,MAAM,KAAK,GAAG,QAAQ;AAC3B,QAAM,IAAI,IAAI;AACd,MAAI,MAAM,GAAG;AACT,WAAO;AAAA,EACX;AAEA,MAAI,UAAU;AAEd,SAAO,UAAU,GAAG;AAChB,UAAM,WAAW,IAAI,OAAO,IAAI,UAAU,CAAC;AAC3C,QAAI,aAAa,KAAK,CAAC,QAAQ;AAC3B;AAAA,IACJ,WACS,aAAa,KAAK,QAAQ;AAC/B;AAAA,IACJ,OACK;AACD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,IAAI,MAAM,GAAG,IAAI,OAAO;AACnC;AACA,SAAS,mBAAmB,KAAK,GAAG;AAChC,MAAI,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,IAAI;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,IAAI,CAAC,MAAM,MAAM;AACjB;AAAA,IACJ,WACS,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACtB;AAAA,IACJ,WACS,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACtB;AACA,UAAI,QAAQ,GAAG;AACX,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,KAAK,MAAM,KAAKA,QAAO;AACvC,QAAM,OAAO,KAAK;AAClB,QAAM,QAAQ,KAAK,QAAQ,OAAO,KAAK,KAAK,IAAI;AAChD,QAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,eAAe,IAAI;AAC/C,MAAI,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK;AAC1B,IAAAA,OAAM,MAAM,SAAS;AACrB,UAAM,QAAQ;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQA,OAAM,aAAa,IAAI;AAAA,IACnC;AACA,IAAAA,OAAM,MAAM,SAAS;AACrB,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,OAAO,IAAI;AAAA,EACrB;AACJ;AACA,SAAS,uBAAuB,KAAK,MAAM;AACvC,QAAM,oBAAoB,IAAI,MAAM,eAAe;AACnD,MAAI,sBAAsB,MAAM;AAC5B,WAAO;AAAA,EACX;AACA,QAAM,eAAe,kBAAkB,CAAC;AACxC,SAAO,KACF,MAAM,IAAI,EACV,IAAI,UAAQ;AACb,UAAM,oBAAoB,KAAK,MAAM,MAAM;AAC3C,QAAI,sBAAsB,MAAM;AAC5B,aAAO;AAAA,IACX;AACA,UAAM,CAAC,YAAY,IAAI;AACvB,QAAI,aAAa,UAAU,aAAa,QAAQ;AAC5C,aAAO,KAAK,MAAM,aAAa,MAAM;AAAA,IACzC;AACA,WAAO;AAAA,EACX,CAAC,EACI,KAAK,IAAI;AAClB;AAIA,IAAM,aAAN,MAAiB;AAAA,EACb;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA,YAAYC,UAAS;AACjB,SAAK,UAAUA,YAAW;AAAA,EAC9B;AAAA,EACA,MAAM,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,IAAI,CAAC,EAAE,SAAS,GAAG;AAC1B,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,YAAM,OAAO,IAAI,CAAC,EAAE,QAAQ,aAAa,EAAE;AAC3C,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAM,CAAC,KAAK,QAAQ,WACd,MAAM,MAAM,IAAI,IAChB;AAAA,MACV;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC5C,QAAI,KAAK;AACL,YAAM,MAAM,IAAI,CAAC;AACjB,YAAM,OAAO,uBAAuB,KAAK,IAAI,CAAC,KAAK,EAAE;AACrD,aAAO;AAAA,QACH,MAAM;AAAA,QACN;AAAA,QACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,QAC9E;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ,KAAK;AACT,UAAM,MAAM,KAAK,MAAM,MAAM,QAAQ,KAAK,GAAG;AAC7C,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AAEvB,UAAI,KAAK,KAAK,IAAI,GAAG;AACjB,cAAM,UAAU,MAAM,MAAM,GAAG;AAC/B,YAAI,KAAK,QAAQ,UAAU;AACvB,iBAAO,QAAQ,KAAK;AAAA,QACxB,WACS,CAAC,WAAW,KAAK,KAAK,OAAO,GAAG;AAErC,iBAAO,QAAQ,KAAK;AAAA,QACxB;AAAA,MACJ;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE;AAAA,QACd;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,GAAG,KAAK;AACJ,UAAM,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG;AACxC,QAAI,KAAK;AACL,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,MAAM,WAAW,KAAK,GAAG;AAChD,QAAI,KAAK;AACL,YAAM,OAAO,MAAM,IAAI,CAAC,EAAE,QAAQ,gBAAgB,EAAE,GAAG,IAAI;AAC3D,YAAM,MAAM,KAAK,MAAM,MAAM;AAC7B,WAAK,MAAM,MAAM,MAAM;AACvB,YAAM,SAAS,KAAK,MAAM,YAAY,IAAI;AAC1C,WAAK,MAAM,MAAM,MAAM;AACvB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,KAAK;AACN,QAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AACxC,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,KAAK;AACvB,YAAM,YAAY,KAAK,SAAS;AAChC,YAAM,OAAO;AAAA,QACT,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,QACT,OAAO,YAAY,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;AAAA,QACxC,OAAO;AAAA,QACP,OAAO,CAAC;AAAA,MACZ;AACA,aAAO,YAAY,aAAa,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI;AAC5D,UAAI,KAAK,QAAQ,UAAU;AACvB,eAAO,YAAY,OAAO;AAAA,MAC9B;AAEA,YAAM,YAAY,IAAI,OAAO,WAAW,IAAI,8BAA+B;AAC3E,UAAI,MAAM;AACV,UAAI,eAAe;AACnB,UAAI,oBAAoB;AAExB,aAAO,KAAK;AACR,YAAI,WAAW;AACf,YAAI,EAAE,MAAM,UAAU,KAAK,GAAG,IAAI;AAC9B;AAAA,QACJ;AACA,YAAI,KAAK,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG;AAC/B;AAAA,QACJ;AACA,cAAM,IAAI,CAAC;AACX,cAAM,IAAI,UAAU,IAAI,MAAM;AAC9B,YAAI,OAAO,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,QAAQ,CAAC,MAAM,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC;AACnF,YAAI,WAAW,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACnC,YAAI,SAAS;AACb,YAAI,KAAK,QAAQ,UAAU;AACvB,mBAAS;AACT,yBAAe,KAAK,UAAU;AAAA,QAClC,OACK;AACD,mBAAS,IAAI,CAAC,EAAE,OAAO,MAAM;AAC7B,mBAAS,SAAS,IAAI,IAAI;AAC1B,yBAAe,KAAK,MAAM,MAAM;AAChC,oBAAU,IAAI,CAAC,EAAE;AAAA,QACrB;AACA,YAAI,YAAY;AAChB,YAAI,CAAC,QAAQ,OAAO,KAAK,QAAQ,GAAG;AAChC,iBAAO,WAAW;AAClB,gBAAM,IAAI,UAAU,SAAS,SAAS,CAAC;AACvC,qBAAW;AAAA,QACf;AACA,YAAI,CAAC,UAAU;AACX,gBAAM,kBAAkB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAqD;AACvH,gBAAM,UAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,oDAAoD;AAC9G,gBAAM,mBAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,iBAAiB;AACpF,gBAAM,oBAAoB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC,IAAI;AAExE,iBAAO,KAAK;AACR,kBAAM,UAAU,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC;AACpC,uBAAW;AAEX,gBAAI,KAAK,QAAQ,UAAU;AACvB,yBAAW,SAAS,QAAQ,2BAA2B,IAAI;AAAA,YAC/D;AAEA,gBAAI,iBAAiB,KAAK,QAAQ,GAAG;AACjC;AAAA,YACJ;AAEA,gBAAI,kBAAkB,KAAK,QAAQ,GAAG;AAClC;AAAA,YACJ;AAEA,gBAAI,gBAAgB,KAAK,QAAQ,GAAG;AAChC;AAAA,YACJ;AAEA,gBAAI,QAAQ,KAAK,GAAG,GAAG;AACnB;AAAA,YACJ;AACA,gBAAI,SAAS,OAAO,MAAM,KAAK,UAAU,CAAC,SAAS,KAAK,GAAG;AACvD,8BAAgB,OAAO,SAAS,MAAM,MAAM;AAAA,YAChD,OACK;AAED,kBAAI,WAAW;AACX;AAAA,cACJ;AAEA,kBAAI,KAAK,OAAO,MAAM,KAAK,GAAG;AAC1B;AAAA,cACJ;AACA,kBAAI,iBAAiB,KAAK,IAAI,GAAG;AAC7B;AAAA,cACJ;AACA,kBAAI,kBAAkB,KAAK,IAAI,GAAG;AAC9B;AAAA,cACJ;AACA,kBAAI,QAAQ,KAAK,IAAI,GAAG;AACpB;AAAA,cACJ;AACA,8BAAgB,OAAO;AAAA,YAC3B;AACA,gBAAI,CAAC,aAAa,CAAC,SAAS,KAAK,GAAG;AAChC,0BAAY;AAAA,YAChB;AACA,mBAAO,UAAU;AACjB,kBAAM,IAAI,UAAU,QAAQ,SAAS,CAAC;AACtC,mBAAO,SAAS,MAAM,MAAM;AAAA,UAChC;AAAA,QACJ;AACA,YAAI,CAAC,KAAK,OAAO;AAEb,cAAI,mBAAmB;AACnB,iBAAK,QAAQ;AAAA,UACjB,WACS,YAAY,KAAK,GAAG,GAAG;AAC5B,gCAAoB;AAAA,UACxB;AAAA,QACJ;AACA,YAAI,SAAS;AACb,YAAI;AAEJ,YAAI,KAAK,QAAQ,KAAK;AAClB,mBAAS,cAAc,KAAK,YAAY;AACxC,cAAI,QAAQ;AACR,wBAAY,OAAO,CAAC,MAAM;AAC1B,2BAAe,aAAa,QAAQ,gBAAgB,EAAE;AAAA,UAC1D;AAAA,QACJ;AACA,aAAK,MAAM,KAAK;AAAA,UACZ,MAAM;AAAA,UACN;AAAA,UACA,MAAM,CAAC,CAAC;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,CAAC;AAAA,QACb,CAAC;AACD,aAAK,OAAO;AAAA,MAChB;AAEA,WAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,MAAM,IAAI,QAAQ;AACpD,WAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,OAAO,aAAa,QAAQ;AAC9D,WAAK,MAAM,KAAK,IAAI,QAAQ;AAE5B,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,aAAK,MAAM,MAAM,MAAM;AACvB,aAAK,MAAM,CAAC,EAAE,SAAS,KAAK,MAAM,YAAY,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AACpE,YAAI,CAAC,KAAK,OAAO;AAEb,gBAAM,UAAU,KAAK,MAAM,CAAC,EAAE,OAAO,OAAO,OAAK,EAAE,SAAS,OAAO;AACnE,gBAAM,wBAAwB,QAAQ,SAAS,KAAK,QAAQ,KAAK,OAAK,SAAS,KAAK,EAAE,GAAG,CAAC;AAC1F,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AAEA,UAAI,KAAK,OAAO;AACZ,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,eAAK,MAAM,CAAC,EAAE,QAAQ;AAAA,QAC1B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,YAAM,QAAQ;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM;AAAA,QAC3D,MAAM,IAAI,CAAC;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG;AACzC,QAAI,KAAK;AACL,YAAM,MAAM,IAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,QAAQ,GAAG;AACpD,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ,YAAY,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI;AACnG,YAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI,IAAI,CAAC;AAC/G,aAAO;AAAA,QACH,MAAM;AAAA,QACN;AAAA,QACA,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,KAAK;AACP,UAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,UAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG;AAEtB;AAAA,MACJ;AACA,YAAM,OAAO;AAAA,QACT,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,QAAQ,WAAW,IAAI,CAAC,CAAC,EAAE,IAAI,OAAK;AAChC,iBAAO,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE;AAAA,QACjC,CAAC;AAAA,QACD,OAAO,IAAI,CAAC,EAAE,QAAQ,cAAc,EAAE,EAAE,MAAM,GAAG;AAAA,QACjD,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,EAAE,QAAQ,aAAa,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC;AAAA,MACnF;AACA,UAAI,KAAK,OAAO,WAAW,KAAK,MAAM,QAAQ;AAC1C,YAAI,IAAI,KAAK,MAAM;AACnB,YAAI,GAAG,GAAG,GAAG;AACb,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,gBAAM,QAAQ,KAAK,MAAM,CAAC;AAC1B,cAAI,OAAO;AACP,gBAAI,YAAY,KAAK,KAAK,GAAG;AACzB,mBAAK,MAAM,CAAC,IAAI;AAAA,YACpB,WACS,aAAa,KAAK,KAAK,GAAG;AAC/B,mBAAK,MAAM,CAAC,IAAI;AAAA,YACpB,WACS,YAAY,KAAK,KAAK,GAAG;AAC9B,mBAAK,MAAM,CAAC,IAAI;AAAA,YACpB,OACK;AACD,mBAAK,MAAM,CAAC,IAAI;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,KAAK,KAAK;AACd,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,eAAK,KAAK,CAAC,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,OAAK;AACjE,mBAAO,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE;AAAA,UACjC,CAAC;AAAA,QACL;AAGA,YAAI,KAAK,OAAO;AAChB,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,eAAK,OAAO,CAAC,EAAE,SAAS,KAAK,MAAM,OAAO,KAAK,OAAO,CAAC,EAAE,IAAI;AAAA,QACjE;AAEA,YAAI,KAAK,KAAK;AACd,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,gBAAM,KAAK,KAAK,CAAC;AACjB,eAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC7B,gBAAI,CAAC,EAAE,SAAS,KAAK,MAAM,OAAO,IAAI,CAAC,EAAE,IAAI;AAAA,UACjD;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AAC9C,QAAI,KAAK;AACL,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,QACtC,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,KAAK;AACX,UAAM,MAAM,KAAK,MAAM,MAAM,UAAU,KAAK,GAAG;AAC/C,QAAI,KAAK;AACL,YAAM,OAAO,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,OAC5C,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAClB,IAAI,CAAC;AACX,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA,QAAQ,KAAK,MAAM,OAAO,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,KAAK;AACR,UAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG;AAC7C,QAAI,KAAK;AACL,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,UAAI,CAAC,KAAK,MAAM,MAAM,UAAU,QAAQ,KAAK,IAAI,CAAC,CAAC,GAAG;AAClD,aAAK,MAAM,MAAM,SAAS;AAAA,MAC9B,WACS,KAAK,MAAM,MAAM,UAAU,UAAU,KAAK,IAAI,CAAC,CAAC,GAAG;AACxD,aAAK,MAAM,MAAM,SAAS;AAAA,MAC9B;AACA,UAAI,CAAC,KAAK,MAAM,MAAM,cAAc,iCAAiC,KAAK,IAAI,CAAC,CAAC,GAAG;AAC/E,aAAK,MAAM,MAAM,aAAa;AAAA,MAClC,WACS,KAAK,MAAM,MAAM,cAAc,mCAAmC,KAAK,IAAI,CAAC,CAAC,GAAG;AACrF,aAAK,MAAM,MAAM,aAAa;AAAA,MAClC;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,QAAQ,KAAK,MAAM,MAAM;AAAA,QACzB,YAAY,KAAK,MAAM,MAAM;AAAA,QAC7B,OAAO;AAAA,QACP,MAAM,IAAI,CAAC;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,KAAK;AACN,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,YAAM,aAAa,IAAI,CAAC,EAAE,KAAK;AAC/B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,KAAK,UAAU,GAAG;AAEjD,YAAI,CAAE,KAAK,KAAK,UAAU,GAAI;AAC1B;AAAA,QACJ;AAEA,cAAM,aAAa,MAAM,WAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAK,WAAW,SAAS,WAAW,UAAU,MAAM,GAAG;AACnD;AAAA,QACJ;AAAA,MACJ,OACK;AAED,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,IAAI;AACtD,YAAI,iBAAiB,IAAI;AACrB,gBAAM,QAAQ,IAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI;AAC9C,gBAAM,UAAU,QAAQ,IAAI,CAAC,EAAE,SAAS;AACxC,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,cAAc;AAC3C,cAAI,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,KAAK;AAC3C,cAAI,CAAC,IAAI;AAAA,QACb;AAAA,MACJ;AACA,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AAEvB,cAAM,OAAO,gCAAgC,KAAK,IAAI;AACtD,YAAI,MAAM;AACN,iBAAO,KAAK,CAAC;AACb,kBAAQ,KAAK,CAAC;AAAA,QAClB;AAAA,MACJ,OACK;AACD,gBAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,MAC3C;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,KAAK,KAAK,IAAI,GAAG;AACjB,YAAI,KAAK,QAAQ,YAAY,CAAE,KAAK,KAAK,UAAU,GAAI;AAEnD,iBAAO,KAAK,MAAM,CAAC;AAAA,QACvB,OACK;AACD,iBAAO,KAAK,MAAM,GAAG,EAAE;AAAA,QAC3B;AAAA,MACJ;AACA,aAAO,WAAW,KAAK;AAAA,QACnB,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI;AAAA,QAC9D,OAAO,QAAQ,MAAM,QAAQ,KAAK,MAAM,OAAO,UAAU,IAAI,IAAI;AAAA,MACrE,GAAG,IAAI,CAAC,GAAG,KAAK,KAAK;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,QAAQ,KAAK,OAAO;AAChB,QAAI;AACJ,SAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,GAAG,OACrC,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,GAAG,IAAI;AAC/C,UAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,QAAQ,GAAG;AACjD,aAAO,MAAM,KAAK,YAAY,CAAC;AAC/B,UAAI,CAAC,MAAM;AACP,cAAM,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO;AAAA,UACH,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,WAAW,KAAK,MAAM,IAAI,CAAC,GAAG,KAAK,KAAK;AAAA,IACnD;AAAA,EACJ;AAAA,EACA,SAAS,KAAK,WAAW,WAAW,IAAI;AACpC,QAAI,QAAQ,KAAK,MAAM,OAAO,SAAS,OAAO,KAAK,GAAG;AACtD,QAAI,CAAC;AACD;AAEJ,QAAI,MAAM,CAAC,KAAK,SAAS,MAAM,eAAe;AAC1C;AACJ,UAAM,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK;AACzC,QAAI,CAAC,YAAY,CAAC,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,QAAQ,GAAG;AAExE,YAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS;AACvC,UAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;AAC3D,YAAM,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,SAAS,YAAY,KAAK,MAAM,OAAO,SAAS;AACvG,aAAO,YAAY;AAEnB,kBAAY,UAAU,MAAM,KAAK,IAAI,SAAS,OAAO;AACrD,cAAQ,QAAQ,OAAO,KAAK,SAAS,MAAM,MAAM;AAC7C,iBAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC;AAC5E,YAAI,CAAC;AACD;AACJ,kBAAU,CAAC,GAAG,MAAM,EAAE;AACtB,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACtB,wBAAc;AACd;AAAA,QACJ,WACS,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AAC3B,cAAI,UAAU,KAAK,GAAG,UAAU,WAAW,IAAI;AAC3C,6BAAiB;AACjB;AAAA,UACJ;AAAA,QACJ;AACA,sBAAc;AACd,YAAI,aAAa;AACb;AAEJ,kBAAU,KAAK,IAAI,SAAS,UAAU,aAAa,aAAa;AAEhE,cAAM,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AACxC,cAAM,MAAM,IAAI,MAAM,GAAG,UAAU,MAAM,QAAQ,iBAAiB,OAAO;AAEzE,YAAI,KAAK,IAAI,SAAS,OAAO,IAAI,GAAG;AAChC,gBAAMC,QAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,YACA,MAAAA;AAAA,YACA,QAAQ,KAAK,MAAM,aAAaA,KAAI;AAAA,UACxC;AAAA,QACJ;AAEA,cAAM,OAAO,IAAI,MAAM,GAAG,EAAE;AAC5B,eAAO;AAAA,UACH,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,QAAQ,KAAK,MAAM,aAAa,IAAI;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,UAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAG;AACpC,YAAM,mBAAmB,OAAO,KAAK,IAAI;AACzC,YAAM,0BAA0B,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AACjE,UAAI,oBAAoB,yBAAyB;AAC7C,eAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,MAC5C;AACA,aAAO,OAAO,MAAM,IAAI;AACxB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,GAAG,KAAK;AACJ,UAAM,MAAM,KAAK,MAAM,OAAO,GAAG,KAAK,GAAG;AACzC,QAAI,KAAK;AACL,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACL,UAAM,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAC1C,QAAI,KAAK;AACL,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV,MAAM,IAAI,CAAC;AAAA,QACX,QAAQ,KAAK,MAAM,aAAa,IAAI,CAAC,CAAC;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS,KAAK;AACV,UAAM,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,GAAG;AAC/C,QAAI,KAAK;AACL,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAChB,eAAO,OAAO,IAAI,CAAC,CAAC;AACpB,eAAO,YAAY;AAAA,MACvB,OACK;AACD,eAAO,OAAO,IAAI,CAAC,CAAC;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACJ;AAAA,YACI,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACL,QAAI;AACJ,QAAI,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG;AACvC,UAAI,MAAM;AACV,UAAI,IAAI,CAAC,MAAM,KAAK;AAChB,eAAO,OAAO,IAAI,CAAC,CAAC;AACpB,eAAO,YAAY;AAAA,MACvB,OACK;AAED,YAAI;AACJ,WAAG;AACC,wBAAc,IAAI,CAAC;AACnB,cAAI,CAAC,IAAI,KAAK,MAAM,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;AAAA,QACxD,SAAS,gBAAgB,IAAI,CAAC;AAC9B,eAAO,OAAO,IAAI,CAAC,CAAC;AACpB,YAAI,IAAI,CAAC,MAAM,QAAQ;AACnB,iBAAO,YAAY,IAAI,CAAC;AAAA,QAC5B,OACK;AACD,iBAAO,IAAI,CAAC;AAAA,QAChB;AAAA,MACJ;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,UACJ;AAAA,YACI,MAAM;AAAA,YACN,KAAK;AAAA,YACL;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW,KAAK;AACZ,UAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG;AAC3C,QAAI,KAAK;AACL,UAAI;AACJ,UAAI,KAAK,MAAM,MAAM,YAAY;AAC7B,eAAO,IAAI,CAAC;AAAA,MAChB,OACK;AACD,eAAO,OAAO,IAAI,CAAC,CAAC;AAAA,MACxB;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,KAAK,IAAI,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAOA,IAAM,QAAQ;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,EAUN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,UAAU;AAAA;AAAA;AAAA,EAGV,YAAY;AAAA,EACZ,MAAM;AACV;AACA,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,MAAM,KAAK,MAAM,GAAG,EACrB,QAAQ,SAAS,MAAM,MAAM,EAC7B,QAAQ,SAAS,MAAM,MAAM,EAC7B,SAAS;AACd,MAAM,SAAS;AACf,MAAM,gBAAgB,KAAK,eAAe,EACrC,QAAQ,QAAQ,MAAM,MAAM,EAC5B,SAAS;AACd,MAAM,OAAO,KAAK,MAAM,IAAI,EACvB,QAAQ,SAAS,MAAM,MAAM,EAC7B,QAAQ,MAAM,iEAAiE,EAC/E,QAAQ,OAAO,YAAY,MAAM,IAAI,SAAS,GAAG,EACjD,SAAS;AACd,MAAM,OAAO;AAMb,MAAM,WAAW;AACjB,MAAM,OAAO,KAAK,MAAM,MAAM,GAAG,EAC5B,QAAQ,WAAW,MAAM,QAAQ,EACjC,QAAQ,OAAO,MAAM,IAAI,EACzB,QAAQ,aAAa,0EAA0E,EAC/F,SAAS;AACd,MAAM,WAAW,KAAK,MAAM,QAAQ,EAC/B,QAAQ,SAAS,MAAM,MAAM,EAC7B,SAAS;AACd,MAAM,YAAY,KAAK,MAAM,UAAU,EAClC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,MAAM,IAAI,EACzB,SAAS;AACd,MAAM,aAAa,KAAK,MAAM,UAAU,EACnC,QAAQ,aAAa,MAAM,SAAS,EACpC,SAAS;AAId,MAAM,SAAS,mBAAK;AAIpB,MAAM,MAAM,iCACL,MAAM,SADD;AAAA,EAER,OAAO;AAAA;AAGX;AACA,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,KAAK,EACjC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,cAAc,SAAS,EAC/B,QAAQ,QAAQ,YAAY,EAC5B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,MAAM,IAAI,EACzB,SAAS;AACd,MAAM,IAAI,YAAY,KAAK,MAAM,UAAU,EACtC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAAS,MAAM,IAAI,KAAK,EAChC,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAO,MAAM,IAAI,EACzB,SAAS;AAId,MAAM,WAAW,iCACV,MAAM,SADI;AAAA,EAEb,MAAM,KAAK,wIAEiE,EACvE,QAAQ,WAAW,MAAM,QAAQ,EACjC,QAAQ,QAAQ,mKAGgB,EAChC,SAAS;AAAA,EACd,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,WAAW,KAAK,MAAM,OAAO,UAAU,EAClC,QAAQ,MAAM,MAAM,EAAE,EACtB,QAAQ,WAAW,iBAAiB,EACpC,QAAQ,YAAY,MAAM,QAAQ,EAClC,QAAQ,cAAc,SAAS,EAC/B,QAAQ,WAAW,EAAE,EACrB,QAAQ,SAAS,EAAE,EACnB,QAAQ,SAAS,EAAE,EACnB,SAAS;AAClB;AAMA,IAAM,SAAS;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,KAAK;AAAA,EACL,KAAK;AAAA,EAML,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,UAAU;AAAA,IACN,QAAQ;AAAA;AAAA;AAAA,IAGR,WAAW;AAAA,IACX,WAAW;AAAA;AAAA,EACf;AAAA,EACA,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,aAAa;AACjB;AAEA,OAAO,eAAe;AACtB,OAAO,cAAc,KAAK,OAAO,aAAa,GAAG,EAAE,QAAQ,gBAAgB,OAAO,YAAY,EAAE,SAAS;AAEzG,OAAO,YAAY;AACnB,OAAO,iBAAiB;AACxB,OAAO,WAAW;AAClB,OAAO,WAAW,KAAK,MAAM,QAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAS;AAC5E,OAAO,SAAS,SAAS,KAAK,OAAO,SAAS,QAAQ,GAAG,EACpD,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AACd,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,WAAW,IAAI,EAC3D,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AACd,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,WAAW,IAAI,EAC3D,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AACd,OAAO,iBAAiB,KAAK,OAAO,gBAAgB,IAAI,EACnD,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AACd,OAAO,WAAW,KAAK,OAAO,UAAU,IAAI,EACvC,QAAQ,UAAU,OAAO,YAAY,EACrC,SAAS;AACd,OAAO,UAAU;AACjB,OAAO,SAAS;AAChB,OAAO,WAAW,KAAK,OAAO,QAAQ,EACjC,QAAQ,UAAU,OAAO,OAAO,EAChC,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AACd,OAAO,aAAa;AACpB,OAAO,MAAM,KAAK,OAAO,GAAG,EACvB,QAAQ,WAAW,OAAO,QAAQ,EAClC,QAAQ,aAAa,OAAO,UAAU,EACtC,SAAS;AACd,OAAO,SAAS;AAChB,OAAO,QAAQ;AACf,OAAO,SAAS;AAChB,OAAO,OAAO,KAAK,OAAO,IAAI,EACzB,QAAQ,SAAS,OAAO,MAAM,EAC9B,QAAQ,QAAQ,OAAO,KAAK,EAC5B,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AACd,OAAO,UAAU,KAAK,OAAO,OAAO,EAC/B,QAAQ,SAAS,OAAO,MAAM,EAC9B,QAAQ,OAAO,MAAM,MAAM,EAC3B,SAAS;AACd,OAAO,SAAS,KAAK,OAAO,MAAM,EAC7B,QAAQ,OAAO,MAAM,MAAM,EAC3B,SAAS;AACd,OAAO,gBAAgB,KAAK,OAAO,eAAe,GAAG,EAChD,QAAQ,WAAW,OAAO,OAAO,EACjC,QAAQ,UAAU,OAAO,MAAM,EAC/B,SAAS;AAId,OAAO,SAAS,mBAAK;AAIrB,OAAO,WAAW,iCACX,OAAO,SADI;AAAA,EAEd,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,IAAI;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,MAAM,KAAK,yBAAyB,EAC/B,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AAAA,EACd,SAAS,KAAK,+BAA+B,EACxC,QAAQ,SAAS,OAAO,MAAM,EAC9B,SAAS;AAClB;AAIA,OAAO,MAAM,iCACN,OAAO,SADD;AAAA,EAET,QAAQ,KAAK,OAAO,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,SAAS;AAAA,EAC3D,iBAAiB;AAAA,EACjB,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AACV;AACA,OAAO,IAAI,MAAM,KAAK,OAAO,IAAI,KAAK,GAAG,EACpC,QAAQ,SAAS,OAAO,IAAI,eAAe,EAC3C,SAAS;AAId,OAAO,SAAS,iCACT,OAAO,MADE;AAAA,EAEZ,IAAI,KAAK,OAAO,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAS;AAAA,EAClD,MAAM,KAAK,OAAO,IAAI,IAAI,EACrB,QAAQ,QAAQ,eAAe,EAC/B,QAAQ,WAAW,GAAG,EACtB,SAAS;AAClB;AAKA,IAAM,SAAN,MAAM,QAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAYD,UAAS;AAGjB,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,QAAQ,uBAAO,OAAO,IAAI;AACtC,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAI,WAAW;AAClE,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,UAAU,UAAU,KAAK;AAC9B,SAAK,UAAU,QAAQ;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,QAAQ;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,KAAK;AAAA,IACT;AACA,UAAM,QAAQ;AAAA,MACV,OAAO,MAAM;AAAA,MACb,QAAQ,OAAO;AAAA,IACnB;AACA,QAAI,KAAK,QAAQ,UAAU;AACvB,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS,OAAO;AAAA,IAC1B,WACS,KAAK,QAAQ,KAAK;AACvB,YAAM,QAAQ,MAAM;AACpB,UAAI,KAAK,QAAQ,QAAQ;AACrB,cAAM,SAAS,OAAO;AAAA,MAC1B,OACK;AACD,cAAM,SAAS,OAAO;AAAA,MAC1B;AAAA,IACJ;AACA,SAAK,UAAU,QAAQ;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ;AACf,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,IAAI,KAAKA,UAAS;AACrB,UAAMD,SAAQ,IAAI,QAAOC,QAAO;AAChC,WAAOD,OAAM,IAAI,GAAG;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,UAAU,KAAKC,UAAS;AAC3B,UAAMD,SAAQ,IAAI,QAAOC,QAAO;AAChC,WAAOD,OAAM,aAAa,GAAG;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK;AACL,UAAM,IACD,QAAQ,YAAY,IAAI;AAC7B,SAAK,YAAY,KAAK,KAAK,MAAM;AACjC,QAAI;AACJ,WAAO,OAAO,KAAK,YAAY,MAAM,GAAG;AACpC,WAAK,aAAa,KAAK,KAAK,KAAK,MAAM;AAAA,IAC3C;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,YAAY,KAAK,SAAS,CAAC,GAAG;AAC1B,QAAI,KAAK,QAAQ,UAAU;AACvB,YAAM,IAAI,QAAQ,OAAO,MAAM,EAAE,QAAQ,UAAU,EAAE;AAAA,IACzD,OACK;AACD,YAAM,IAAI,QAAQ,gBAAgB,CAAC,GAAG,SAAS,SAAS;AACpD,eAAO,UAAU,OAAO,OAAO,KAAK,MAAM;AAAA,MAC9C,CAAC;AAAA,IACL;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,WAAO,KAAK;AACR,UAAI,KAAK,QAAQ,cACV,KAAK,QAAQ,WAAW,SACxB,KAAK,QAAQ,WAAW,MAAM,KAAK,CAAC,iBAAiB;AACpD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AACzD,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,CAAC,GAAG;AACJ;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,WAAW,KAAK,OAAO,SAAS,GAAG;AAG7C,iBAAO,OAAO,SAAS,CAAC,EAAE,OAAO;AAAA,QACrC,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AAEpC,YAAI,cAAc,UAAU,SAAS,eAAe,UAAU,SAAS,SAAS;AAC5E,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAClE,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,QAAQ,GAAG,GAAG;AACrC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,WAAW,GAAG,GAAG;AACxC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,cAAc,UAAU,SAAS,eAAe,UAAU,SAAS,SAAS;AAC5E,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAClE,WACS,CAAC,KAAK,OAAO,MAAM,MAAM,GAAG,GAAG;AACpC,eAAK,OAAO,MAAM,MAAM,GAAG,IAAI;AAAA,YAC3B,MAAM,MAAM;AAAA,YACZ,OAAO,MAAM;AAAA,UACjB;AAAA,QACJ;AACA;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,MAAM,GAAG,GAAG;AACnC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAGA,eAAS;AACT,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,YAAY;AAC/D,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,WAAW,QAAQ,CAAC,kBAAkB;AAC1D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACjD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC/C;AAAA,QACJ,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC1C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC5C;AAAA,MACJ;AACA,UAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,UAAU,UAAU,MAAM,IAAI;AAC9D,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,wBAAwB,UAAU,SAAS,aAAa;AACxD,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAClE,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA,+BAAwB,OAAO,WAAW,IAAI;AAC9C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,UAAU,SAAS,QAAQ;AACxC,oBAAU,OAAO,OAAO,MAAM;AAC9B,oBAAU,QAAQ,OAAO,MAAM;AAC/B,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE,MAAM,UAAU;AAAA,QAClE,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACJ;AACA,UAAI,KAAK;AACL,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACJ,OACK;AACD,gBAAM,IAAI,MAAM,MAAM;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,MAAM,MAAM;AACjB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK,SAAS,CAAC,GAAG;AACrB,SAAK,YAAY,KAAK,EAAE,KAAK,OAAO,CAAC;AACrC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK,SAAS,CAAC,GAAG;AAC3B,QAAI,OAAO,WAAW;AAEtB,QAAI,YAAY;AAChB,QAAI;AACJ,QAAI,cAAc;AAElB,QAAI,KAAK,OAAO,OAAO;AACnB,YAAM,QAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAI,MAAM,SAAS,GAAG;AAClB,gBAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK,SAAS,MAAM,MAAM;AAChF,cAAI,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG;AACnE,wBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;AAAA,UACnK;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK,SAAS,MAAM,MAAM;AAC5E,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;AAAA,IAC/J;AAEA,YAAQ,QAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK,SAAS,MAAM,MAAM;AACjF,kBAAY,UAAU,MAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;AAAA,IAC7H;AACA,WAAO,KAAK;AACR,UAAI,CAAC,cAAc;AACf,mBAAW;AAAA,MACf;AACA,qBAAe;AAEf,UAAI,KAAK,QAAQ,cACV,KAAK,QAAQ,WAAW,UACxB,KAAK,QAAQ,WAAW,OAAO,KAAK,CAAC,iBAAiB;AACrD,YAAI,QAAQ,aAAa,KAAK,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM,GAAG;AACzD,gBAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,iBAAO,KAAK,KAAK;AACjB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,CAAC,GAAG;AACJ;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,OAAO,GAAG,GAAG;AACpC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,MAAM,SAAS,UAAU,UAAU,SAAS,QAAQ;AACjE,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC5B,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,GAAG;AAClC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,OAAO,KAAK,GAAG;AACxD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,MAAM,SAAS,UAAU,UAAU,SAAS,QAAQ;AACjE,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC5B,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,SAAS,KAAK,WAAW,QAAQ,GAAG;AAC3D,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,GAAG;AACjC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAEA,UAAI,CAAC,KAAK,MAAM,WAAW,QAAQ,KAAK,UAAU,IAAI,GAAG,IAAI;AACzD,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,eAAO,KAAK,KAAK;AACjB;AAAA,MACJ;AAGA,eAAS;AACT,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa;AAChE,YAAI,aAAa;AACjB,cAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,YAAI;AACJ,aAAK,QAAQ,WAAW,YAAY,QAAQ,CAAC,kBAAkB;AAC3D,sBAAY,cAAc,KAAK,EAAE,OAAO,KAAK,GAAG,OAAO;AACvD,cAAI,OAAO,cAAc,YAAY,aAAa,GAAG;AACjD,yBAAa,KAAK,IAAI,YAAY,SAAS;AAAA,UAC/C;AAAA,QACJ,CAAC;AACD,YAAI,aAAa,YAAY,cAAc,GAAG;AAC1C,mBAAS,IAAI,UAAU,GAAG,aAAa,CAAC;AAAA,QAC5C;AAAA,MACJ;AACA,UAAI,QAAQ,KAAK,UAAU,WAAW,MAAM,GAAG;AAC3C,cAAM,IAAI,UAAU,MAAM,IAAI,MAAM;AACpC,YAAI,MAAM,IAAI,MAAM,EAAE,MAAM,KAAK;AAC7B,qBAAW,MAAM,IAAI,MAAM,EAAE;AAAA,QACjC;AACA,uBAAe;AACf,oBAAY,OAAO,OAAO,SAAS,CAAC;AACpC,YAAI,aAAa,UAAU,SAAS,QAAQ;AACxC,oBAAU,OAAO,MAAM;AACvB,oBAAU,QAAQ,MAAM;AAAA,QAC5B,OACK;AACD,iBAAO,KAAK,KAAK;AAAA,QACrB;AACA;AAAA,MACJ;AACA,UAAI,KAAK;AACL,cAAM,SAAS,4BAA4B,IAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAQ,MAAM,MAAM;AACpB;AAAA,QACJ,OACK;AACD,gBAAM,IAAI,MAAM,MAAM;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,YAAN,MAAgB;AAAA,EACZ;AAAA,EACA,YAAYC,UAAS;AACjB,SAAK,UAAUA,YAAW;AAAA,EAC9B;AAAA,EACA,KAAK,MAAM,YAAY,SAAS;AAC5B,UAAM,QAAQ,cAAc,IAAI,MAAM,MAAM,IAAI,CAAC;AACjD,WAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;AACjC,QAAI,CAAC,MAAM;AACP,aAAO,iBACA,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;AAAA,IACV;AACA,WAAO,gCACD,OAAO,IAAI,IACX,QACC,UAAU,OAAO,OAAO,MAAM,IAAI,KACnC;AAAA,EACV;AAAA,EACA,WAAW,OAAO;AACd,WAAO;AAAA,EAAiB,KAAK;AAAA;AAAA,EACjC;AAAA,EACA,KAAK,MAAME,QAAO;AACd,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,MAAM,OAAO,KAAK;AAEtB,WAAO,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK;AAAA;AAAA,EACxC;AAAA,EACA,KAAK;AACD,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM,SAAS,OAAO;AACvB,UAAM,OAAO,UAAU,OAAO;AAC9B,UAAM,WAAY,WAAW,UAAU,IAAM,aAAa,QAAQ,MAAO;AACzE,WAAO,MAAM,OAAO,WAAW,QAAQ,OAAO,OAAO,OAAO;AAAA,EAChE;AAAA,EACA,SAAS,MAAM,MAAM,SAAS;AAC1B,WAAO,OAAO,IAAI;AAAA;AAAA,EACtB;AAAA,EACA,SAAS,SAAS;AACd,WAAO,aACA,UAAU,gBAAgB,MAC3B;AAAA,EACV;AAAA,EACA,UAAU,MAAM;AACZ,WAAO,MAAM,IAAI;AAAA;AAAA,EACrB;AAAA,EACA,MAAM,QAAQ,MAAM;AAChB,QAAI;AACA,aAAO,UAAU,IAAI;AACzB,WAAO,uBAED,SACA,eACA,OACA;AAAA,EACV;AAAA,EACA,SAAS,SAAS;AACd,WAAO;AAAA,EAAS,OAAO;AAAA;AAAA,EAC3B;AAAA,EACA,UAAU,SAAS,OAAO;AACtB,UAAM,OAAO,MAAM,SAAS,OAAO;AACnC,UAAM,MAAM,MAAM,QACZ,IAAI,IAAI,WAAW,MAAM,KAAK,OAC9B,IAAI,IAAI;AACd,WAAO,MAAM,UAAU,KAAK,IAAI;AAAA;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM;AACT,WAAO,WAAW,IAAI;AAAA,EAC1B;AAAA,EACA,GAAG,MAAM;AACL,WAAO,OAAO,IAAI;AAAA,EACtB;AAAA,EACA,SAAS,MAAM;AACX,WAAO,SAAS,IAAI;AAAA,EACxB;AAAA,EACA,KAAK;AACD,WAAO;AAAA,EACX;AAAA,EACA,IAAI,MAAM;AACN,WAAO,QAAQ,IAAI;AAAA,EACvB;AAAA,EACA,KAAK,MAAM,OAAO,MAAM;AACpB,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AACP,QAAI,MAAM,cAAc,OAAO;AAC/B,QAAI,OAAO;AACP,aAAO,aAAa,QAAQ;AAAA,IAChC;AACA,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACX;AAAA,EACA,MAAM,MAAM,OAAO,MAAM;AACrB,UAAM,YAAY,SAAS,IAAI;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AACP,QAAI,MAAM,aAAa,IAAI,UAAU,IAAI;AACzC,QAAI,OAAO;AACP,aAAO,WAAW,KAAK;AAAA,IAC3B;AACA,WAAO;AACP,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM;AACP,WAAO;AAAA,EACX;AACJ;AAMA,IAAM,gBAAN,MAAoB;AAAA;AAAA,EAEhB,OAAO,MAAM;AACT,WAAO;AAAA,EACX;AAAA,EACA,GAAG,MAAM;AACL,WAAO;AAAA,EACX;AAAA,EACA,SAAS,MAAM;AACX,WAAO;AAAA,EACX;AAAA,EACA,IAAI,MAAM;AACN,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM;AACP,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM;AACP,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM,OAAO,MAAM;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,MAAM,MAAM,OAAO,MAAM;AACrB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,KAAK;AACD,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAYF,UAAS;AACjB,SAAK,UAAUA,YAAW;AAC1B,SAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAI,UAAU;AAC/D,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,eAAe,IAAI,cAAc;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQA,UAAS;AAC1B,UAAMG,UAAS,IAAI,SAAQH,QAAO;AAClC,WAAOG,QAAO,MAAM,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,YAAY,QAAQH,UAAS;AAChC,UAAMG,UAAS,IAAI,SAAQH,QAAO;AAClC,WAAOG,QAAO,YAAY,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,QAAQ,MAAM,MAAM;AACtB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,QAAQ,OAAO,CAAC;AAEtB,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,GAAG;AAC/G,cAAM,eAAe;AACrB,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,aAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,YAAY;AACpG,YAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAAS,aAAa,IAAI,GAAG;AAC9I,iBAAO,OAAO;AACd;AAAA,QACJ;AAAA,MACJ;AACA,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK,SAAS;AACV;AAAA,QACJ;AAAA,QACA,KAAK,MAAM;AACP,iBAAO,KAAK,SAAS,GAAG;AACxB;AAAA,QACJ;AAAA,QACA,KAAK,WAAW;AACZ,gBAAM,eAAe;AACrB,iBAAO,KAAK,SAAS,QAAQ,KAAK,YAAY,aAAa,MAAM,GAAG,aAAa,OAAO,SAAS,KAAK,YAAY,aAAa,QAAQ,KAAK,YAAY,CAAC,CAAC;AAC1J;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,iBAAO,KAAK,SAAS,KAAK,UAAU,MAAM,UAAU,MAAM,CAAC,CAAC,UAAU,OAAO;AAC7E;AAAA,QACJ;AAAA,QACA,KAAK,SAAS;AACV,gBAAM,aAAa;AACnB,cAAI,SAAS;AAEb,cAAI,OAAO;AACX,mBAAS,IAAI,GAAG,IAAI,WAAW,OAAO,QAAQ,KAAK;AAC/C,oBAAQ,KAAK,SAAS,UAAU,KAAK,YAAY,WAAW,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,QAAQ,MAAM,OAAO,WAAW,MAAM,CAAC,EAAE,CAAC;AAAA,UAC/H;AACA,oBAAU,KAAK,SAAS,SAAS,IAAI;AACrC,cAAI,OAAO;AACX,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK,QAAQ,KAAK;AAC7C,kBAAM,MAAM,WAAW,KAAK,CAAC;AAC7B,mBAAO;AACP,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,sBAAQ,KAAK,SAAS,UAAU,KAAK,YAAY,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,QAAQ,OAAO,OAAO,WAAW,MAAM,CAAC,EAAE,CAAC;AAAA,YAClH;AACA,oBAAQ,KAAK,SAAS,SAAS,IAAI;AAAA,UACvC;AACA,iBAAO,KAAK,SAAS,MAAM,QAAQ,IAAI;AACvC;AAAA,QACJ;AAAA,QACA,KAAK,cAAc;AACf,gBAAM,kBAAkB;AACxB,gBAAM,OAAO,KAAK,MAAM,gBAAgB,MAAM;AAC9C,iBAAO,KAAK,SAAS,WAAW,IAAI;AACpC;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,gBAAM,UAAU,UAAU;AAC1B,gBAAM,QAAQ,UAAU;AACxB,gBAAM,QAAQ,UAAU;AACxB,cAAI,OAAO;AACX,mBAAS,IAAI,GAAG,IAAI,UAAU,MAAM,QAAQ,KAAK;AAC7C,kBAAM,OAAO,UAAU,MAAM,CAAC;AAC9B,kBAAM,UAAU,KAAK;AACrB,kBAAM,OAAO,KAAK;AAClB,gBAAI,WAAW;AACf,gBAAI,KAAK,MAAM;AACX,oBAAM,WAAW,KAAK,SAAS,SAAS,CAAC,CAAC,OAAO;AACjD,kBAAI,OAAO;AACP,oBAAI,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,SAAS,aAAa;AAC/D,uBAAK,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE;AACtD,sBAAI,KAAK,OAAO,CAAC,EAAE,UAAU,KAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,QAAQ;AACvG,yBAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE;AAAA,kBAC9E;AAAA,gBACJ,OACK;AACD,uBAAK,OAAO,QAAQ;AAAA,oBAChB,MAAM;AAAA,oBACN,MAAM,WAAW;AAAA,kBACrB,CAAC;AAAA,gBACL;AAAA,cACJ,OACK;AACD,4BAAY,WAAW;AAAA,cAC3B;AAAA,YACJ;AACA,wBAAY,KAAK,MAAM,KAAK,QAAQ,KAAK;AACzC,oBAAQ,KAAK,SAAS,SAAS,UAAU,MAAM,CAAC,CAAC,OAAO;AAAA,UAC5D;AACA,iBAAO,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK;AAC9C;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,iBAAO,KAAK,SAAS,KAAK,UAAU,MAAM,UAAU,KAAK;AACzD;AAAA,QACJ;AAAA,QACA,KAAK,aAAa;AACd,gBAAM,iBAAiB;AACvB,iBAAO,KAAK,SAAS,UAAU,KAAK,YAAY,eAAe,MAAM,CAAC;AACtE;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,cAAI,YAAY;AAChB,cAAI,OAAO,UAAU,SAAS,KAAK,YAAY,UAAU,MAAM,IAAI,UAAU;AAC7E,iBAAO,IAAI,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,EAAE,SAAS,QAAQ;AAC3D,wBAAY,OAAO,EAAE,CAAC;AACtB,oBAAQ,QAAQ,UAAU,SAAS,KAAK,YAAY,UAAU,MAAM,IAAI,UAAU;AAAA,UACtF;AACA,iBAAO,MAAM,KAAK,SAAS,UAAU,IAAI,IAAI;AAC7C;AAAA,QACJ;AAAA,QACA,SAAS;AACL,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACrB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACX,OACK;AACD,kBAAM,IAAI,MAAM,MAAM;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,QAAQ,UAAU;AAC1B,eAAW,YAAY,KAAK;AAC5B,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,QAAQ,OAAO,CAAC;AAEtB,UAAI,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,GAAG;AAC/G,cAAM,MAAM,KAAK,QAAQ,WAAW,UAAU,MAAM,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAG,KAAK;AACtF,YAAI,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAAS,MAAM,IAAI,GAAG;AAC7H,iBAAO,OAAO;AACd;AAAA,QACJ;AAAA,MACJ;AACA,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK,UAAU;AACX,gBAAM,cAAc;AACpB,iBAAO,SAAS,KAAK,YAAY,IAAI;AACrC;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,WAAW;AACjB,iBAAO,SAAS,KAAK,SAAS,IAAI;AAClC;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,iBAAO,SAAS,KAAK,UAAU,MAAM,UAAU,OAAO,KAAK,YAAY,UAAU,QAAQ,QAAQ,CAAC;AAClG;AAAA,QACJ;AAAA,QACA,KAAK,SAAS;AACV,gBAAM,aAAa;AACnB,iBAAO,SAAS,MAAM,WAAW,MAAM,WAAW,OAAO,WAAW,IAAI;AACxE;AAAA,QACJ;AAAA,QACA,KAAK,UAAU;AACX,gBAAM,cAAc;AACpB,iBAAO,SAAS,OAAO,KAAK,YAAY,YAAY,QAAQ,QAAQ,CAAC;AACrE;AAAA,QACJ;AAAA,QACA,KAAK,MAAM;AACP,gBAAM,UAAU;AAChB,iBAAO,SAAS,GAAG,KAAK,YAAY,QAAQ,QAAQ,QAAQ,CAAC;AAC7D;AAAA,QACJ;AAAA,QACA,KAAK,YAAY;AACb,gBAAM,gBAAgB;AACtB,iBAAO,SAAS,SAAS,cAAc,IAAI;AAC3C;AAAA,QACJ;AAAA,QACA,KAAK,MAAM;AACP,iBAAO,SAAS,GAAG;AACnB;AAAA,QACJ;AAAA,QACA,KAAK,OAAO;AACR,gBAAM,WAAW;AACjB,iBAAO,SAAS,IAAI,KAAK,YAAY,SAAS,QAAQ,QAAQ,CAAC;AAC/D;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,iBAAO,SAAS,KAAK,UAAU,IAAI;AACnC;AAAA,QACJ;AAAA,QACA,SAAS;AACL,gBAAM,SAAS,iBAAiB,MAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,QAAQ;AACrB,oBAAQ,MAAM,MAAM;AACpB,mBAAO;AAAA,UACX,OACK;AACD,kBAAM,IAAI,MAAM,MAAM;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,SAAN,MAAa;AAAA,EACT;AAAA,EACA,YAAYH,UAAS;AACjB,SAAK,UAAUA,YAAW;AAAA,EAC9B;AAAA,EACA,OAAO,mBAAmB,oBAAI,IAAI;AAAA,IAC9B;AAAA,IACA;AAAA,EACJ,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,WAAW,UAAU;AACjB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,MAAM;AACd,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,SAAN,MAAa;AAAA,EACT,WAAW,aAAa;AAAA,EACxB,UAAU,KAAK;AAAA,EACf,QAAQ,KAAK,eAAe,OAAO,KAAK,QAAQ,KAAK;AAAA,EACrD,cAAc,KAAK,eAAe,OAAO,WAAW,QAAQ,WAAW;AAAA,EACvE,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe,MAAM;AACjB,SAAK,IAAI,GAAG,IAAI;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ,UAAU;AACzB,QAAI,SAAS,CAAC;AACd,eAAW,SAAS,QAAQ;AACxB,eAAS,OAAO,OAAO,SAAS,KAAK,MAAM,KAAK,CAAC;AACjD,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK,SAAS;AACV,gBAAM,aAAa;AACnB,qBAAW,QAAQ,WAAW,QAAQ;AAClC,qBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,UACjE;AACA,qBAAW,OAAO,WAAW,MAAM;AAC/B,uBAAW,QAAQ,KAAK;AACpB,uBAAS,OAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,YACjE;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,QACA,KAAK,QAAQ;AACT,gBAAM,YAAY;AAClB,mBAAS,OAAO,OAAO,KAAK,WAAW,UAAU,OAAO,QAAQ,CAAC;AACjE;AAAA,QACJ;AAAA,QACA,SAAS;AACL,gBAAM,eAAe;AACrB,cAAI,KAAK,SAAS,YAAY,cAAc,aAAa,IAAI,GAAG;AAC5D,iBAAK,SAAS,WAAW,YAAY,aAAa,IAAI,EAAE,QAAQ,CAAC,gBAAgB;AAC7E,uBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,WAAW,GAAG,QAAQ,CAAC;AAAA,YAC/E,CAAC;AAAA,UACL,WACS,aAAa,QAAQ;AAC1B,qBAAS,OAAO,OAAO,KAAK,WAAW,aAAa,QAAQ,QAAQ,CAAC;AAAA,UACzE;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM;AACT,UAAM,aAAa,KAAK,SAAS,cAAc,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,EAAE;AAChF,SAAK,QAAQ,CAAC,SAAS;AAEnB,YAAM,OAAO,mBAAK;AAElB,WAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,SAAS;AAElD,UAAI,KAAK,YAAY;AACjB,aAAK,WAAW,QAAQ,CAAC,QAAQ;AAC7B,cAAI,CAAC,IAAI,MAAM;AACX,kBAAM,IAAI,MAAM,yBAAyB;AAAA,UAC7C;AACA,cAAI,cAAc,KAAK;AACnB,kBAAM,eAAe,WAAW,UAAU,IAAI,IAAI;AAClD,gBAAI,cAAc;AAEd,yBAAW,UAAU,IAAI,IAAI,IAAI,YAAaI,OAAM;AAChD,oBAAI,MAAM,IAAI,SAAS,MAAM,MAAMA,KAAI;AACvC,oBAAI,QAAQ,OAAO;AACf,wBAAM,aAAa,MAAM,MAAMA,KAAI;AAAA,gBACvC;AACA,uBAAO;AAAA,cACX;AAAA,YACJ,OACK;AACD,yBAAW,UAAU,IAAI,IAAI,IAAI,IAAI;AAAA,YACzC;AAAA,UACJ;AACA,cAAI,eAAe,KAAK;AACpB,gBAAI,CAAC,IAAI,SAAU,IAAI,UAAU,WAAW,IAAI,UAAU,UAAW;AACjE,oBAAM,IAAI,MAAM,6CAA6C;AAAA,YACjE;AACA,kBAAM,WAAW,WAAW,IAAI,KAAK;AACrC,gBAAI,UAAU;AACV,uBAAS,QAAQ,IAAI,SAAS;AAAA,YAClC,OACK;AACD,yBAAW,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS;AAAA,YAC1C;AACA,gBAAI,IAAI,OAAO;AACX,kBAAI,IAAI,UAAU,SAAS;AACvB,oBAAI,WAAW,YAAY;AACvB,6BAAW,WAAW,KAAK,IAAI,KAAK;AAAA,gBACxC,OACK;AACD,6BAAW,aAAa,CAAC,IAAI,KAAK;AAAA,gBACtC;AAAA,cACJ,WACS,IAAI,UAAU,UAAU;AAC7B,oBAAI,WAAW,aAAa;AACxB,6BAAW,YAAY,KAAK,IAAI,KAAK;AAAA,gBACzC,OACK;AACD,6BAAW,cAAc,CAAC,IAAI,KAAK;AAAA,gBACvC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,iBAAiB,OAAO,IAAI,aAAa;AACzC,uBAAW,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,UAC3C;AAAA,QACJ,CAAC;AACD,aAAK,aAAa;AAAA,MACtB;AAEA,UAAI,KAAK,UAAU;AACf,cAAM,WAAW,KAAK,SAAS,YAAY,IAAI,UAAU,KAAK,QAAQ;AACtE,mBAAW,QAAQ,KAAK,UAAU;AAC9B,gBAAM,eAAe,KAAK,SAAS,IAAI;AACvC,gBAAM,cAAc;AACpB,gBAAM,eAAe,SAAS,WAAW;AAEzC,mBAAS,WAAW,IAAI,IAAIA,UAAS;AACjC,gBAAI,MAAM,aAAa,MAAM,UAAUA,KAAI;AAC3C,gBAAI,QAAQ,OAAO;AACf,oBAAM,aAAa,MAAM,UAAUA,KAAI;AAAA,YAC3C;AACA,mBAAO,OAAO;AAAA,UAClB;AAAA,QACJ;AACA,aAAK,WAAW;AAAA,MACpB;AACA,UAAI,KAAK,WAAW;AAChB,cAAM,YAAY,KAAK,SAAS,aAAa,IAAI,WAAW,KAAK,QAAQ;AACzE,mBAAW,QAAQ,KAAK,WAAW;AAC/B,gBAAM,gBAAgB,KAAK,UAAU,IAAI;AACzC,gBAAM,eAAe;AACrB,gBAAM,gBAAgB,UAAU,YAAY;AAE5C,oBAAU,YAAY,IAAI,IAAIA,UAAS;AACnC,gBAAI,MAAM,cAAc,MAAM,WAAWA,KAAI;AAC7C,gBAAI,QAAQ,OAAO;AACf,oBAAM,cAAc,MAAM,WAAWA,KAAI;AAAA,YAC7C;AACA,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,aAAK,YAAY;AAAA,MACrB;AAEA,UAAI,KAAK,OAAO;AACZ,cAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,OAAO;AAChD,mBAAW,QAAQ,KAAK,OAAO;AAC3B,gBAAM,YAAY,KAAK,MAAM,IAAI;AACjC,gBAAM,WAAW;AACjB,gBAAM,WAAW,MAAM,QAAQ;AAC/B,cAAI,OAAO,iBAAiB,IAAI,IAAI,GAAG;AACnC,kBAAM,QAAQ,IAAI,CAAC,QAAQ;AACvB,kBAAI,KAAK,SAAS,OAAO;AACrB,uBAAO,QAAQ,QAAQ,UAAU,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,CAAAC,SAAO;AAC3D,yBAAO,SAAS,KAAK,OAAOA,IAAG;AAAA,gBACnC,CAAC;AAAA,cACL;AACA,oBAAM,MAAM,UAAU,KAAK,OAAO,GAAG;AACrC,qBAAO,SAAS,KAAK,OAAO,GAAG;AAAA,YACnC;AAAA,UACJ,OACK;AACD,kBAAM,QAAQ,IAAI,IAAID,UAAS;AAC3B,kBAAI,MAAM,UAAU,MAAM,OAAOA,KAAI;AACrC,kBAAI,QAAQ,OAAO;AACf,sBAAM,SAAS,MAAM,OAAOA,KAAI;AAAA,cACpC;AACA,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AACA,aAAK,QAAQ;AAAA,MACjB;AAEA,UAAI,KAAK,YAAY;AACjB,cAAME,cAAa,KAAK,SAAS;AACjC,cAAM,iBAAiB,KAAK;AAC5B,aAAK,aAAa,SAAU,OAAO;AAC/B,cAAI,SAAS,CAAC;AACd,iBAAO,KAAK,eAAe,KAAK,MAAM,KAAK,CAAC;AAC5C,cAAIA,aAAY;AACZ,qBAAS,OAAO,OAAOA,YAAW,KAAK,MAAM,KAAK,CAAC;AAAA,UACvD;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,WAAK,WAAW,kCAAK,KAAK,WAAa;AAAA,IAC3C,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,WAAW,KAAK;AACZ,SAAK,WAAW,kCAAK,KAAK,WAAa;AACvC,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAKN,UAAS;AAChB,WAAO,OAAO,IAAI,KAAKA,YAAW,KAAK,QAAQ;AAAA,EACnD;AAAA,EACA,OAAO,QAAQA,UAAS;AACpB,WAAO,QAAQ,MAAM,QAAQA,YAAW,KAAK,QAAQ;AAAA,EACzD;AAAA,EACA,eAAeD,QAAOI,SAAQ;AAC1B,WAAO,CAAC,KAAKH,aAAY;AACrB,YAAM,UAAU,mBAAKA;AACrB,YAAM,MAAM,kCAAK,KAAK,WAAa;AAEnC,UAAI,KAAK,SAAS,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACzD,YAAI,CAAC,IAAI,QAAQ;AACb,kBAAQ,KAAK,oHAAoH;AAAA,QACrI;AACA,YAAI,QAAQ;AAAA,MAChB;AACA,YAAM,aAAa,KAAK,SAAS,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,KAAK;AAE1D,UAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;AAC5C,eAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;AAAA,MACjF;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,WAAW,IAAI,MAAM,0CACtB,OAAO,UAAU,SAAS,KAAK,GAAG,IAAI,mBAAmB,CAAC;AAAA,MACpE;AACA,UAAI,IAAI,OAAO;AACX,YAAI,MAAM,UAAU;AAAA,MACxB;AACA,UAAI,IAAI,OAAO;AACX,eAAO,QAAQ,QAAQ,IAAI,QAAQ,IAAI,MAAM,WAAW,GAAG,IAAI,GAAG,EAC7D,KAAK,CAAAO,SAAOR,OAAMQ,MAAK,GAAG,CAAC,EAC3B,KAAK,YAAU,IAAI,aAAa,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,MAAM,EAChH,KAAK,YAAUJ,QAAO,QAAQ,GAAG,CAAC,EAClC,KAAK,UAAQ,IAAI,QAAQ,IAAI,MAAM,YAAY,IAAI,IAAI,IAAI,EAC3D,MAAM,UAAU;AAAA,MACzB;AACA,UAAI;AACA,YAAI,IAAI,OAAO;AACX,gBAAM,IAAI,MAAM,WAAW,GAAG;AAAA,QAClC;AACA,cAAM,SAASJ,OAAM,KAAK,GAAG;AAC7B,YAAI,IAAI,YAAY;AAChB,eAAK,WAAW,QAAQ,IAAI,UAAU;AAAA,QAC1C;AACA,YAAI,OAAOI,QAAO,QAAQ,GAAG;AAC7B,YAAI,IAAI,OAAO;AACX,iBAAO,IAAI,MAAM,YAAY,IAAI;AAAA,QACrC;AACA,eAAO;AAAA,MACX,SACO,GAAG;AACN,eAAO,WAAW,CAAC;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS,QAAQ,OAAO;AACpB,WAAO,CAAC,MAAM;AACV,QAAE,WAAW;AACb,UAAI,QAAQ;AACR,cAAM,MAAM,mCACN,OAAO,EAAE,UAAU,IAAI,IAAI,IAC3B;AACN,YAAI,OAAO;AACP,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC9B;AACA,eAAO;AAAA,MACX;AACA,UAAI,OAAO;AACP,eAAO,QAAQ,OAAO,CAAC;AAAA,MAC3B;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB,IAAI,OAAO;AAClC,SAAS,OAAO,KAAK,KAAK;AACtB,SAAO,eAAe,MAAM,KAAK,GAAG;AACxC;AAMA,OAAO,UACH,OAAO,aAAa,SAAUH,UAAS;AACnC,iBAAe,WAAWA,QAAO;AACjC,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACX;AAIJ,OAAO,cAAc;AACrB,OAAO,WAAW;AAIlB,OAAO,MAAM,YAAa,MAAM;AAC5B,iBAAe,IAAI,GAAG,IAAI;AAC1B,SAAO,WAAW,eAAe;AACjC,iBAAe,OAAO,QAAQ;AAC9B,SAAO;AACX;AAIA,OAAO,aAAa,SAAU,QAAQ,UAAU;AAC5C,SAAO,eAAe,WAAW,QAAQ,QAAQ;AACrD;AAQA,OAAO,cAAc,eAAe;AAIpC,OAAO,SAAS;AAChB,OAAO,SAAS,QAAQ;AACxB,OAAO,WAAW;AAClB,OAAO,eAAe;AACtB,OAAO,QAAQ;AACf,OAAO,QAAQ,OAAO;AACtB,OAAO,YAAY;AACnB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,IAAM,UAAU,OAAO;AACvB,IAAM,aAAa,OAAO;AAC1B,IAAM,MAAM,OAAO;AACnB,IAAM,aAAa,OAAO;AAC1B,IAAM,cAAc,OAAO;AAE3B,IAAM,SAAS,QAAQ;AACvB,IAAM,QAAQ,OAAO;;;AC50ErB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,UAAU,KAAK,cAAc,KAAK,UAAU,MAAM,MAAM,GAAG,IAAI,GAAG,MAAM,GAAI,EAAE,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,qBAAqB,GAAG,YAAY,CAAC,CAAC;AAC/I,SAAK,cAAc,KAAK,QAAQ,KAAK,UAAU,KAAK,GAAG,IAAI,YAAU,SAAS,qBAAqB,gBAAgB,CAAC;AAAA,EACtH;AAAA,EACA,yBAAyB;AACvB,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA0B;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,OAAO,CAAC;AAAA,MACrD,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,SAAS,SAAS,4DAA4D;AAC1F,mBAAO,IAAI,uBAAuB;AAAA,UACpC,CAAC;AACD,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAa,YAAY,GAAG,GAAG,IAAI,OAAO,CAAC;AAC1D,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,GAAG,GAAG,IAAI,WAAW,CAAC;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,cAAc,CAAI,SAAS;AAAA,MAC3B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAGhE,IAAM,uBAAN,MAA2B;AAAC;AAC5B,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,UAAU;AACzB,QAAI,SAAS,MAAM;AACjB,cAAQ;AAAA,IACV;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW;AAAA,IACb;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,MAAM,6DAA6D,OAAO,KAAK,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,cAAQ,MAAM,4DAA4D,OAAO,QAAQ,GAAG;AAC5F,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,WAAW,OAAO,QAAQ;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI;AAAA,CACH,SAAUQ,cAAa;AACtB,EAAAA,aAAY,aAAa,IAAI;AAC7B,EAAAA,aAAY,eAAe,IAAI;AAC/B,EAAAA,aAAY,aAAa,IAAI;AAC/B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAChE,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAG1D,IAAM,0BAA0B;AAChC,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,0BAA0B;AAChC,IAAM,sCAAsC;AAC5C,IAAM,4BAA4B;AAElC,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,mBAAN,cAA+B,UAAS;AAAA,EACtC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,4CAA4C;AACjD,SAAK,yCAAyC;AAAA,EAChD;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,kCACX,KAAK,yBACL;AAAA,EAEP;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EACA,YAAY,kBAAkB,YAAYC,UAAS,UAAU,iBAAiB,MAAM,WAAW;AAC7F,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAAA,MAC5B,UAAU,IAAI,UAAS;AAAA,IACzB;AACA,SAAK,wBAAwB;AAAA,MAC3B,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,SAAK,0BAA0B;AAAA,MAC7B,aAAa;AAAA,IACf;AACA,SAAK,4BAA4B;AAAA,MAC/B,iBAAiB;AAAA,IACnB;AACA,SAAK,wBAAwB;AAAA,MAC3B,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,eAAe,KAAK;AAAA,MACpB,kBAAkB;AAAA,IACpB;AACA,SAAK,yBAAyB;AAAA,MAC5B,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,MACT,gBAAgB;AAAA,IAClB;AACA,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,UAAU,KAAK,SAAS,aAAa;AAC1C,SAAK,UAAUA;AAAA,EACjB;AAAA,EACA,MAAM,UAAU,eAAe,KAAK,uBAAuB;AACzD,UAAM;AAAA,MACJ;AAAA,MACA,QAAAC;AAAA,MACA;AAAA,MACA,SAAAC;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,kCACjB,KAAK,UACL,aAAa;AAElB,UAAM,WAAW,cAAc,YAAY,KAAK,YAAY,IAAI,UAAS;AACzE,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,KAAK,6BAA6B,QAAQ;AAAA,IAC5D;AACA,QAAIA,UAAS;AACX,WAAK,WAAW,KAAK,0BAA0B,QAAQ;AAAA,IACzD;AACA,UAAM,UAAU,KAAK,gBAAgB,QAAQ;AAC7C,UAAM,UAAU,aAAa,KAAK,WAAW,OAAO,IAAI;AACxD,UAAM,YAAY,QAAQ,KAAK,WAAW,OAAO,IAAI;AACrD,UAAMC,UAAS,KAAK,YAAY,WAAW,eAAeF,OAAM;AAChE,UAAM,YAAY,mBAAmBE,UAAS,KAAK,UAAU,SAAS,KAAK,iBAAiBA,OAAM;AAClG,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,OAAO,SAASH,WAAU,KAAK,wBAAwB,kBAAkB;AACvE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,OAAAI;AAAA,MACA;AAAA,MACA,SAAAF;AAAA,MACA;AAAA,IACF,IAAIF;AACJ,QAAI,WAAW;AACb,WAAK,gBAAgB,SAAS,kBAAkB,iDAC3C,KAAK,4BACL,KAAK,mBACL,iBACJ;AAAA,IACH;AACA,QAAII,QAAO;AACT,WAAK,YAAY,SAAS,kCACrB,KAAK,wBACL,aACJ;AAAA,IACH;AACA,QAAIF,UAAS;AACX,WAAK,cAAc,SAAS,kCACvB,KAAK,0BACL,eACJ;AAAA,IACH;AACA,SAAK,UAAU,OAAO;AAAA,EACxB;AAAA,EACA,SAAS;AACP,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,UAAU,KAAK;AACb,QAAI,CAAC,KAAK,MAAM;AACd,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO,KAAK,KAAK,IAAI,KAAK;AAAA,MACxB,cAAc;AAAA,IAChB,CAAC,EAAE,KAAK,IAAI,cAAY,KAAK,gBAAgB,KAAK,QAAQ,CAAC,CAAC;AAAA,EAC9D;AAAA,EACA,UAAU,SAAS;AACjB,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,OAAO,MAAM,sBAAsB,aAAa;AAClF;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU;AAAA,IACZ;AACA,UAAM,qBAAqB,QAAQ,iBAAiB,oCAAoC;AACxF,UAAM,UAAU,QAAQ,KAAK,oBAAoB,OAAK,EAAE,UAAU,IAAI,eAAe,CAAC;AACtF,UAAM,kBAAkB,OAAO;AAAA,EACjC;AAAA,EACA,WAAW,MAAM;AACf,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,SAAS,cAAc,UAAU;AAClD,aAAS,YAAY;AACrB,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,6BAA6B,UAAU;AACrC,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,8CAA8C,MAAM;AACvE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,aAAO,IAAI,GAAG,KAAK,UAAU;AAAA,IAC/B;AACA,qBAAiB,4CAA4C;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,UAAU;AAClC,UAAM,mBAAmB;AACzB,QAAI,iBAAiB,2CAA2C,MAAM;AACpE,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,SAAS;AAC7B,aAAS,OAAO,SAAU,MAAM,UAAU,WAAW;AACnD,aAAO,aAAa,YAAY,wBAAwB,IAAI,WAAW,YAAY,KAAK,MAAM,MAAM,UAAU,SAAS;AAAA,IACzH;AACA,qBAAiB,yCAAyC;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,KAAK,UAAU;AAC7B,UAAM,mBAAmB,IAAI,YAAY,KAAK;AAC9C,UAAM,qBAAqB,mBAAmB,KAAK,IAAI,UAAU,mBAAmB,CAAC,IAAI;AACzF,UAAM,iBAAiB,mBAAmB,YAAY,GAAG;AACzD,UAAM,iBAAiB,iBAAiB,KAAK,mBAAmB,UAAU,iBAAiB,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAC9G,UAAM,eAAe,eAAe,YAAY,GAAG;AACnD,UAAM,YAAY,eAAe,KAAK,eAAe,UAAU,eAAe,CAAC,IAAI;AACnF,WAAO,CAAC,CAAC,aAAa,cAAc,OAAO,QAAQ,YAAY,OAAO,WAAW,UAAU;AAAA,EAC7F;AAAA,EACA,YAAY,MAAM,eAAeD,UAAS,OAAO;AAG/C,QAAI,cAAc,UAAU;AAC1B,aAAO,IAAI;AAAA,QACT,UAAU,cAAc;AAAA,MAC1B,CAAC;AACD,aAAO,cAAc;AAAA,IACvB;AACA,WAAOA,UAAS,OAAO,YAAY,MAAM,aAAa,IAAI,OAAO,MAAM,MAAM,aAAa;AAAA,EAC5F;AAAA,EACA,WAAW,MAAM;AACf,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,cAAc,eAAe,OAAO,UAAU,uBAAuB,aAAa;AAC3F,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,WAAO,UAAU,mBAAmB,IAAI;AAAA,EAC1C;AAAA,EACA,YAAY,SAASD,UAAS;AAC5B,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,OAAO,wBAAwB,aAAa;AAC9E,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACrC;AACA,wBAAoB,SAASA,QAAO;AAAA,EACtC;AAAA,EACA,gBAAgB,SAAS,kBAAkBA,UAAS;AAClD,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,gBAAgB,aAAa;AACtC,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AACA,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACrD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAIA;AAEJ,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,aAAa,YAAY,KAAK,CAAC;AAErC,YAAM,oBAAoB,SAAS,cAAc,KAAK;AACtD,wBAAkB,MAAM,WAAW;AACnC,iBAAW,WAAW,aAAa,mBAAmB,UAAU;AAChE,wBAAkB,YAAY,UAAU;AAExC,YAAM,wBAAwB,SAAS,cAAc,KAAK;AAC1D,4BAAsB,MAAM,WAAW;AACvC,4BAAsB,MAAM,MAAM;AAClC,4BAAsB,MAAM,QAAQ;AACpC,4BAAsB,MAAM,UAAU;AACtC,4BAAsB,MAAM,aAAa;AACzC,wBAAkB,sBAAsB,aAAa,qBAAqB;AAE1E,iBAAW,cAAc,MAAM,sBAAsB,MAAM,UAAU;AACrE,iBAAW,aAAa,MAAM,sBAAsB,MAAM,UAAU;AAEpE,UAAI;AAGJ,UAAI,iBAAiB;AACnB,cAAM,eAAe,iBAAiB,gBAAgB,eAAe;AACrE,0BAAkB,aAAa;AAAA,MACjC,WAES,gBAAgB;AACvB,0BAAkB,iBAAiB,mBAAmB,cAAc;AAAA,MACtE,OAEK;AACH,cAAM,eAAe,iBAAiB,gBAAgB,wBAAwB;AAC9E,0BAAkB,aAAa;AAAA,MACjC;AAEA,UAAI;AAEJ,sBAAgB,UAAU,QAAQ,UAAQ;AACxC,aAAK,cAAc,MAAM,sBAAsB,MAAM,UAAU;AAC/D,8BAAsB,YAAY,IAAI;AACtC,4BAAoB,IAAI,YAAY,MAAM;AAAA,UACxC,MAAM,MAAM,WAAW;AAAA,QACzB,CAAC;AAAA,MACH,CAAC;AAED,sBAAgB,UAAU,MAAM,kBAAkB,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,cAAc,SAASA,WAAU,KAAK,yBAAyB;AAC7D,QAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG;AACrC;AAAA,IACF;AACA,QAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,eAAe,aAAa;AAC/E,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AACA,UAAM,kBAAkB,QAAQ,iBAAiB,UAAU;AAC3D,QAAI,gBAAgB,WAAW,GAAG;AAChC;AAAA,IACF;AACA,YAAQ,WAAWA,QAAO;AAC1B,YAAQ,IAAI;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,UAAU;AACxB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI;AACJ,WAAO,SAAS,MAAM,IAAI,EAAE,IAAI,UAAQ;AACtC,UAAI,iBAAiB;AACrB,UAAI,KAAK,SAAS,GAAG;AACnB,yBAAiB,MAAM,cAAc,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,MAAM,GAAG,cAAc;AAAA,MAC7G;AACA,UAAI,MAAM,WAAW,GAAG;AACtB,sBAAc;AAAA,MAChB;AACA,aAAO,iBAAiB,KAAK,UAAU,cAAc,IAAI;AAAA,IAC3D,CAAC,EAAE,KAAK,IAAI;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,SAAS,mBAAmB,CAAC,GAAM,SAAS,mBAAmB,CAAC,GAAM,SAAS,gBAAgB,CAAC,GAAM,SAAS,WAAW,GAAM,SAAS,gBAAgB,GAAM,SAAc,YAAY,CAAC,GAAM,SAAY,YAAY,CAAC;AAAA,IAChQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,oBAAoB,KAAK,sBAAsB,KAAK;AAAA,EAC3D;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU,KAAK,sBAAsB,KAAK;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,KAAK,sBAAsB,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,KAAK,sBAAsB,KAAK;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,KAAK,sBAAsB,KAAK;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,KAAK,sBAAsB,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB,KAAK,sBAAsB,KAAK;AAAA,EACxD;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,KAAK,sBAAsB,KAAK;AAAA,EACtD;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,KAAK,sBAAsB,KAAK;AAAA,EACtD;AAAA,EACA,YAAY,SAAS,iBAAiB,kBAAkB;AACtD,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAExB,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,OAAO,IAAI,aAAa;AAC7B,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,QAAQ;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW;AAChB;AAAA,IACF;AACA,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,UAAU;AACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;AAC3B,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,YAAY,CAAC;AAAA,EAClG;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACM,OAAO,UAAU,aAAa,OAAO;AAAA;AACzC,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,QACd,kBAAkB,KAAK;AAAA,MACzB;AACA,YAAM,gBAAgB;AAAA,QACpB,WAAW,KAAK;AAAA,QAChB,kBAAkB;AAAA,UAChB,iBAAiB,KAAK;AAAA,UACtB,gBAAgB,KAAK;AAAA,QACvB;AAAA,QACA,OAAO,KAAK;AAAA,QACZ,cAAc,KAAK;AAAA,QACnB,SAAS,KAAK;AAAA,QACd,gBAAgB,KAAK;AAAA,MACvB;AACA,YAAM,SAAS,MAAM,KAAK,gBAAgB,MAAM,UAAU,aAAa;AACvE,WAAK,QAAQ,cAAc,YAAY;AACvC,WAAK,cAAc;AACnB,WAAK,gBAAgB,OAAO,KAAK,QAAQ,eAAe,eAAe,KAAK,gBAAgB;AAC5F,WAAK,MAAM,KAAK;AAAA,IAClB;AAAA;AAAA,EACA,sBAAsB,OAAO;AAC3B,WAAO,SAAS,QAAQ,GAAG,OAAO,KAAK,CAAC,OAAO;AAAA,EACjD;AAAA,EACA,aAAa;AACX,SAAK,OAAO,KAAK,IAAI;AAAA,EACvB;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB,UAAU,KAAK,GAAG,EAAE,UAAU;AAAA,MACjD,MAAM,cAAY;AAChB,aAAK,OAAO,QAAQ,EAAE,KAAK,MAAM;AAC/B,eAAK,KAAK,KAAK,QAAQ;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,MACA,OAAO,WAAS,KAAK,MAAM,KAAK,KAAK;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,OAAO,KAAK,QAAQ,cAAc,WAAW,IAAI;AAAA,EACxD;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,KAAK,QAAQ,eAAe,YAAY,WAAW;AACvE,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,kBAAkB,KAAK;AAAA,QACvB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,KAAK,QAAQ,eAAe,YAAY,WAAW;AACvE,WAAK,iBAAiB,KAAK,QAAQ,eAAe;AAAA,QAChD,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,SAAS,QAAQ;AAC9B,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,UAAU,kBAAkB,QAAQ,SAAS,CAAC,MAAM;AAC1D,kBAAY,KAAK,CAAC,EAAE,UAAU,IAAI,GAAG,OAAO;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,iBAAiB,SAASA,UAAS;AACjC,UAAM,cAAc,QAAQ,iBAAiB,KAAK;AAClD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,aAAO,KAAKA,QAAO,EAAE,QAAQ,YAAU;AACrC,cAAM,iBAAiBA,SAAQ,MAAM;AACrC,YAAI,gBAAgB;AAClB,gBAAM,gBAAgB,KAAK,WAAW,MAAM;AAC5C,sBAAY,KAAK,CAAC,EAAE,aAAa,eAAe,eAAe,SAAS,CAAC;AAAA,QAC3E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,aAAa,MAAM,MAAM,UAAU;AACzC,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,QAAI,MAAM,MAAM,SAAS;AACzB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,YAAM,IAAI,QAAQ,IAAI,OAAO,WAAW,CAAC,CAAC,GAAG,MAAM,WAAW,CAAC,EAAE,YAAY,CAAC;AAAA,IAChF;AACA,QAAI,IAAI,MAAM,GAAG,CAAC,MAAM,KAAK;AAC3B,YAAM,IAAI,MAAM,CAAC;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAC3J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,MAC9C,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,QACd,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,OAAO;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,QACd,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,cAAc,YAAY,iBAAiB,kBAAkB,MAAM;AAC7E,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,OAAO;AAAA,EACd;AAAA,EACM,UAAU,OAAOA,UAAS;AAAA;AAC9B,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,MAAM,6DAA6D,OAAO,KAAK,GAAG;AAC1F,eAAO;AAAA,MACT;AACA,YAAM,WAAW,MAAM,KAAK,gBAAgB,MAAM,OAAOA,QAAO;AAChE,WAAK,KAAK,SAAS,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,gBAAgB,OAAO,KAAK,WAAW,eAAeA,UAAS,KAAK,gBAAgB,CAAC;AAC3I,aAAO,KAAK,aAAa,wBAAwB,QAAQ;AAAA,IAC3D;AAAA;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAqB,cAAc,EAAE,GAAM,kBAAqB,YAAY,EAAE,GAAM,kBAAkB,iBAAiB,EAAE,GAAM,kBAAqB,kBAAkB,EAAE,GAAM,kBAAqB,QAAQ,EAAE,CAAC;AAAA,IAClP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAqB,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AACnG,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,QAAQ,sBAAsB;AACnC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,iBAAiB,sBAAsB,UAAU,CAAC,GAAG,sBAAsB,oBAAoB,CAAC,GAAG,sBAAsB,iBAAiB,CAAC,GAAG;AAAA,QACxJ,SAAS;AAAA,QACT,UAAU,sBAAsB,oBAAoB,CAAC;AAAA,MACvD,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,sBAAsB,YAAY,gBAAgB;AAAA,MAC9D,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,WAAW;AAChB,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AAAA,MACtF,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,0BAA0B,cAAc,mBAAmB,YAAY;AAAA,IACnF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS;AAAA,MACT,cAAc;AAAA,IAChB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUK,aAAY;AACrB,MAAI;AACJ,GAAC,SAAUC,gBAAe;AAIxB,IAAAA,eAAc,QAAQ,IAAI;AAI1B,IAAAA,eAAc,OAAO,IAAI;AAIzB,IAAAA,eAAc,YAAY,IAAI;AAO9B,IAAAA,eAAc,SAAS,IAAI;AAAA,EAC7B,GAAG,gBAAgBD,YAAW,kBAAkBA,YAAW,gBAAgB,CAAC,EAAE;AAC9E,MAAI;AACJ,GAAC,SAAUE,QAAO;AAIhB,IAAAA,OAAM,MAAM,IAAI;AAIhB,IAAAA,OAAM,QAAQ,IAAI;AAIlB,IAAAA,OAAM,MAAM,IAAI;AAIhB,IAAAA,OAAM,SAAS,IAAI;AAInB,IAAAA,OAAM,SAAS,IAAI;AAAA,EACrB,GAAG,QAAQF,YAAW,UAAUA,YAAW,QAAQ,CAAC,EAAE;AACtD,MAAI;AACJ,GAAC,SAAUG,WAAU;AACnB,IAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,IAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,IAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,IAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,IAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAAA,EACpC,GAAG,WAAWH,YAAW,aAAaA,YAAW,WAAW,CAAC,EAAE;AACjE,GAAG,eAAe,aAAa,CAAC,EAAE;", "names": ["lexer", "options", "text", "block", "parser", "args", "ret", "walkTokens", "src", "PrismPlugin", "options", "inline", "mermaid", "marked", "katex", "MermaidAPI", "SecurityLevel", "Theme", "LogLevel"]}