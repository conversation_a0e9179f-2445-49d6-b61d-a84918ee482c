﻿namespace AdminPortalBackend.Core.Models;

public class NavToSqlUpdateQueue
{
    public int QueueId { get; set; }
    public string LogTableName { get; set; }
    public string Company { get; set; }
    public int TableNo { get; set; }
    public string TableCaption { get; set; }
    public string DWServiceName { get; set; }
    public int PrimaryKeyFieldNo { get; set; }
    public string PrimaryKeyFieldCaption { get; set; }
    public string DWKeyFieldName { get; set; }
    public string PrimaryKeyFieldValue { get; set; }
    public string TypeofChange { get; set; }
    public string Status { get; set; }
}
