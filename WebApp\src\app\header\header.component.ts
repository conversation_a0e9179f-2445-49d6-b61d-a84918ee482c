import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { IUser } from '../app.interface';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { UserNotificationDialogComponent } from '../dialog/user-notification-dialog/user-notification-dialog.component';

interface AutoCompleteCompleteEvent {
  originalEvent: Event;
  query: string;
}


@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, AutoCompleteModule, FormsModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {
  isMenuOpen: boolean = false;
  selectedItems: any[] | undefined;
  items: any[] | undefined;
  @Output() chatToggle = new EventEmitter<void>();
  isProfileMenuToggled: Boolean = false;
  user: IUser = null;
  apps = [
    { name: 'Video', icon: 'fa-solid fa-photo-film', url: '/video' },
    { name: 'Projects', icon: 'fa-brands fa-r-project', url: '/project' },
    { name: 'SyncHub', icon: 'fa-solid fa-gears', url: '/sync-hub' },
  ];

  search(event: AutoCompleteCompleteEvent) {
    this.items = [...Array(10).keys()].map((item) => event.query + '-' + item);
  }

  toggleChat() {
    this.chatToggle.emit(); // Emit an event to toggle chat
  }



  constructor(private router: Router, private dialog: MatDialog) {
  }

  // Method to open the notification dialog
  notificationDialog(): void {
    const dialogRef = this.dialog.open(UserNotificationDialogComponent, {
      width: '300px',
      height: 'calc(100% - 54px)',
      position: { right: '0', top: '54px' },
      panelClass: 'custom-dialog-container'
    });

    // Handle the after close event
    dialogRef.afterClosed().subscribe(result => {
      console.log('Dialog was closed');
    });
  }
  toggleProfileMenu() {
    this.isProfileMenuToggled = !this.isProfileMenuToggled
    console.log(this.isProfileMenuToggled)
  }


  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }
  gotoDeveloperSite() {
    this.router.navigate(['./developer'])
    this.toggleProfileMenu()
  }

}
