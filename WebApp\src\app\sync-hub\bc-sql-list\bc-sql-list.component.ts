import { Component, OnInit } from '@angular/core';
import { TableModule } from 'primeng/table';
import {
  ConnectionIntegration,
  ConnectionIntegrationServiceProxy,
  DbConnectionDto,
  DbConnectionServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { CustomConfirmDialogComponent } from '../../dialog/custom-confirm-dialog/custom-confirm-dialog.component';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { DropdownModule } from 'primeng/dropdown';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';
import { JoblogsViewDialogComponent } from '../dialog/joblogs-view-dialog/joblogs-view-dialog.component';
import { BcSqlStateService } from '../../bc-sql-state.service';
import { SignalRService } from '../../signalr.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-bc-sql-list',
  standalone: true,
  imports: [TableModule, FormsModule, CommonModule, TooltipModule, DropdownModule, ButtonModule],
  templateUrl: './bc-sql-list.component.html',
  styleUrls: ['./bc-sql-list.component.css'],
  providers: [DbConnectionServiceProxy]
})
export class BcSqlListComponent implements OnInit {
  sqlListData: ConnectionIntegration[] = [];
  groupedSqlListData: any[] = []; // Grouped data for p-table
  selectedConnectionType: string;
  filteredSqlListData: ConnectionIntegration[] = [];
  dbConnections: DbConnectionDto[] = [];
  connectionType: string[] = ['All', 'NAV', 'BC Web Service', 'BC Rest API'];
  companyType: string[] = ["All"]
  selectedCompanyType: string = ''
  comFilteredData: any
  filterKey: string;
  localStorageFilterkey: string = '';
  isDataLoaded = false
  searchByEntityKey: string;
  searchByEntityKeyList: ConnectionIntegration[] = [];
  isFilterAdded = false;
  highlightedGuid: string | null = null;
  executingJobs: Set<string> = new Set(); // Track currently executing jobs

  constructor(
    private dialog: MatDialog,
    private _connectionIntegrationService: ConnectionIntegrationServiceProxy,
    private router: Router,
    private _dbConnectionService: DbConnectionServiceProxy,
    private bcSqlStateService: BcSqlStateService,
    private signalRService: SignalRService,  // Add SignalR service
    private toastr: ToastrService
  ) {
    // ... existing constructor code ...

    // Subscribe to SignalR messages to track job execution
    this.signalRService.message$.subscribe(message => {
      if (message.message) {
        const [integrationId] = message.message.split('~');
        if (message.message.includes('Merging Completed Successfully')) {
          this.executingJobs.delete(integrationId);
          this.toastr.success('Job Completed Successfully');
        }
        else if (message.message.includes('Failed')) {
          this.executingJobs.delete(integrationId);
          this.toastr.error('Job Failed');
        }
        else if (message.message.includes('Cancelled')) {
          this.executingJobs.delete(integrationId);
          this.toastr.success('Job Cancelled Successfully');
        }
        else {
          this.executingJobs.add(integrationId);
        }
      }
    });
  }

  ngOnInit(): void {
    this.filterKey = localStorage.getItem('filterKey') || 'All';
    this.localStorageFilterkey = localStorage.getItem('searchByEntityKey') || '';
    this.selectedConnectionType = this.filterKey;
    this.selectedCompanyType = localStorage.getItem("selectedCompanyType") || 'All'
    //console.log(this.localStorageFilterkey);
    this.loadAllData();

    // Subscribe to state changes
    this.bcSqlStateService.currentState$.subscribe(state => {
      this.highlightedGuid = state.highlightedGuid;
      if (state.scrollPosition) {
        setTimeout(() => {
          window.scrollTo(0, state.scrollPosition);
        }, 100);
      }
    });
  }

  getSettings(data: string) {
    let div = document.getElementsByClassName('dataflow')[0];
    if (data) {
      let obj = JSON.parse(data);

      if (obj.SqlToBc && obj.BcToSql) {
        return 'pi-arrow-right-arrow-left';
      } else if (obj.SqlToBc) {
        return 'pi-arrow-left';
      } else if (obj.BcToSql) {
        return 'pi-arrow-right';
      }
    }
    (div as HTMLElement).style.display = 'none';
    return '';
  }

  loadAllData() {
    this._connectionIntegrationService.getBcSqlAll().subscribe((res) => {
      this.sqlListData = res;
      this.filteredSqlListData = res;
      this.searchByEntityKeyList = res;

      this._dbConnectionService.getAll().subscribe((res) => {
        this.groupedSqlListData = this.groupBySourceDatabase(this.sqlListData);
        this.groupedSqlListData = this.sortBySourceDatabase(this.groupedSqlListData);
        if (res) {
          this.isDataLoaded = true
        }
        this.dbConnections = res;
        this.onConnTypeChange();

        this.onCompanyTypeChange()
        this.loadExpandedGroups();
        this.searchByEntityKey = this.localStorageFilterkey;
        this.OnSearchByEntityKeyChange(this.searchByEntityKey);
      });


    });
  }

  navigateToAddIntegration() {
    this.router.navigate(['/sync-hub/bc-sql']);
  }

  updateBcSql(guid: string) {
    // Save current scroll position and highlighted row
    // //console.log(window.scrollY);
    // //console.log(document.documentElement.scrollTop);
    // //console.log(document.body.scrollTop);
    // //console.log(window);
    // //console.log(window.pageXOffset);


    this.bcSqlStateService.setState(window.scrollY, guid);
    this.router.navigate(['/sync-hub/bc-sql', guid]);
  }

  deleteBCIntegration(guid: string) {
    const dialogRef = this.dialog.open(CustomConfirmDialogComponent, {
      width: '400px',
      height: '200px',
      data: {
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this item?',
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this._connectionIntegrationService.delete(guid).subscribe((res) => {
          if (res) {
            this.sqlListData = this.sqlListData.filter((item) => item.guid !== guid);
            this.filteredSqlListData = this.filteredSqlListData.filter((item) => item.guid !== guid);
            this.groupedSqlListData = this.groupBySourceDatabase(this.filteredSqlListData);

            this.groupedSqlListData = this.sortBySourceDatabase(this.groupedSqlListData);
          }
        });
      }
    });
  }
  showJobLogsDialog(item: any) {
    this.dialog.open(JoblogsViewDialogComponent, {
      width: '750px',
      height: 'auto',
      minHeight: '350px',
      data: {
        intergrationId: item.guid,
        settings: JSON.parse(item.settings)
      },
    });

  }

  groupBySourceDatabase(data: any[]): any[] {
    const groupedData = [];
    const groups = data.reduce((acc, item) => {
      const group = item.sourceDatabase || 'Unknown'; // Handle items with undefined sourceDatabase
      if (!acc[group]) {
        acc[group] = { sourceDatabase: group, items: [] };
        groupedData.push(acc[group]);
      }
      acc[group].items.push(item);
      return acc;
    }, {});

    return groupedData;
  }

  sortBySourceDatabase(data: any[]): any[] {
    return data.sort((a, b) => {
      return a.sourceDatabase.localeCompare(b.sourceDatabase);
    });
  }

  updateSearchEntityKey(event: Event) {
    localStorage.setItem('searchByEntityKey', this.searchByEntityKey);
    this.searchByEntityKey = event.target['value'];

    // this.groupedSqlListData = this.groupBySourceDatabase(this.filteredSqlListData)
    this.groupedSqlListData = this.groupBySourceDatabase(this.filteredSqlListData)
    // this.onCompanyTypeChange()
    this.OnSearchByEntityKeyChange(this.searchByEntityKey);
    //console.log(this.searchByEntityKey);

  }

  OnSearchByEntityKeyChange(key: string) {
    // if (this.selectedConnectionType === 'All') {
    //   this.filteredSqlListData = this.sqlListData;
    // }
    const searchValue = key.toLowerCase();
    if (!this.comFilteredData) {
      this.comFilteredData = this.groupedSqlListData

    }
    this.isFilterAdded = true
    if ((searchValue == "")) {
      this.isFilterAdded = false
    }

    const filteredData = this.comFilteredData
      .map((entry: any) => ({
        ...entry,
        items: entry.items.filter((item: any) => item.destinationTable.toLowerCase().includes(searchValue)),
      }))
      .filter((entry) => entry.items.length > 0);
    //console.log(filteredData);

    this.groupedSqlListData = this.sortBySourceDatabase(filteredData);
    //console.log(this.groupedSqlListData);
    this.loadExpandedGroups()
  }

  onConnTypeChange() {
    this.isFilterAdded = true
    if ((this.selectedConnectionType == "All")) {
      //console.log(this.selectedCompanyType);
      this.isFilterAdded = false
    }
    this.searchByEntityKey = '';
    this.companyType = ["All"]
    if (this.selectedConnectionType === 'NAV') {
      let dbConnections = this.dbConnections.filter((connection) => connection.type === 'BCODataWebService');
      this.filteredSqlListData = this.sqlListData.filter((data) =>
        dbConnections.find(
          (connection) => connection.guid === data.sourceConnectionGuid && this.isNav(connection.connectionCredJson)
        )
      );
    } else if (this.selectedConnectionType === 'BC Web Service') {
      let dbConnections = this.dbConnections.filter((connection) => connection.type === 'BCODataWebService');
      this.filteredSqlListData = this.sqlListData.filter((data) =>
        dbConnections.find(
          (connection) => connection.guid === data.sourceConnectionGuid && !this.isNav(connection.connectionCredJson)
        )
      );
    } else if (this.selectedConnectionType === 'BC Rest API') {
      let dbConnections = this.dbConnections.filter((connection) => connection.type === 'BCODataRestApiService');
      this.filteredSqlListData = this.sqlListData.filter((data) =>
        dbConnections.find((connection) => connection.guid === data.sourceConnectionGuid)
      );
    } else {
      this.filteredSqlListData = this.sqlListData;
    }
    this.groupedSqlListData = this.groupBySourceDatabase(this.filteredSqlListData);
    this.comFilteredData = this.sortBySourceDatabase(this.groupedSqlListData)

    // this.comFilteredData=this.groupedSqlListData
    this.companyType = ['All']
    this.groupedSqlListData.forEach((item) => {

      this.companyType.push(item.sourceDatabase)
    })
    // //console.log(this.groupedSqlListData)
    localStorage.setItem('filterKey', this.selectedConnectionType);
    this.loadExpandedGroups();
    localStorage.removeItem("selectedCompanyType")
  }
  onCompanyTypeChange() {
    // this.onConnTypeChange()
    //console.log(this.selectedCompanyType);
    this.isFilterAdded = true
    if ((this.selectedCompanyType == "All")) {
      //console.log(this.selectedCompanyType);
      this.isFilterAdded = false
    }
    let allData = this.groupBySourceDatabase(this.filteredSqlListData)

    let filteredData;
    //console.log(this.selectedCompanyType);

    if (this.selectedCompanyType == "All") {
      localStorage.setItem("selectedCompanyType", 'All')
      filteredData = allData
    } else {

      this.companyType.forEach((type) => {

        if (type == this.selectedCompanyType) {
          localStorage.setItem("selectedCompanyType", type)
          filteredData = allData.filter((item) => item.sourceDatabase == type)
        }
      })
    }
    this.comFilteredData = filteredData
    this.groupedSqlListData = this.sortBySourceDatabase(filteredData)
    this.loadExpandedGroups()
    this.OnSearchByEntityKeyChange(this.searchByEntityKey)

  }
  clearAllFilter() {
    this.isFilterAdded = false;
    this.selectedConnectionType = 'All'
    this.selectedCompanyType = 'All'
    this.searchByEntityKey = ''
    localStorage.setItem("filterKey", 'All')
    localStorage.setItem("searchByEntityKey", '')

    localStorage.setItem("selectedCompanyType", 'All')
    this.onCompanyTypeChange()
    this.onConnTypeChange()
    this.OnSearchByEntityKeyChange(this.searchByEntityKey)
    this.groupedSqlListData = this.groupBySourceDatabase(this.sqlListData);
    this.groupedSqlListData = this.sortBySourceDatabase(this.groupedSqlListData)
    this.loadExpandedGroups()
  }
  isNav(connectionJson: any): boolean {
    const connection = JSON.parse(connectionJson);
    return connection['scope'] === 'CustomBasicAuth';
  }

  logo(sourceConnection: string): string {
    const connection = this.dbConnections.find((conn) => conn.guid === sourceConnection);
    if (connection) {
      if (connection.type === 'BCODataRestApiService') {
        return 'business-central-logo.png';
      } else if (connection.type === 'BCODataWebService' && !this.isNav(connection.connectionCredJson)) {
        return 'business-central-logo.png';
      } else if (connection.type === 'BCODataWebService' && this.isNav(connection.connectionCredJson)) {
        return 'nav.png';
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  toggleGroupExpansion(group: any): void {
    group.isExpanded = !group.isExpanded;
    this.saveExpandedGroups();
  }

  saveExpandedGroups(): void {
    const expandedGroups = this.groupedSqlListData
      .filter((group) => group.isExpanded)
      .map((group) => group.sourceDatabase);
    //console.log(expandedGroups);

    localStorage.setItem("expandedBcSqlGroups", JSON.stringify(expandedGroups));
  }

  loadExpandedGroups(): void {
    // Retrieve expanded groups from localStorage
    const expandedGroups: string[] = JSON.parse(localStorage.getItem('expandedBcSqlGroups') || '[]');

    // Check if groupedSqlListData is populated
    if (!this.groupedSqlListData || this.groupedSqlListData.length === 0) {
      //console.error('Grouped SQL List data is empty or undefined.');
      return;
    }

    // Map expanded states to groups
    this.groupedSqlListData.forEach((group) => {
      if (group.sourceDatabase) {
        group.isExpanded = expandedGroups.includes(group.sourceDatabase);
      } else {
        //console.warn('Group has no sourceDatabase property:', group);
        group.isExpanded = false; // Default to collapsed
      }
    });

    //console.log('Loaded expanded groups:', expandedGroups);
  }

  // Add methods to execute/stop job
  async executeJob(guid: string) {
    try {
      await this._connectionIntegrationService.executeJob(guid).toPromise();
      this.executingJobs.add(guid);
      this.toastr.info('Job Started Executing');
    } catch (error) {
      //console.error('Error executing job:', error);
    }
  }

  async stopJob(guid: string) {
    try {
      this.toastr.info('Cancelling Job');
      await this._connectionIntegrationService.deleteJob(guid).toPromise();
    } catch (error) {
      //console.error('Error stopping job:', error);
    }
  }
}
