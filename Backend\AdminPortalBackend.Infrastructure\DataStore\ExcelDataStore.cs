﻿using AdminPortalBackend.Core.Contracts;

namespace AdminPortalBackend.Infrastructure.DataStore;

public class ExcelDataStore : IDataStore
{
    private Dictionary<string, object> storeConfig = [];

    public object GetConfig(string key)
    {
        throw new NotImplementedException();
    }

    public Task<List<Dictionary<string, object>>> RetrieveDataAsync(string tableName)
    {
        throw new NotImplementedException();
    }

    public Task<List<Dictionary<string, object>>> SaveDataAsync(List<Dictionary<string, object>> data, string tableName, string sourceTable, string primaryKeys = "")
    {
        throw new NotImplementedException();
    }

    public void SetConfig(string key, object value)
    {
        storeConfig[key] = value;
    }

    public Task<Dictionary<string, object>> UpdateDataAsync(Dictionary<string, object> data, string tableName, Dictionary<string, object> primaryKeyValues)
    {
        throw new NotImplementedException();
    }
}
