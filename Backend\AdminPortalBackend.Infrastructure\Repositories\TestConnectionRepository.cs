﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Repositiories;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Infrastructure.Repositories
{
    public class TestConnectionRepository: ITestConnectionRepository
    {

        public async Task<ResponseMessage> CheckSqlConnection(string username, string password, string serverName)
        {
            var connectionString = $"Server={serverName};User Id={username};Password={password};";
            var responseMessage = new ResponseMessage();
            using (var connection = new SqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    responseMessage.IsError = false;
                    responseMessage.Message = "Test Connection Passed";
                }
                catch
                {
                    responseMessage.IsError = true;
                    responseMessage.Message = "Test Connection Failed";
                }
                return responseMessage;
            }
        }

        public async Task<ResponseMessage> CheckBcConnection(string clientSecret, string clientId, string tokenEndpoint, string scope)
        {
            var responseMessage = new ResponseMessage();
            if (scope == "CustomBasicAuth")
            {
                responseMessage.IsError = false;
                responseMessage.Message = "Test Connection Passed";
                return responseMessage;
            }
            IConfidentialClientApplication app = ConfidentialClientApplicationBuilder.Create(clientId)
                .WithClientSecret(clientSecret)
                .WithAuthority(tokenEndpoint)
                .Build();

            string[] scopes = new string[] { scope };
            try
            {
                await app.AcquireTokenForClient(scopes).ExecuteAsync();
                responseMessage.IsError = false;
                responseMessage.Message = "Test Connection Passed";

            }
            catch (Exception ex)
            {
                responseMessage.IsError = true;
                responseMessage.Message = "Test Connection Failed";                
            }
            return responseMessage;
        }

    }
}
