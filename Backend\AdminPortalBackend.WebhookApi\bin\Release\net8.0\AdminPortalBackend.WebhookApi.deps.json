{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AdminPortalBackend.WebhookApi/1.0.0": {"dependencies": {"AdminPortalBackend.Core": "1.0.0", "AdminPortalBackend.Infrastructure": "1.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.8", "SendGrid": "9.29.3", "Serilog.AspNetCore": "8.0.0", "Serilog.Sinks.MSSqlServer": "6.5.0", "Stripe.net": "45.14.0", "Swashbuckle.AspNetCore": "6.4.0"}, "runtime": {"AdminPortalBackend.WebhookApi.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AWSSDK.Core/3.7.400.43": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.400.43"}}}, "AWSSDK.S3/3.7.405.7": {"dependencies": {"AWSSDK.Core": "3.7.400.43"}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.405.7"}}}, "AWSSDK.SecurityToken/**********": {"dependencies": {"AWSSDK.Core": "3.7.400.43"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "Azure.AI.ContentSafety/1.0.0": {"dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Azure.AI.ContentSafety.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.23.61102"}}}, "Azure.AI.FormRecognizer/4.1.0": {"dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Azure.AI.FormRecognizer.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.100.23.41002"}}}, "Azure.AI.OpenAI/2.1.0-beta.2": {"dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.1.0-beta.2"}, "runtime": {"lib/netstandard2.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.100.24.55401"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.2.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Memory.Data": "8.0.1", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.13.1": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.66.1", "Microsoft.Identity.Client.Extensions.Msal": "4.66.1", "System.Memory": "4.5.5", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.13.1.0", "fileVersion": "1.1300.124.52405"}}}, "Azure.Search.Documents/11.6.0": {"dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "8.0.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Search.Documents.dll": {"assemblyVersion": "11.6.0.0", "fileVersion": "11.600.24.36703"}}}, "Azure.Storage.Blobs/12.22.2": {"dependencies": {"Azure.Storage.Common": "12.21.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.22.2.0", "fileVersion": "12.2200.224.51019"}}}, "Azure.Storage.Common/12.21.1": {"dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net6.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.21.1.0", "fileVersion": "12.2100.124.51010"}}}, "Azure.Storage.Queues/12.20.1": {"dependencies": {"Azure.Storage.Common": "12.21.1", "System.Memory.Data": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Azure.Storage.Queues.dll": {"assemblyVersion": "12.20.1.0", "fileVersion": "12.2000.124.51019"}}}, "ClosedXML/0.104.1": {"dependencies": {"ClosedXML.Parser": "1.2.0", "DocumentFormat.OpenXml": "3.1.1", "ExcelNumberFormat": "1.1.0", "RBush": "3.2.0", "SixLabors.Fonts": "1.0.0", "System.IO.Packaging": "8.0.1"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.104.1.0", "fileVersion": "0.104.1.0"}}}, "ClosedXML.Parser/1.2.0": {"runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Dapper/2.1.35": {"runtime": {"lib/net7.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.35.13827"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "DocumentFormat.OpenXml/3.1.1": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.Clients.Elasticsearch/8.12.1": {"dependencies": {"Elastic.Transport": "0.4.18"}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "8.12.1.0"}}}, "Elastic.Transport/0.4.18": {"runtime": {"lib/net8.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.4.18.0"}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation/11.5.1": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.5.1.0"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.5.1", "FluentValidation.DependencyInjectionExtensions": "11.5.1"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.3.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"dependencies": {"FluentValidation": "11.5.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.5.1.0"}}}, "Google.Protobuf/3.19.6": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.19.6.0", "fileVersion": "3.19.6.0"}}}, "Hangfire/1.8.14": {"dependencies": {"Hangfire.AspNetCore": "1.8.14", "Hangfire.Core": "1.8.14", "Hangfire.SqlServer": "1.8.14"}}, "Hangfire.AspNetCore/1.8.14": {"dependencies": {"Hangfire.NetCore": "1.8.14"}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.Core/1.8.14": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.MaximumConcurrentExecutions/1.1.0": {"dependencies": {"NETStandard.Library": "1.6.1", "Hangfire.Core": "1.8.14"}, "runtime": {"lib/netstandard1.3/Hangfire.MaximumConcurrentExecutions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Hangfire.NetCore/1.8.14": {"dependencies": {"Hangfire.Core": "1.8.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.SqlServer/1.8.14": {"dependencies": {"Hangfire.Core": "1.8.14"}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "HtmlAgilityPack/1.11.70": {"runtime": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "LLamaSharp/0.18.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "System.Numerics.Tensors": "8.0.0"}, "runtime": {"lib/net8.0/LLamaSharp.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "MediatR/12.4.1": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "********", "fileVersion": "12.4.1.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.8": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.8.0", "fileVersion": "8.0.824.36908"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.13.1", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.66.1", "Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Runtime.Caching": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Extensions.AI.Abstractions/9.0.1-preview.1.24570.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.57005"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.1", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Logging.Console": "8.0.1", "Microsoft.Extensions.Logging.Debug": "8.0.1", "Microsoft.Extensions.Logging.EventLog": "8.0.1", "Microsoft.Extensions.Logging.EventSource": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Http/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Configuration/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Console/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Debug/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.EventLog/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.EventLog": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.EventSource/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.ObjectPool/6.0.3": {}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24523.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.66.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.66.1.0", "fileVersion": "4.66.1.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.66.1": {"dependencies": {"Microsoft.Identity.Client": "4.66.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.66.1.0", "fileVersion": "4.66.1.0"}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.KernelMemory/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.AI.Anthropic": "0.92.241112.1", "Microsoft.KernelMemory.AI.AzureOpenAI": "0.92.241112.1", "Microsoft.KernelMemory.AI.LlamaSharp": "0.92.241112.1", "Microsoft.KernelMemory.AI.Ollama": "0.92.241112.1", "Microsoft.KernelMemory.AI.Onnx": "0.92.241112.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Microsoft.KernelMemory.Core": "0.92.241112.1", "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel": "0.92.241112.1", "Microsoft.KernelMemory.DocumentStorage.AWSS3": "0.92.241112.1", "Microsoft.KernelMemory.DocumentStorage.AzureBlobs": "0.92.241112.1", "Microsoft.KernelMemory.MemoryDb.AzureAISearch": "0.92.241112.1", "Microsoft.KernelMemory.MemoryDb.Elasticsearch": "0.92.241112.1", "Microsoft.KernelMemory.MemoryDb.Postgres": "0.92.241112.1", "Microsoft.KernelMemory.MemoryDb.Qdrant": "0.92.241112.1", "Microsoft.KernelMemory.MemoryDb.Redis": "0.92.241112.1", "Microsoft.KernelMemory.MemoryDb.SQLServer": "0.92.241112.1", "Microsoft.KernelMemory.MongoDbAtlas": "0.92.241112.1", "Microsoft.KernelMemory.Orchestration.AzureQueues": "0.92.241112.1", "Microsoft.KernelMemory.Orchestration.RabbitMQ": "0.92.241112.1", "Microsoft.KernelMemory.Safety.AzureAIContentSafety": "0.92.241112.1", "Microsoft.KernelMemory.SemanticKernelPlugin": "0.92.241112.1", "Microsoft.KernelMemory.WebClient": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.All.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Abstractions/0.92.241112.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.SemanticKernel.Abstractions": "1.32.0", "System.Memory.Data": "8.0.1", "System.Numerics.Tensors": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Anthropic/0.92.241112.1": {"dependencies": {"Microsoft.Extensions.Http": "8.0.1", "Microsoft.KernelMemory.AI.OpenAI": "0.92.241112.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Anthropic.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.AzureOpenAI/0.92.241112.1": {"dependencies": {"Azure.Identity": "1.13.1", "Microsoft.KernelMemory.AI.OpenAI": "0.92.241112.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.32.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.AzureOpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.LlamaSharp/0.92.241112.1": {"dependencies": {"LLamaSharp": "0.18.0", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Microsoft.KernelMemory.Core": "0.92.241112.1", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.LlamaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Ollama/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.AI.OpenAI": "0.92.241112.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "OllamaSharp": "4.0.3"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Ollama.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Onnx/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.AI.OpenAI": "0.92.241112.1", "Microsoft.ML.OnnxRuntimeGenAI": "0.4.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Onnx.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.OpenAI/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Microsoft.ML.Tokenizers": "0.22.0-preview.24378.1", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.32.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.OpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Core/0.92.241112.1": {"dependencies": {"ClosedXML": "0.104.1", "DocumentFormat.OpenXml": "3.1.1", "HtmlAgilityPack": "1.11.70", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Microsoft.ML.Tokenizers": "0.22.0-preview.24378.1", "PdfPig": "0.1.9", "Polly.Core": "8.4.2", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel/0.92.241112.1": {"dependencies": {"Azure.AI.FormRecognizer": "4.1.0", "Azure.Identity": "1.13.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.DocumentStorage.AWSS3/0.92.241112.1": {"dependencies": {"AWSSDK.S3": "3.7.405.7", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AWSS3.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.DocumentStorage.AzureBlobs/0.92.241112.1": {"dependencies": {"Azure.Identity": "1.13.1", "Azure.Storage.Blobs": "12.22.2", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.AzureAISearch/0.92.241112.1": {"dependencies": {"Azure.Identity": "1.13.1", "Azure.Search.Documents": "11.6.0", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Elasticsearch/0.92.241112.1": {"dependencies": {"Elastic.Clients.Elasticsearch": "8.12.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Postgres/0.92.241112.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Pgvector": "0.3.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Postgres.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Qdrant/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Qdrant.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Redis/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "NRedisStack": "0.13.0", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Redis.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.SQLServer/0.92.241112.1": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "System.Runtime.Caching": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.SQLServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MongoDbAtlas/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "MongoDB.Driver.GridFS": "2.30.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MongoDbAtlas.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Orchestration.AzureQueues/0.92.241112.1": {"dependencies": {"Azure.Identity": "1.13.1", "Azure.Storage.Queues": "12.20.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.AzureQueues.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Orchestration.RabbitMQ/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "RabbitMQ.Client": "6.8.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.RabbitMQ.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Safety.AzureAIContentSafety/0.92.241112.1": {"dependencies": {"Azure.AI.ContentSafety": "1.0.0", "Azure.Identity": "1.13.1", "Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.SemanticKernelPlugin/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1", "Microsoft.KernelMemory.WebClient": "0.92.241112.1", "Microsoft.SemanticKernel.Abstractions": "1.32.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.SemanticKernelPlugin.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.WebClient/0.92.241112.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.92.241112.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.WebClient.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.ML.OnnxRuntime/1.19.1": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.19.1"}, "runtimeTargets": {"runtimes/android/native/onnxruntime.aar": {"rid": "android", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios/native/onnxruntime.xcframework.zip": {"rid": "ios", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libonnxruntime.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime_providers_shared.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libonnxruntime.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libonnxruntime.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.19.24.820"}, "runtimes/win-arm64/native/onnxruntime.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.19.24.820"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.19.24.820"}, "runtimes/win-x64/native/onnxruntime.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.19.24.820"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onnxruntime.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.19.24.820"}, "runtimes/win-x86/native/onnxruntime.lib": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onnxruntime_providers_shared.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.19.24.820"}, "runtimes/win-x86/native/onnxruntime_providers_shared.lib": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.19.1": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/net6.0/Microsoft.ML.OnnxRuntime.dll": {"assemblyVersion": "1.19.1.0", "fileVersion": "1.19.1.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI/0.4.0": {"dependencies": {"Microsoft.ML.OnnxRuntime": "1.19.1", "Microsoft.ML.OnnxRuntimeGenAI.Managed": "0.4.0"}, "runtimeTargets": {"runtimes/linux-x64/native/libonnxruntime-genai.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime-genai.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.4.0.0"}, "runtimes/win-arm64/native/onnxruntime-genai.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime-genai.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.4.0.0"}, "runtimes/win-x64/native/onnxruntime-genai.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.4.0": {"runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.Tokenizers/0.22.0-preview.24378.1": {"dependencies": {"Google.Protobuf": "3.19.6", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.ML.Tokenizers.dll": {"assemblyVersion": "*******", "fileVersion": "0.2200.24.37801"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OData.Client/8.1.0": {"dependencies": {"Microsoft.Extensions.Http": "8.0.1", "Microsoft.OData.Core": "8.1.0"}, "runtime": {"lib/net8.0/Microsoft.OData.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.OData.Core/8.1.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "6.0.3", "Microsoft.OData.Edm": "8.1.0", "Microsoft.Spatial": "8.1.0"}, "runtime": {"lib/net8.0/Microsoft.OData.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.OData.Edm/8.1.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.OData.Edm.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Microsoft.SemanticKernel/1.32.0": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.32.0", "Microsoft.SemanticKernel.Core": "1.32.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "1.32.0.0", "fileVersion": "1.32.0.0"}}}, "Microsoft.SemanticKernel.Abstractions/1.32.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI.Abstractions": "9.0.1-preview.1.24570.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.VectorData.Abstractions": "9.0.0-preview.1.24523.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.32.0.0", "fileVersion": "1.32.0.0"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.32.0": {"dependencies": {"Azure.AI.OpenAI": "2.1.0-beta.2", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.32.0", "Microsoft.SemanticKernel.Core": "1.32.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "1.32.0.0", "fileVersion": "1.32.0.0"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.32.0": {"dependencies": {"Microsoft.SemanticKernel.Core": "1.32.0", "OpenAI": "2.1.0-beta.2"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "1.32.0.0", "fileVersion": "1.32.0.0"}}}, "Microsoft.SemanticKernel.Core/1.32.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.SemanticKernel.Abstractions": "1.32.0", "System.Numerics.Tensors": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "1.32.0.0", "fileVersion": "1.32.0.0"}}}, "Microsoft.SemanticKernel.Plugins.Core/1.29.0-alpha": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.SemanticKernel.Core": "1.32.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Plugins.Core.dll": {"assemblyVersion": "1.29.0.0", "fileVersion": "1.29.0.0"}}}, "Microsoft.Spatial/8.1.0": {"runtime": {"lib/net8.0/Microsoft.Spatial.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "MongoDB.Bson/2.30.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.30.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "MongoDB.Bson": "2.30.0", "MongoDB.Driver.Core": "2.30.0", "MongoDB.Libmongocrypt": "1.12.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.30.0": {"dependencies": {"AWSSDK.SecurityToken": "**********", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "MongoDB.Bson": "2.30.0", "MongoDB.Libmongocrypt": "1.12.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.GridFS/2.30.0": {"dependencies": {"MongoDB.Bson": "2.30.0", "MongoDB.Driver": "2.30.0", "MongoDB.Driver.Core": "2.30.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.GridFS.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.12.0": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.12.0.0", "fileVersion": "1.12.0.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "NetTopologySuite/2.5.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Npgsql/8.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "8.0.3.0", "fileVersion": "8.0.3.0"}}}, "NRedisStack/0.13.0": {"dependencies": {"NetTopologySuite": "2.5.0", "StackExchange.Redis": "2.8.16"}, "runtime": {"lib/net8.0/NRedisStack.dll": {"assemblyVersion": "0.13.0.0", "fileVersion": "0.13.0.0"}}}, "OllamaSharp/4.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.AI.Abstractions": "9.0.1-preview.1.24570.5"}, "runtime": {"lib/netstandard2.0/OllamaSharp.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "OpenAI/2.1.0-beta.2": {"dependencies": {"System.ClientModel": "1.2.1", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/OpenAI.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "PdfPig/0.1.9": {"runtime": {"lib/net8.0/UglyToad.PdfPig.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/UglyToad.PdfPig.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/UglyToad.PdfPig.Package.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/UglyToad.PdfPig.Tokenization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/UglyToad.PdfPig.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/UglyToad.PdfPig.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pgvector/0.3.0": {"dependencies": {"Npgsql": "8.0.3"}, "runtime": {"lib/net6.0/Pgvector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "RabbitMQ.Client/6.8.1": {"dependencies": {"System.Memory": "4.5.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RBush/3.2.0": {"runtime": {"lib/net6.0/RBush.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SendGrid/9.29.3": {"dependencies": {"Newtonsoft.Json": "13.0.3", "starkbank-ecdsa": "1.3.3"}, "runtime": {"lib/netstandard2.0/SendGrid.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Serilog/3.1.1": {"runtime": {"lib/net7.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Serilog": "3.1.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.0", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Serilog": "3.1.1", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyModel": "8.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.MSSqlServer/6.5.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Serilog": "3.1.1", "Serilog.Sinks.PeriodicBatching": "3.1.0", "System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net6.0/Serilog.Sinks.MSSqlServer.dll": {"assemblyVersion": "6.5.0.0", "fileVersion": "6.5.0.0"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.8.16": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.16.12844"}}}, "starkbank-ecdsa/1.3.3": {"runtime": {"lib/netstandard2.1/StarkbankEcdsa.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Stripe.net/45.14.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net6.0/Stripe.net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Swashbuckle.AspNetCore/6.4.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.2.1": {"dependencies": {"System.Memory.Data": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.200.124.50905"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/8.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.IO.Pipelines/5.0.1": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.35981"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/8.0.1": {"runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Tensors/8.0.0": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.5": {}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "0.7.3.0", "fileVersion": "0.7.3.0"}}}, "AdminPortalBackend.Core/1.0.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "FluentValidation.AspNetCore": "11.3.0", "MediatR": "12.4.1", "Newtonsoft.Json": "13.0.3"}, "runtime": {"AdminPortalBackend.Core.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "AdminPortalBackend.Infrastructure/1.0.0": {"dependencies": {"AdminPortalBackend.Core": "1.0.0", "Dapper": "2.1.35", "Hangfire": "1.8.14", "Hangfire.MaximumConcurrentExecutions": "1.1.0", "Hangfire.SqlServer": "1.8.14", "Microsoft.Identity.Client": "4.66.1", "Microsoft.KernelMemory": "0.92.241112.1", "Microsoft.KernelMemory.Core": "0.92.241112.1", "Microsoft.OData.Client": "8.1.0", "Microsoft.OData.Core": "8.1.0", "Microsoft.OData.Edm": "8.1.0", "Microsoft.SemanticKernel": "1.32.0", "Microsoft.SemanticKernel.Plugins.Core": "1.29.0-alpha", "Microsoft.Spatial": "8.1.0", "Newtonsoft.Json": "13.0.3", "System.ComponentModel.Annotations": "5.0.0", "System.Data.SqlClient": "4.8.6", "System.Text.Json": "8.0.5"}, "runtime": {"AdminPortalBackend.Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"AdminPortalBackend.WebhookApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "AWSSDK.Core/3.7.400.43": {"type": "package", "serviceable": true, "sha512": "sha512-1e9Tq2LBfGmD3C9bbkErz65jpatDu8oz1QQPU7MqxQWm6h12z6bOKL5udhvuqXknqtLBAh0+V1JTkAX0vOQZsw==", "path": "awssdk.core/3.7.400.43", "hashPath": "awssdk.core.3.7.400.43.nupkg.sha512"}, "AWSSDK.S3/3.7.405.7": {"type": "package", "serviceable": true, "sha512": "sha512-RMQw6VIjw1agwtpZyV7slOt/5J/P38Y+0x7OeP/mqSPttjOJZFL1an27TaJ56rH9UCpdIRwpPpHfvHfabHifkw==", "path": "awssdk.s3/3.7.405.7", "hashPath": "awssdk.s3.3.7.405.7.nupkg.sha512"}, "AWSSDK.SecurityToken/**********": {"type": "package", "serviceable": true, "sha512": "sha512-dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "path": "awssdk.securitytoken/**********", "hashPath": "awssdk.securitytoken.**********.nupkg.sha512"}, "Azure.AI.ContentSafety/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-62UgYaWYXlieHXnvF6EZjPahgYcuRe0jhsDZu8pMGhvUwkEsgmL4d2ag3Wlk+nYdLbKCE3TIPJmAPI/U/BVYJQ==", "path": "azure.ai.contentsafety/1.0.0", "hashPath": "azure.ai.contentsafety.1.0.0.nupkg.sha512"}, "Azure.AI.FormRecognizer/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R9mEeYFa2+EcCu5dOGOFG07nbELHY/2o6JtgJoxNTo2wtsonLnLwcKzX/sxOnYhpib4TJEBTUX0/ea0lL130Iw==", "path": "azure.ai.formrecognizer/4.1.0", "hashPath": "azure.ai.formrecognizer.4.1.0.nupkg.sha512"}, "Azure.AI.OpenAI/2.1.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-9dm9MgejTpvWZHkLC9rBP01SII2bRLR55CwkKHS55Hai1pKxST7QvZ46MjIcTcmApfaV6XbHuF8mxSorqUU05w==", "path": "azure.ai.openai/2.1.0-beta.2", "hashPath": "azure.ai.openai.2.1.0-beta.2.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-4eeK9XztjTmvA4WN+qAvlUCSxSv45+LqTMeC8XT2giGGZHKthTMU2IuXcHjAOf5VLH3wE3Bo6EwhIcJxVB8RmQ==", "path": "azure.identity/1.13.1", "hashPath": "azure.identity.1.13.1.nupkg.sha512"}, "Azure.Search.Documents/11.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-M7WLx3ANLPHymfqb4Nwk4EwcWWRiHqdvnxJ7RH857baAbkEZ3FYVCRJmHgxH+ROpYOTVSx30uJzsa573/cdD8A==", "path": "azure.search.documents/11.6.0", "hashPath": "azure.search.documents.11.6.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.22.2": {"type": "package", "serviceable": true, "sha512": "sha512-/BK63qx31dhXjnqOXB90DP8mJM+pHbti45+v/D3SiEgP2A+ekvJlGWGLVGQznriT5UAOerM+3vAAEJNKolVSIQ==", "path": "azure.storage.blobs/12.22.2", "hashPath": "azure.storage.blobs.12.22.2.nupkg.sha512"}, "Azure.Storage.Common/12.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-NgDJw/upcro33AgGf91sPIG+BU2pFTgGDBzWEp8HctGwzmbjG80eYTl4CJMIwgxVOQWnEXnQZXLY7w3k+BQ9ig==", "path": "azure.storage.common/12.21.1", "hashPath": "azure.storage.common.12.21.1.nupkg.sha512"}, "Azure.Storage.Queues/12.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>ylPb8kc1DkLvDhNjdKxSbC23hPzu74a7Wiil7UXIBjnIZOkLI+dfQSTTXiejQaHBNpgZC+xWjNyZa3a71AoBQ==", "path": "azure.storage.queues/12.20.1", "hashPath": "azure.storage.queues.12.20.1.nupkg.sha512"}, "ClosedXML/0.104.1": {"type": "package", "serviceable": true, "sha512": "sha512-RVm2fUNWJlBJlg07shrfeWzrHPG5ypI/vARqdUOUbUdaog8yBw8l4IbCHf2MXt0AXtzaZqGNqhFaCAHigCBdfw==", "path": "closedxml/0.104.1", "hashPath": "closedxml.0.104.1.nupkg.sha512"}, "ClosedXML.Parser/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "path": "closedxml.parser/1.2.0", "hashPath": "closedxml.parser.1.2.0.nupkg.sha512"}, "Dapper/2.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "path": "dapper/2.1.35", "hashPath": "dapper.2.1.35.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "path": "documentformat.openxml/3.1.1", "hashPath": "documentformat.openxml.3.1.1.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "path": "documentformat.openxml.framework/3.1.1", "hashPath": "documentformat.openxml.framework.3.1.1.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-DBVRNLq9JPgRn5YtdNy1v2ghV8Zh5GJ0ms1U+jlfKyL8b9a/kp8PpeWoxDFmeFz2lL4OJNadxR8lHSQmskHsfw==", "path": "elastic.clients.elasticsearch/8.12.1", "hashPath": "elastic.clients.elasticsearch.8.12.1.nupkg.sha512"}, "Elastic.Transport/0.4.18": {"type": "package", "serviceable": true, "sha512": "sha512-Q9nGgYxB0r1jTkUf8zWbFKLk5JHliZhVU6vukdMLHLH/1EBKQvHzPahkrnV8KsvGOPFFqFAVOhQvaxpjXuS3TA==", "path": "elastic.transport/0.4.18", "hashPath": "elastic.transport.0.4.18.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "FluentValidation/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-0h1Q5lNOLLyYTWMJmyNoMqhY4CBRvvUWvJP1R4F2CnmmzuWwvB0A8aVmw5+lOuwYnwUwCRrdeMLbc81F38ahNQ==", "path": "fluentvalidation/11.5.1", "hashPath": "fluentvalidation.11.5.1.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-iWM0LS1MDYX06pcjMEQKqHirl2zkjHlNV23mEJSoR1IZI7KQmTa0RcTtGEJpj5+iHvBCfrzP2mYKM4FtRKVb+A==", "path": "fluentvalidation.dependencyinjectionextensions/11.5.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512"}, "Google.Protobuf/3.19.6": {"type": "package", "serviceable": true, "sha512": "sha512-I+gb51P2GrzyXkUD9y60pMzA6ox4d3ByvXCm5OYZsAii2/fs5S5cuKiEIoaseEv26cdIAnKL4W7W5W0QO0Iobw==", "path": "google.protobuf/3.19.6", "hashPath": "google.protobuf.3.19.6.nupkg.sha512"}, "Hangfire/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-8ve7Di0xvy0ZxCxibcDEzjVVx/H4mJiYSrc5A7Oj2Q62y5vB+Fq6k03zkFj96Xlgs5ivVSeEGfreF/NECJ7tlQ==", "path": "hangfire/1.8.14", "hashPath": "hangfire.1.8.14.nupkg.sha512"}, "Hangfire.AspNetCore/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-aTPzKN/g9SW30QB1SLTOMuckKqTVL1YAhtEpWll4LPbPTgyeBHJTChdRhrN127fj+mqJ6P4P7+HJBhNYUFTfNw==", "path": "hangfire.aspnetcore/1.8.14", "hashPath": "hangfire.aspnetcore.1.8.14.nupkg.sha512"}, "Hangfire.Core/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-tj/+J8/UdaxydFX6VQr5IEyBtVbAOvkQ8X8tIQKwY9zlpmK83hP4iHEQQQ26zzGUpcE1HlPc6PBUv0NgUDXS3A==", "path": "hangfire.core/1.8.14", "hashPath": "hangfire.core.1.8.14.nupkg.sha512"}, "Hangfire.MaximumConcurrentExecutions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-zfVc2r2JfvZH+RjU6nwQl9wB+Wt+c54pQUU0yg8dZouDGFtLr8qjz50GGA9JpdX3yykXG39myHdHJWeFy0XgCA==", "path": "hangfire.maximumconcurrentexecutions/1.1.0", "hashPath": "hangfire.maximumconcurrentexecutions.1.1.0.nupkg.sha512"}, "Hangfire.NetCore/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-fBLdsxWYFdrQuenvVHEj/z8nOXoOTqpWIl4qYoinBAUCVmp4qlxfFsY3Aq3VVbwket0wBH472aG2LAmYm6hjxw==", "path": "hangfire.netcore/1.8.14", "hashPath": "hangfire.netcore.1.8.14.nupkg.sha512"}, "Hangfire.SqlServer/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-OrsxbJD0UYanIk4E1oqwffZSWRfltYXJa89Icn+fcWmOLGyWTiAg9j5UX4MoS2RaS3WyZG8xbZzbhoRqnujo8g==", "path": "hangfire.sqlserver/1.8.14", "hashPath": "hangfire.sqlserver.1.8.14.nupkg.sha512"}, "HtmlAgilityPack/1.11.70": {"type": "package", "serviceable": true, "sha512": "sha512-lwCgdq4H+WXH+lkM7TvJhyEs5uNGzoTud1VmXz2jr30/yOTRtG/oMCour5JMTN0t0fU4uFwFYIrDd98FH2RKsQ==", "path": "htmlagilitypack/1.11.70", "hashPath": "htmlagilitypack.1.11.70.nupkg.sha512"}, "LLamaSharp/0.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-gcelZvdJZrFSyo6BPKuKJar0AOJzqPo9I1H6w53HLlWsj1vKF+SmM9qmBTEOE3EUw+P90ThVjdcTidqgK/tdpA==", "path": "llamasharp/0.18.0", "hashPath": "llamasharp.0.18.0.nupkg.sha512"}, "MediatR/12.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-0tLxCgEC5+r1OCuumR3sWyiVa+BMv3AgiU4+pz8xqTc+2q1WbUEXFOr7Orm96oZ9r9FsldgUtWvB2o7b9jDOaw==", "path": "mediatr/12.4.1", "hashPath": "mediatr.12.4.1.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-J145j2LgD4kkkNkrf5DW/pKzithZRKN5EFY+KAO3SqweMyDfv4cgKgtOIsv2bhrOLGqPJixuZkZte7LfK1seYQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.8", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.8.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.0.1-preview.1.24570.5": {"type": "package", "serviceable": true, "sha512": "sha512-zqUK1epXT+mxmJW7bonO2gRuYEhysLGIGCbPVXW5qmgYA+8PgFle9IPeKpb9RevwxnEedXdHm+nQyQiOnZIHWg==", "path": "microsoft.extensions.ai.abstractions/9.0.1-preview.1.24570.5", "hashPath": "microsoft.extensions.ai.abstractions.9.0.1-preview.1.24570.5.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "path": "microsoft.extensions.configuration.binder/8.0.2", "hashPath": "microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "path": "microsoft.extensions.configuration.json/8.0.1", "hashPath": "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7tYqdPPpAK+3jO9d5LTuCK2VxrEdf85Ol4trUr6ds4jclBecadWZ/RyPCbNjfbN5iGTfUnD/h65TOQuqQv2c+A==", "path": "microsoft.extensions.configuration.usersecrets/8.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-doVPCUUCY7c6LhBsEfiy3W1bvS7Mi6LkfQMS8nlC22jZWNxBv8VO8bdfeyvpYFst6Kxqk7HBC6lytmEoBssvSQ==", "path": "microsoft.extensions.diagnostics/8.0.1", "hashPath": "microsoft.extensions.diagnostics.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bP9EEkHBEfjgYiG8nUaXqMk/ujwJrffOkNPP7onpRMO8R+OUSESSP4xHkCAXgYZ1COP2Q9lXlU5gkMFh20gRuw==", "path": "microsoft.extensions.hosting/8.0.1", "hashPath": "microsoft.extensions.hosting.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDYeKJUzh0qeg/AI+nSr3ffthmXYQTEb0nS9qRC7YhSbbuN4M4NPbaB77AJwtkTnCV9XZ7qYj3dkZaNcyl73EA==", "path": "microsoft.extensions.http/8.0.1", "hashPath": "microsoft.extensions.http.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QWwTrsgOnJMmn+XUslm8D2H1n3PkP/u/v52FODtyBc/k4W9r3i2vcXXeeX/upnzllJYRRbrzVzT0OclfNJtBJA==", "path": "microsoft.extensions.logging.configuration/8.0.1", "hashPath": "microsoft.extensions.logging.configuration.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uzcg/5U2eLyn5LIKlERkdSxw6VPC1yydnOSQiRRWGBGN3kphq3iL4emORzrojScDmxRhv49gp5BI8U3Dz7y4iA==", "path": "microsoft.extensions.logging.console/8.0.1", "hashPath": "microsoft.extensions.logging.console.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B8hqNuYudC2RB+L/DI33uO4rf5by41fZVdcVL2oZj0UyoAZqnwTwYHp1KafoH4nkl1/23piNeybFFASaV2HkFg==", "path": "microsoft.extensions.logging.debug/8.0.1", "hashPath": "microsoft.extensions.logging.debug.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZD1m4GXoxcZeDJIq8qePKj+QAWeQNO/OG8skvrOG8RQfxLp9MAKRoliTc27xanoNUzeqvX5HhS/I7c0BvwAYUg==", "path": "microsoft.extensions.logging.eventlog/8.0.1", "hashPath": "microsoft.extensions.logging.eventlog.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-YMXMAla6B6sEf/SnfZYTty633Ool3AH7KOw2LOaaEqwSo2piK4f7HMtzyc3CNiipDnq1fsUSuG5Oc7ZzpVy8WQ==", "path": "microsoft.extensions.logging.eventsource/8.0.1", "hashPath": "microsoft.extensions.logging.eventsource.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-IbQUEZr/LxxpPxkXDmKaemMeQNPjdHfk87HtTsI18a3RVgad0NOJSRaJ20hcesqL45PLcpQHR8xrPP7wZKbFQQ==", "path": "microsoft.extensions.objectpool/6.0.3", "hashPath": "microsoft.extensions.objectpool.6.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24523.1": {"type": "package", "serviceable": true, "sha512": "sha512-5TUbyFZN8v2eWQXwF7WyAAHCW+OMdsmEVEWw1N1J7L5xA25UD0BtUXxO1kc8Nzkc6c1YGN/NwarySgiY84ALlg==", "path": "microsoft.extensions.vectordata.abstractions/9.0.0-preview.1.24523.1", "hashPath": "microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.24523.1.nupkg.sha512"}, "Microsoft.Identity.Client/4.66.1": {"type": "package", "serviceable": true, "sha512": "sha512-mE+m3pZ7zSKocSubKXxwZcUrCzLflC86IdLxrVjS8tialy0b1L+aECBqRBC/ykcPlB4y7skg49TaTiA+O2UfDw==", "path": "microsoft.identity.client/4.66.1", "hashPath": "microsoft.identity.client.4.66.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.66.1": {"type": "package", "serviceable": true, "sha512": "sha512-osgt1J9Rve3LO7wXqpWoFx9UFjl0oeqoUMK/xEru7dvafQ28RgV1A17CoCGCCRSUbgDQ4Arg5FgGK2lQ3lXR4A==", "path": "microsoft.identity.client.extensions.msal/4.66.1", "hashPath": "microsoft.identity.client.extensions.msal.4.66.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.KernelMemory/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-+B6cDJCRCIvcrTXCf/S+i9rwA/anAVIa5D60I5J55Faln7aTrPL7TWAyzBwbdh0Jhuver7o16a+7m0Asirau8Q==", "path": "microsoft.kernelmemory/0.92.241112.1", "hashPath": "microsoft.kernelmemory.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.Abstractions/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-kQfdKqKyVjsMUmwQnBXIolEEN5pJmu//j1UqlB1jl1BvygJHTNYBklM5LzzvgVEV8ue63EPHDkPEbIPyByYPhQ==", "path": "microsoft.kernelmemory.abstractions/0.92.241112.1", "hashPath": "microsoft.kernelmemory.abstractions.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Anthropic/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-RlnmGeNTzHeiiJGEvcAv6s5ZJYZtuHwd6mn/tkMUKxu4zCM4H8jQm6xfx/SvzFnPP83sXcsOSwwspDryWVNs2Q==", "path": "microsoft.kernelmemory.ai.anthropic/0.92.241112.1", "hashPath": "microsoft.kernelmemory.ai.anthropic.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.AzureOpenAI/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-nrsSDSOgz79N8wxEyF0w3W6Ad5Gt1HzM0TFnSdCe9mGCHPr2VrTqqAg8YnptMjKMFS+GRRHuTwy27cOzNTG1ZA==", "path": "microsoft.kernelmemory.ai.azureopenai/0.92.241112.1", "hashPath": "microsoft.kernelmemory.ai.azureopenai.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.LlamaSharp/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-v/8JNt6JvtLhhhMYY2cCrQUPpidpB5x8D715gwbu959lz76crum4bb4sGYqUfQblwh+5pVKLGX8N2HSCGpKbGQ==", "path": "microsoft.kernelmemory.ai.llamasharp/0.92.241112.1", "hashPath": "microsoft.kernelmemory.ai.llamasharp.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Ollama/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-fCu/QPf8wmoVRNqhWW8Sap60fkDSy6aBlFTzpOWTKeOvUV+5y42U6yGTVAZpcQz6JwH1TPGk4wA1YK7Ignqbpg==", "path": "microsoft.kernelmemory.ai.ollama/0.92.241112.1", "hashPath": "microsoft.kernelmemory.ai.ollama.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Onnx/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-6B25thT6epLDWCvgyWZnyPSuGGScaflHFC8XRdJiUI63jH7g3r32Um0sfvB0W9QutuhJrGPW7PpQ20aGx4ihFw==", "path": "microsoft.kernelmemory.ai.onnx/0.92.241112.1", "hashPath": "microsoft.kernelmemory.ai.onnx.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.OpenAI/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zw0iZC6zXny/x4ysxE+5xxhdOUS+QLRbZ8ZXOa6NGtFz5yRuWR4xwlqTaK1w79LyGfafHVeqvS0xPj7d5psc7g==", "path": "microsoft.kernelmemory.ai.openai/0.92.241112.1", "hashPath": "microsoft.kernelmemory.ai.openai.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.Core/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-jPFcZFaPDui6jBDeuVjGwFD/vBiy/8xgrfQdN2WAXSj3aUOOncNdFoJpIZsnjMHAkVWKygeAkcP3JA+TCCpTNw==", "path": "microsoft.kernelmemory.core/0.92.241112.1", "hashPath": "microsoft.kernelmemory.core.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-n53bhmbQpKt+q45Dodlrbgq6rALt5YJvP5vLdaMja8QFH4TECPdBD4eav5mNwWVqLPnhXYBgvcKPzHN6LQLnrg==", "path": "microsoft.kernelmemory.dataformats.azureaidocintel/0.92.241112.1", "hashPath": "microsoft.kernelmemory.dataformats.azureaidocintel.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.DocumentStorage.AWSS3/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-xqiCOdr/PI56ooKFaTEB1ggQOG3MMqXSxLcDVYIs8JdU//UBAazzjG5lrdeqZCrbVUrPpGmKObF/CW/Teb8NXA==", "path": "microsoft.kernelmemory.documentstorage.awss3/0.92.241112.1", "hashPath": "microsoft.kernelmemory.documentstorage.awss3.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.DocumentStorage.AzureBlobs/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZJmLqU+AXfZYrjIZsenE/iK/n176vVyamGNW7v+nGu9nwWsKnoxXRty73oGa3ai7qeEN62Q0ppH2D7HA6UMFyg==", "path": "microsoft.kernelmemory.documentstorage.azureblobs/0.92.241112.1", "hashPath": "microsoft.kernelmemory.documentstorage.azureblobs.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.AzureAISearch/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-ShJOWuRrkMuwqagRWhx24TGjT+TMKzkrVo2KjdfqROp5qkBh9iH1wf22OIA5G8KzMiQPY3soX+c/oEBswD/RPQ==", "path": "microsoft.kernelmemory.memorydb.azureaisearch/0.92.241112.1", "hashPath": "microsoft.kernelmemory.memorydb.azureaisearch.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Elasticsearch/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-ytNSXv7qhYR8GUYu8P+6yWejIaYD7xvWBzmZpqYluDHh7CT3Mro3Pcv5S4pD6KRNmk/aseU/Lr5T1j0XBd2GDQ==", "path": "microsoft.kernelmemory.memorydb.elasticsearch/0.92.241112.1", "hashPath": "microsoft.kernelmemory.memorydb.elasticsearch.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Postgres/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-RS4T9ox3KY70RZahJ04hbusqT/7IWlyjUfui3kFch24XO+w0vTQN07mwbscBj97QesMbqX8VwbceFRr2ZktaHA==", "path": "microsoft.kernelmemory.memorydb.postgres/0.92.241112.1", "hashPath": "microsoft.kernelmemory.memorydb.postgres.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Qdrant/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-TsBefK82ban9UgYB/KG9ljclZk9ZJtPqzNhPhSAndhVfLd3ga7RiSN3MEs2s3HAfC0tOuPgPNLGJHw7R+HOk8Q==", "path": "microsoft.kernelmemory.memorydb.qdrant/0.92.241112.1", "hashPath": "microsoft.kernelmemory.memorydb.qdrant.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Redis/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-JX2khT4OgBr3maCxlhCdq6tmfFbXwnKQxqY5tuG5utxoKZA2jMdMyAF4mJHs/T+t3wxfa6NU7vMErwQT9yytVA==", "path": "microsoft.kernelmemory.memorydb.redis/0.92.241112.1", "hashPath": "microsoft.kernelmemory.memorydb.redis.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.SQLServer/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-r36x9inH6jNKvicSgo1X0M9nfV1vO8N/MB2ACYozlOMb5MbqzEVf1rew58XJeIz4Ld9bEBv8G2lOEsw3e5jTnw==", "path": "microsoft.kernelmemory.memorydb.sqlserver/0.92.241112.1", "hashPath": "microsoft.kernelmemory.memorydb.sqlserver.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.MongoDbAtlas/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-/2j5cS+sl980uqst/q/v00RgRshJzba/7brT5PG5JitlSOegDu9RTsdNQcmyW+06T7ZrB9JM9rCL+mjViT+FYA==", "path": "microsoft.kernelmemory.mongodbatlas/0.92.241112.1", "hashPath": "microsoft.kernelmemory.mongodbatlas.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.Orchestration.AzureQueues/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-PD5uNhCNYImafcDGSqIaYXv9D7A+Ui+D5mEo4eh6/ZtqOvBwlGj/2K8gg7LZLYQzwBqMoM8b6wahYdlpoXj9Ew==", "path": "microsoft.kernelmemory.orchestration.azurequeues/0.92.241112.1", "hashPath": "microsoft.kernelmemory.orchestration.azurequeues.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.Orchestration.RabbitMQ/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-5kSNTm2Jl9Dq1zo9lIxEVvtdzSM27F4Q666z1KGEewN609fMRZ971JttrsyJ2j8oeZdzvhr2pBDhxbV42l9AHA==", "path": "microsoft.kernelmemory.orchestration.rabbitmq/0.92.241112.1", "hashPath": "microsoft.kernelmemory.orchestration.rabbitmq.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.Safety.AzureAIContentSafety/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-+CAUvwZZi+ziAHMk6GBSJIy/9E+FROY5gzDiZ6hWIhfrXZkjfJ29YgvE9yCzvr788taFRuz53WmrQ4E1nKu6hA==", "path": "microsoft.kernelmemory.safety.azureaicontentsafety/0.92.241112.1", "hashPath": "microsoft.kernelmemory.safety.azureaicontentsafety.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.SemanticKernelPlugin/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-o2Ecil8xYbc1uZT/dudT0t57SUzi9fDwL0VL7KcFupS+6HBsLJGcbDjHcPbMa483WZuNUGFSQceQbGStS06svw==", "path": "microsoft.kernelmemory.semantickernelplugin/0.92.241112.1", "hashPath": "microsoft.kernelmemory.semantickernelplugin.0.92.241112.1.nupkg.sha512"}, "Microsoft.KernelMemory.WebClient/0.92.241112.1": {"type": "package", "serviceable": true, "sha512": "sha512-ICndQ01Jn+6WqghH3P4/243tJXpbcqXGEK8ofm6VBs0/uFiqMPmw9lVD5mhdZsEhLjE0l+rNbgVdRn58NSpV+g==", "path": "microsoft.kernelmemory.webclient/0.92.241112.1", "hashPath": "microsoft.kernelmemory.webclient.0.92.241112.1.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime/1.19.1": {"type": "package", "serviceable": true, "sha512": "sha512-W66IpH91gujz9Pt+qmIqDF6sFSP2mR16NIlVJeQCWPP87LduRnTLWVI3z+Xzxvq2NtHMmFolikPzopR6KCWJVA==", "path": "microsoft.ml.onnxruntime/1.19.1", "hashPath": "microsoft.ml.onnxruntime.1.19.1.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Managed/1.19.1": {"type": "package", "serviceable": true, "sha512": "sha512-K7/zT016lrn4B6SWoOUAZnzTcTexh54bDVCbnSow0GcyKX/lNyntqmEHSchuXvUTbAso9DyNakq52kEExJek6A==", "path": "microsoft.ml.onnxruntime.managed/1.19.1", "hashPath": "microsoft.ml.onnxruntime.managed.1.19.1.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI/0.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-wOzIOjbgLf4mxTFbsnX2IxKh+MkaysetgcrHdGaVTUmlzaxyS0nioHmnrmuy/feNc4muX9p8jEvdpjfbL4fHyQ==", "path": "microsoft.ml.onnxruntimegenai/0.4.0", "hashPath": "microsoft.ml.onnxruntimegenai.0.4.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-gdCJoZsqvhNHkqeJjsDt6oj89xwpQqYxE8ZLmVWrjpnPICLFUCkvGhaY5Xqv4Tew+EU4jdbVrOljX9OplBItZw==", "path": "microsoft.ml.onnxruntimegenai.managed/0.4.0", "hashPath": "microsoft.ml.onnxruntimegenai.managed.0.4.0.nupkg.sha512"}, "Microsoft.ML.Tokenizers/0.22.0-preview.24378.1": {"type": "package", "serviceable": true, "sha512": "sha512-f+iWRizqQtf5Y1ikhWreKUryHizbciL/mhh5fS2+hbu5ECgth2ZbI7KSi9otQOe3z3PFPp7USgUnyFIVav2Qhg==", "path": "microsoft.ml.tokenizers/0.22.0-preview.24378.1", "hashPath": "microsoft.ml.tokenizers.0.22.0-preview.24378.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OData.Client/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TN0F9ulVEMN4Kjwyo4qYAWEwXC/M2kSPQPc4jVks3nTHbU6NAFa0vE0B+67XOaf0DobvGvsmTgUqMnSGm3tfcw==", "path": "microsoft.odata.client/8.1.0", "hashPath": "microsoft.odata.client.8.1.0.nupkg.sha512"}, "Microsoft.OData.Core/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-8BBwX+FQ5XryKCMp7rgw6h7eTszg2dgEJi1++FPJpbwk11I4zpUaBdC5vAba03RUTXOsIMTLrx04RJJXYe1ycA==", "path": "microsoft.odata.core/8.1.0", "hashPath": "microsoft.odata.core.8.1.0.nupkg.sha512"}, "Microsoft.OData.Edm/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-X3Qm/fQQvnVlrYHqjcuR+V9P89Oh/l8ymyJNmwhZHWxkTAb3y56DYpgmMRB17WWVRmBxb/VYeFnsMSCijbgaLw==", "path": "microsoft.odata.edm/8.1.0", "hashPath": "microsoft.odata.edm.8.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.SemanticKernel/1.32.0": {"type": "package", "serviceable": true, "sha512": "sha512-3K6FGWycODqKVPfG4H/SMWE8nEcl3X30W8Sc46APYd/iwULJNgNtDyXaWJELuoLC1V4BRmdydvLPrhm50U/rfQ==", "path": "microsoft.semantickernel/1.32.0", "hashPath": "microsoft.semantickernel.1.32.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.32.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkowDH+bZhahO+1pcSoqyYpZD0cS22uqIPoWA1NRVxcJlY1qrg+gU+yx0ysAgX6doCQ1sXpPfRn9e3Yxgtqtsw==", "path": "microsoft.semantickernel.abstractions/1.32.0", "hashPath": "microsoft.semantickernel.abstractions.1.32.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.32.0": {"type": "package", "serviceable": true, "sha512": "sha512-yuT+vmwG4W1T8XQN1y76RwYUkgrh5Pz8Ot59N01Fo5oA2t9AL+P7uh6cECg45+7tld6LSPVEXN/fbXvYw6JjgQ==", "path": "microsoft.semantickernel.connectors.azureopenai/1.32.0", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.32.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.32.0": {"type": "package", "serviceable": true, "sha512": "sha512-uA/XXu8Abz7AmjQP7GgSyNeC31JWKY+D1iWjpokR+8tZ8+tFXVCmk9ci8t3buDx/wyF6loHH8UbAfbRC89E0zg==", "path": "microsoft.semantickernel.connectors.openai/1.32.0", "hashPath": "microsoft.semantickernel.connectors.openai.1.32.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.32.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwhDywnFTYoCVPvlAiOvFb8NQKrjBofbrNHqROsBNwIJicSwJz9k7Zc852O5MzQEGRvCzkJlA/oUEG+U++AT2Q==", "path": "microsoft.semantickernel.core/1.32.0", "hashPath": "microsoft.semantickernel.core.1.32.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Plugins.Core/1.29.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-+cO/IBLigbCcvzp7BcqanI8uPTlrUnEGHGGRCssfHsdLxufaoy6IEsQH5E33m0dIGF7ukjOfZSSqtWJJABM4Bw==", "path": "microsoft.semantickernel.plugins.core/1.29.0-alpha", "hashPath": "microsoft.semantickernel.plugins.core.1.29.0-alpha.nupkg.sha512"}, "Microsoft.Spatial/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-iEL7UpDP/nyNh8if1yJDPekBh5q16yvOhRnCD5RYSlKamHZZeOBa3Vo+/rlbYKb5R6zemBchcfGXrCUFtZCKPQ==", "path": "microsoft.spatial/8.1.0", "hashPath": "microsoft.spatial.8.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MongoDB.Bson/2.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gg0TQUT3IEntcqdug5a9P6d8iwL5CqOpQjVBCq1hxTbkjxdGdY6a2CPv7II44AO9GYUnORYsS6dDME2b7aqYyg==", "path": "mongodb.bson/2.30.0", "hashPath": "mongodb.bson.2.30.0.nupkg.sha512"}, "MongoDB.Driver/2.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-BCG8cNF0+U3h5f/O9fu3ktrYhoESBDems1w06PExfYrn2KjHBHCBdvBRY1cIbysnZVjQAJjGtFV9XgW+hXt7Hg==", "path": "mongodb.driver/2.30.0", "hashPath": "mongodb.driver.2.30.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-oepDgu24lo44SljuHmIQ99x6jHISnMC4tLfzQGniQg39xiMD8nxalm1HM9RDZcuZbbWa4F6YLt2AIhWkny3XWA==", "path": "mongodb.driver.core/2.30.0", "hashPath": "mongodb.driver.core.2.30.0.nupkg.sha512"}, "MongoDB.Driver.GridFS/2.30.0": {"type": "package", "serviceable": true, "sha512": "sha512-u7OknyE9F0r4vSISxv/XJKIgYzfJGmzE42WpY0RTXPKM1rxe9NR4HVU1YF7GOpt8IxB6UgGXfdh7KO5PxARftg==", "path": "mongodb.driver.gridfs/2.30.0", "hashPath": "mongodb.driver.gridfs.2.30.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-B1X51jrtNacKvxKoaqWeknYeJfQS5aWf6BmVLT5JZerz3AUXFzv8edPskJYqBc3kLy1J2PWzMqqsnyb9g8FtcA==", "path": "mongodb.libmongocrypt/1.12.0", "hashPath": "mongodb.libmongocrypt.1.12.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "NetTopologySuite/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-5/+2O2ADomEdUn09mlSigACdqvAf0m/pVPGtIPEPQWnyrVykYY0NlfXLIdkMgi41kvH9kNrPqYaFBTZtHYH7Xw==", "path": "nettopologysuite/2.5.0", "hashPath": "nettopologysuite.2.5.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "path": "npgsql/8.0.3", "hashPath": "npgsql.8.0.3.nupkg.sha512"}, "NRedisStack/0.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-n5q7TaYrGNiCeS2Vi0JtRAlrl33hNXVC2Z+OFEFyo4PXNt49jxyUxASAPtxN+v9XQSRNNnnpU/AJyUGPreUXWg==", "path": "nredisstack/0.13.0", "hashPath": "nredisstack.0.13.0.nupkg.sha512"}, "OllamaSharp/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZyKKv50UzTtFVCItficzXdpvquRljYDgpCdL0JgRo5PJJlfp3TjYnNMf/HY9JPMq7RF1Rq5cAotP9ucnlYPfA==", "path": "ollamasharp/4.0.3", "hashPath": "ollamasharp.4.0.3.nupkg.sha512"}, "OpenAI/2.1.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-l+fZAS9XnCxnxGodYFPziMNF9u0ZBfOGEyOuryXnjkcPe+Z4g/ErEvGYyq559V+Q9C8J87eQ0lfq5KtwSk6ppw==", "path": "openai/2.1.0-beta.2", "hashPath": "openai.2.1.0-beta.2.nupkg.sha512"}, "PdfPig/0.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-VU27oq5O0rpD+zWiD639xXkSHtsNXqbPpYV0pzYg9VPhDibH1cq1i67f9DuW2WnzDVEti0HipLcZxDhdayIzGg==", "path": "pdfpig/0.1.9", "hashPath": "pdfpig.0.1.9.nupkg.sha512"}, "Pgvector/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-asa5XkgE3rXCNi5qHGavWZ5w+pmqWjjM1T5vN5VuPaKqV52h4TLiOM9ZJFofYgYtfhReo1QWe4NeD+hbv7ydfw==", "path": "pgvector/0.3.0", "hashPath": "pgvector.0.3.0.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "RabbitMQ.Client/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-jNsmGgmCNw2S/NzskeN2ijtGywtH4Sk/G6jWUTD5sY9SrC27Xz6BsLIiB8hdsfjeyWCa4j4GvCIGkpE8wrjU1Q==", "path": "rabbitmq.client/6.8.1", "hashPath": "rabbitmq.client.6.8.1.nupkg.sha512"}, "RBush/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ijGh9N0zZ7JfXk3oQkWCwK8SwSSByexbyh/MjbCjNxOft9eG5ZqKC1vdgiYq78h4IZRFmN4s3JZ/b10Jipud5w==", "path": "rbush/3.2.0", "hashPath": "rbush.3.2.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SendGrid/9.29.3": {"type": "package", "serviceable": true, "sha512": "sha512-nb/zHePecN9U4/Bmct+O+lpgK994JklbCCNMIgGPOone/DngjQoMCHeTvkl+m0Nglvm0dqMEshmvB4fO8eF3dA==", "path": "sendgrid/9.29.3", "hashPath": "sendgrid.9.29.3.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.AspNetCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FAjtKPZ4IzqFQBqZKPv6evcXK/F0ls7RoXI/62Pnx2igkDZ6nZ/jn/C/FxVATqQbEQvtqP+KViWYIe4NZIHa2w==", "path": "serilog.aspnetcore/8.0.0", "hashPath": "serilog.aspnetcore.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nR0iL5HwKj5v6ULo3/zpP8NMcq9E2pxYA6XKTSWCbugVs4YqPyvaqaKOY+OMpPivKp7zMEpax2UKHnDodbRB0Q==", "path": "serilog.settings.configuration/8.0.0", "hashPath": "serilog.settings.configuration.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "path": "serilog.sinks.console/5.0.0", "hashPath": "serilog.sinks.console.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.MSSqlServer/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WAmP6bMPJ0zxWflBn3eXUXg3KtrjHeuBcWW9+1QSaYrA4EdlviZEJ7ciwEL0GzqIr4/y1WjmYm5vgpEPuVmYXw==", "path": "serilog.sinks.mssqlserver/6.5.0", "hashPath": "serilog.sinks.mssqlserver.6.5.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "StackExchange.Redis/2.8.16": {"type": "package", "serviceable": true, "sha512": "sha512-WaoulkOqOC9jHepca3JZKFTqndCWab5uYS7qCzmiQDlrTkFaDN7eLSlEfHycBxipRnQY9ppZM7QSsWAwUEGblw==", "path": "stackexchange.redis/2.8.16", "hashPath": "stackexchange.redis.2.8.16.nupkg.sha512"}, "starkbank-ecdsa/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog==", "path": "starkbank-ecdsa/1.3.3", "hashPath": "starkbank-ecdsa.1.3.3.nupkg.sha512"}, "Stripe.net/45.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-/Z7RrvZV3v7eTOZgxThWn1RJ6bVtL/m7ncRcXcheF7w8/lFHsL+DAxgaEPktq+M/dChCfkvcghrUYOrkc0BVQg==", "path": "stripe.net/45.14.0", "hashPath": "stripe.net.45.14.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "path": "swashbuckle.aspnetcore/6.4.0", "hashPath": "swashbuckle.aspnetcore.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-s9+M5El+DXdCRRLzxak8uGBKWT8H/eIssGpFtpaMKdJULrQbBDPH/zFbVyHX+NDczhS5EvjHFbBH9/L+0UhmcA==", "path": "system.clientmodel/1.2.1", "hashPath": "system.clientmodel.1.2.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "path": "system.diagnostics.eventlog/8.0.1", "hashPath": "system.diagnostics.eventlog.8.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B<PERSON><PERSON>uec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "path": "system.memory.data/8.0.1", "hashPath": "system.memory.data.8.0.1.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Tensors/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fhODzTe9ON9IzmRfyVeA6L8yXOciMtpq1YufkRVBliggcVKZE+XDxqIn46+yF4PWR6wNPuDpXtPpuY86VcKxUA==", "path": "system.numerics.tensors/8.0.0", "hashPath": "system.numerics.tensors.8.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-tdl7Q47P09UpRu0C/OQsGJU6GacBzzk4vfp5My9rodD+BchrxmajORnTthH8RxPUTPrIoVDJmLyvJcGxB267nQ==", "path": "system.runtime.caching/8.0.1", "hashPath": "system.runtime.caching.8.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "AdminPortalBackend.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdminPortalBackend.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}