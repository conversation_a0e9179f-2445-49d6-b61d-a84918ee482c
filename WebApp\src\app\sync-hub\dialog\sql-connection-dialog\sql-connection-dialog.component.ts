import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { DbConnectionDto, DbConnectionServiceProxy, TestConnectionServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatOption } from '@angular/material/core';
import { CommonModule } from '@angular/common';
import { ToastrService } from 'ngx-toastr';

import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { ToasterService } from '../../../toaster.service';

@Component({
  selector: 'app-sql-connection-dialog',
  standalone: true,
  imports: [MatFormFieldModule, MatInputModule, FormsModule, MatButtonModule, ServiceProxyModule, MatRadioModule, MatCheckboxModule, MatOption, CommonModule, ToastModule, ButtonModule, RippleModule],
  templateUrl: './sql-connection-dialog.component.html',
  styleUrl: './sql-connection-dialog.component.css'
})
export class SqlConnectionDialogComponent {
  sqlServer = {
    connectionName: '',
    type: 'Ms SQL Server', // This can be hardcoded since it's not editable
    description: '',
    ServerName: '',
    UserId: '',
    Password: '',
    TrustedCertificate: false,
  };
  isUpdate: boolean = false;
  sqlData = new DbConnectionDto()
  isError = false;
  isLoading = false;
  isSaving = false;
  constructor(
    private dialogRef: MatDialogRef<SqlConnectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: string,  // Injecting the data
    private _dbConnectionServices: DbConnectionServiceProxy,
    private _testConnectionService: TestConnectionServiceProxy,
    private toastr: ToastrService, private messageService: MessageService, private toasterService: ToasterService
  ) {

  }

  ngOnInit(): void {
    if (this.data) {
      this.isUpdate = true
      this._dbConnectionServices.getSingle(this.data).subscribe((res) => {
        //console.log(res)

        this.sqlData = res
        this.sqlServer.connectionName = this.sqlData.connectionName
        this.sqlServer.type = this.sqlData.type
        this.sqlServer.description = this.sqlData.description

        if (this.sqlData.connectionCredJson) {

          const connectionCredObj = JSON.parse(this.sqlData.connectionCredJson);
          this.sqlServer.ServerName = connectionCredObj.ServerName;
          this.sqlServer.UserId = connectionCredObj.UserId;
          this.sqlServer.Password = connectionCredObj.Password;
          this.sqlServer.TrustedCertificate = connectionCredObj.TrustedCertificate;
        }

      })
    }
  }

  onSubmit(form: any) {
    //console.log(this.isSaving);

    this.isSaving = true
    if (this.isUpdate) {
      this.sqlData.guid = this.data
    } else {
      this.sqlData.guid = '00000000-0000-0000-0000-000000000000';
    }
    this.sqlData.connectionName = this.sqlServer.connectionName;
    this.sqlData.description = this.sqlServer.description;
    this.sqlData.type = this.sqlServer.type;

    // Convert the SQL connection details into a JSON string
    const connectionCredJson = {
      ServerName: this.sqlServer.ServerName,
      UserId: this.sqlServer.UserId,
      Password: this.sqlServer.Password,
      TrustedCertificate: this.sqlServer.TrustedCertificate
    };
    // Assign the JSON string to connectionCredJson property
    this.sqlData.connectionCredJson = JSON.stringify(connectionCredJson);
    // Pass the data to the service to create or edit the SQL connection
    //console.log(this.sqlData)

    this._dbConnectionServices.createOrEdit(this.sqlData).subscribe((res) => {
      if (res) {
        this.isSaving = false;
        this.dialogRef.close(this.sqlServer);
      }
    });
  }
  testConnection() {
    this.isLoading = true

    this._testConnectionService.checkSqlConnection(this.sqlServer.UserId, this.sqlServer.Password, this.sqlServer.ServerName).subscribe((res) => {
      if (res) {
        this.isLoading = false
        //console.log(res)
        if (!res.isError) {
          this.isError = true
          this.toasterService.showToaster("success", res.message)

        } else {
          this.toasterService.showToaster("error", res.message)
        }
      }

    }, (error) => {

      this.toasterService.showToaster("error", error.message)
    }
    )


    // api call
    // res.Iserror == false toogle variable
  }
  // Method to close the dialog
  onClose() {
    this.dialogRef.close();
  }


}
