//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.1.0.0 (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* tslint:disable */
/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

import { DateTime, Duration } from "luxon";

export const API_BASE_URL = new InjectionToken<string>('API_BASE_URL');

@Injectable()
export class AppSettingServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param notificationUrl (optional) 
     * @return Success
     */
    addOrEditWebHookNotification(notificationUrl: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/AppSetting/AddOrEditWebHookNotification?";
        if (notificationUrl === null)
            throw new Error("The parameter 'notificationUrl' cannot be null.");
        else if (notificationUrl !== undefined)
            url_ += "notificationUrl=" + encodeURIComponent("" + notificationUrl) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddOrEditWebHookNotification(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddOrEditWebHookNotification(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processAddOrEditWebHookNotification(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getWebhookNotificationUrl(): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/AppSetting/GetWebhookNotificationUrl";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetWebhookNotificationUrl(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetWebhookNotificationUrl(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processGetWebhookNotificationUrl(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class ATestServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return Success
     */
    entities(connectionId: string): Observable<void> {
        let url_ = this.baseUrl + "/api/ATest/entities/{connectionId}";
        if (connectionId === undefined || connectionId === null)
            throw new Error("The parameter 'connectionId' must be defined.");
        url_ = url_.replace("{connectionId}", encodeURIComponent("" + connectionId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processEntities(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processEntities(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processEntities(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    metadata(connectionId: string): Observable<void> {
        let url_ = this.baseUrl + "/api/ATest/metadata/{connectionId}";
        if (connectionId === undefined || connectionId === null)
            throw new Error("The parameter 'connectionId' must be defined.");
        url_ = url_.replace("{connectionId}", encodeURIComponent("" + connectionId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processMetadata(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processMetadata(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processMetadata(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    companies(connectionId: string): Observable<void> {
        let url_ = this.baseUrl + "/api/ATest/companies/{connectionId}";
        if (connectionId === undefined || connectionId === null)
            throw new Error("The parameter 'connectionId' must be defined.");
        url_ = url_.replace("{connectionId}", encodeURIComponent("" + connectionId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCompanies(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCompanies(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCompanies(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    testBcUrl(token: string): Observable<void> {
        let url_ = this.baseUrl + "/api/ATest/test-bc-url/{token}";
        if (token === undefined || token === null)
            throw new Error("The parameter 'token' must be defined.");
        url_ = url_.replace("{token}", encodeURIComponent("" + token));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processTestBcUrl(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processTestBcUrl(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processTestBcUrl(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    sqltobc(): Observable<void> {
        let url_ = this.baseUrl + "/api/ATest/sqltobc";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSqltobc(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSqltobc(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processSqltobc(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    bctosql(): Observable<void> {
        let url_ = this.baseUrl + "/api/ATest/bctosql";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processBctosql(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processBctosql(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processBctosql(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class ConnectionIntegrationServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return Success
     */
    getDatabase(guid: string): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetDatabase/{guid}";
        if (guid === undefined || guid === null)
            throw new Error("The parameter 'guid' must be defined.");
        url_ = url_.replace("{guid}", encodeURIComponent("" + guid));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetDatabase(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetDatabase(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetDatabase(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getTables(guid: string, databaseName: string): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetTables/{guid}/{databaseName}";
        if (guid === undefined || guid === null)
            throw new Error("The parameter 'guid' must be defined.");
        url_ = url_.replace("{guid}", encodeURIComponent("" + guid));
        if (databaseName === undefined || databaseName === null)
            throw new Error("The parameter 'databaseName' must be defined.");
        url_ = url_.replace("{databaseName}", encodeURIComponent("" + databaseName));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetTables(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetTables(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetTables(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getColumn(guid: string, databaseName: string, tableName: string): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetColumn/{guid}/{databaseName}/{tableName}";
        if (guid === undefined || guid === null)
            throw new Error("The parameter 'guid' must be defined.");
        url_ = url_.replace("{guid}", encodeURIComponent("" + guid));
        if (databaseName === undefined || databaseName === null)
            throw new Error("The parameter 'databaseName' must be defined.");
        url_ = url_.replace("{databaseName}", encodeURIComponent("" + databaseName));
        if (tableName === undefined || tableName === null)
            throw new Error("The parameter 'tableName' must be defined.");
        url_ = url_.replace("{tableName}", encodeURIComponent("" + tableName));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetColumn(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetColumn(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetColumn(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    edit(body: ConnectionIntegration | undefined): Observable<ConnectionIntegration> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/Edit";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processEdit(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processEdit(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ConnectionIntegration>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ConnectionIntegration>;
        }));
    }

    protected processEdit(response: HttpResponseBase): Observable<ConnectionIntegration> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ConnectionIntegration.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    create(body: CreateConnectionIntegrationDto | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/Create";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCreate(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param connectionGuid (optional) 
     * @param database (optional) 
     * @return Success
     */
    getCompanyId(connectionGuid: string | undefined, database: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetCompanyId?";
        if (connectionGuid === null)
            throw new Error("The parameter 'connectionGuid' cannot be null.");
        else if (connectionGuid !== undefined)
            url_ += "connectionGuid=" + encodeURIComponent("" + connectionGuid) + "&";
        if (database === null)
            throw new Error("The parameter 'database' cannot be null.");
        else if (database !== undefined)
            url_ += "database=" + encodeURIComponent("" + database) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCompanyId(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCompanyId(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processGetCompanyId(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param isExecute (optional) 
     * @param body (optional) 
     * @return Success
     */
    createTableForODataWebService(isExecute: boolean | undefined, body: CreateConnectionIntegrationDto | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/CreateTableForODataWebService?";
        if (isExecute === null)
            throw new Error("The parameter 'isExecute' cannot be null.");
        else if (isExecute !== undefined)
            url_ += "isExecute=" + encodeURIComponent("" + isExecute) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateTableForODataWebService(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateTableForODataWebService(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCreateTableForODataWebService(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getSqlSqlAll(): Observable<ConnectionIntegration[]> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetSqlSqlAllAsync";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetSqlSqlAll(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetSqlSqlAll(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ConnectionIntegration[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ConnectionIntegration[]>;
        }));
    }

    protected processGetSqlSqlAll(response: HttpResponseBase): Observable<ConnectionIntegration[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(ConnectionIntegration.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getBcSqlAll(): Observable<ConnectionIntegration[]> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetBcSqlAllAsync";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetBcSqlAll(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetBcSqlAll(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ConnectionIntegration[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ConnectionIntegration[]>;
        }));
    }

    protected processGetBcSqlAll(response: HttpResponseBase): Observable<ConnectionIntegration[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(ConnectionIntegration.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param guid (optional) 
     * @return Success
     */
    getSingle(guid: string | undefined): Observable<ConnectionIntegration> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetSingle?";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetSingle(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetSingle(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ConnectionIntegration>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ConnectionIntegration>;
        }));
    }

    protected processGetSingle(response: HttpResponseBase): Observable<ConnectionIntegration> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ConnectionIntegration.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param guid (optional) 
     * @return Success
     */
    delete(guid: string | undefined): Observable<ConnectionIntegration> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/Delete?";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDelete(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDelete(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ConnectionIntegration>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ConnectionIntegration>;
        }));
    }

    protected processDelete(response: HttpResponseBase): Observable<ConnectionIntegration> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ConnectionIntegration.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    scheduleJob(body: JobRequest | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/ScheduleJob";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processScheduleJob(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processScheduleJob(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processScheduleJob(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param guid (optional) 
     * @return Success
     */
    disableJob(guid: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/DisableJob?";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDisableJob(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDisableJob(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processDisableJob(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param guid (optional) 
     * @return Success
     */
    deleteJob(guid: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/DeleteJob?";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDeleteJob(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDeleteJob(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processDeleteJob(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param integrationId (optional) 
     * @return Success
     */
    executeJob(integrationId: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/ExecuteJob?";
        if (integrationId === null)
            throw new Error("The parameter 'integrationId' cannot be null.");
        else if (integrationId !== undefined)
            url_ += "integrationId=" + encodeURIComponent("" + integrationId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processExecuteJob(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processExecuteJob(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processExecuteJob(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getEntityNames(guid: string): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetEntityNames/{guid}";
        if (guid === undefined || guid === null)
            throw new Error("The parameter 'guid' must be defined.");
        url_ = url_.replace("{guid}", encodeURIComponent("" + guid));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetEntityNames(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetEntityNames(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetEntityNames(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param entityName (optional) 
     * @param guid (optional) 
     * @return Success
     */
    getPropertiesForEntity(entityName: string | undefined, guid: string | undefined): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetPropertiesForEntity?";
        if (entityName === null)
            throw new Error("The parameter 'entityName' cannot be null.");
        else if (entityName !== undefined)
            url_ += "entityName=" + encodeURIComponent("" + entityName) + "&";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetPropertiesForEntity(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetPropertiesForEntity(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetPropertiesForEntity(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getCompanyNames(guid: string): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetCompanyNames/{guid}";
        if (guid === undefined || guid === null)
            throw new Error("The parameter 'guid' must be defined.");
        url_ = url_.replace("{guid}", encodeURIComponent("" + guid));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCompanyNames(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCompanyNames(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetCompanyNames(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param integrationGuid (optional) 
     * @param destinationGuid (optional) 
     * @param databaseName (optional) 
     * @param tableName (optional) 
     * @param sourceGuid (optional) 
     * @return Success
     */
    createTrigger(integrationGuid: string | undefined, destinationGuid: string | undefined, databaseName: string | undefined, tableName: string | undefined, sourceGuid: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/CreateTrigger?";
        if (integrationGuid === null)
            throw new Error("The parameter 'integrationGuid' cannot be null.");
        else if (integrationGuid !== undefined)
            url_ += "integrationGuid=" + encodeURIComponent("" + integrationGuid) + "&";
        if (destinationGuid === null)
            throw new Error("The parameter 'destinationGuid' cannot be null.");
        else if (destinationGuid !== undefined)
            url_ += "destinationGuid=" + encodeURIComponent("" + destinationGuid) + "&";
        if (databaseName === null)
            throw new Error("The parameter 'databaseName' cannot be null.");
        else if (databaseName !== undefined)
            url_ += "databaseName=" + encodeURIComponent("" + databaseName) + "&";
        if (tableName === null)
            throw new Error("The parameter 'tableName' cannot be null.");
        else if (tableName !== undefined)
            url_ += "tableName=" + encodeURIComponent("" + tableName) + "&";
        if (sourceGuid === null)
            throw new Error("The parameter 'sourceGuid' cannot be null.");
        else if (sourceGuid !== undefined)
            url_ += "sourceGuid=" + encodeURIComponent("" + sourceGuid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateTrigger(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateTrigger(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCreateTrigger(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param destinationGuid (optional) 
     * @param databaseName (optional) 
     * @param tableName (optional) 
     * @return Success
     */
    deleteTrigger(destinationGuid: string | undefined, databaseName: string | undefined, tableName: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/DeleteTrigger?";
        if (destinationGuid === null)
            throw new Error("The parameter 'destinationGuid' cannot be null.");
        else if (destinationGuid !== undefined)
            url_ += "destinationGuid=" + encodeURIComponent("" + destinationGuid) + "&";
        if (databaseName === null)
            throw new Error("The parameter 'databaseName' cannot be null.");
        else if (databaseName !== undefined)
            url_ += "databaseName=" + encodeURIComponent("" + databaseName) + "&";
        if (tableName === null)
            throw new Error("The parameter 'tableName' cannot be null.");
        else if (tableName !== undefined)
            url_ += "tableName=" + encodeURIComponent("" + tableName) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDeleteTrigger(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDeleteTrigger(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processDeleteTrigger(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    editTableForODataWebService(body: ConnectionIntegration | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/EditTableForODataWebService";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processEditTableForODataWebService(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processEditTableForODataWebService(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processEditTableForODataWebService(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param destinationGuid (optional) 
     * @param destinationDatabase (optional) 
     * @param body (optional) 
     * @return Success
     */
    checkTableExist(destinationGuid: string | undefined, destinationDatabase: string | undefined, body: string[] | undefined): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/CheckTableExist?";
        if (destinationGuid === null)
            throw new Error("The parameter 'destinationGuid' cannot be null.");
        else if (destinationGuid !== undefined)
            url_ += "destinationGuid=" + encodeURIComponent("" + destinationGuid) + "&";
        if (destinationDatabase === null)
            throw new Error("The parameter 'destinationDatabase' cannot be null.");
        else if (destinationDatabase !== undefined)
            url_ += "destinationDatabase=" + encodeURIComponent("" + destinationDatabase) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCheckTableExist(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCheckTableExist(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processCheckTableExist(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param entityName (optional) 
     * @param connectionGuid (optional) 
     * @return Success
     */
    isSystemRowVersionAvailable(entityName: string | undefined, connectionGuid: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/IsSystemRowVersionAvailable?";
        if (entityName === null)
            throw new Error("The parameter 'entityName' cannot be null.");
        else if (entityName !== undefined)
            url_ += "entityName=" + encodeURIComponent("" + entityName) + "&";
        if (connectionGuid === null)
            throw new Error("The parameter 'connectionGuid' cannot be null.");
        else if (connectionGuid !== undefined)
            url_ += "connectionGuid=" + encodeURIComponent("" + connectionGuid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processIsSystemRowVersionAvailable(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processIsSystemRowVersionAvailable(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processIsSystemRowVersionAvailable(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    createView(body: CreateViewDto | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/CreateView";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateView(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateView(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCreateView(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    checkIfViewExists(body: IsViewDto | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/CheckIfViewExists";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCheckIfViewExists(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCheckIfViewExists(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCheckIfViewExists(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param tableName (optional) 
     * @return Success
     */
    getFieldNames(tableName: string | undefined): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetFieldNames?";
        if (tableName === null)
            throw new Error("The parameter 'tableName' cannot be null.");
        else if (tableName !== undefined)
            url_ += "tableName=" + encodeURIComponent("" + tableName) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFieldNames(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFieldNames(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetFieldNames(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getTableNames(): Observable<ResponseMessageList> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetTableNames";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetTableNames(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetTableNames(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessageList>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessageList>;
        }));
    }

    protected processGetTableNames(response: HttpResponseBase): Observable<ResponseMessageList> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessageList.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param serviceName (optional) 
     * @return Success
     */
    getFieldFromService(serviceName: string | undefined): Observable<ServiceField> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetFieldFromService?";
        if (serviceName === null)
            throw new Error("The parameter 'serviceName' cannot be null.");
        else if (serviceName !== undefined)
            url_ += "serviceName=" + encodeURIComponent("" + serviceName) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFieldFromService(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFieldFromService(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ServiceField>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ServiceField>;
        }));
    }

    protected processGetFieldFromService(response: HttpResponseBase): Observable<ServiceField> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ServiceField.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    processSqlDelete(): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/ProcessSqlDelete";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processProcessSqlDelete(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processProcessSqlDelete(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processProcessSqlDelete(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getDestinationPrimaryKey(destinationTable: string): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/ConnectionIntegration/GetDestinationPrimaryKey/{destinationTable}";
        if (destinationTable === undefined || destinationTable === null)
            throw new Error("The parameter 'destinationTable' must be defined.");
        url_ = url_.replace("{destinationTable}", encodeURIComponent("" + destinationTable));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetDestinationPrimaryKey(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetDestinationPrimaryKey(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processGetDestinationPrimaryKey(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class DbConnectionServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return Success
     */
    getAll(): Observable<DbConnectionDto[]> {
        let url_ = this.baseUrl + "/api/DbConnection/GetAll";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetAll(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetAll(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<DbConnectionDto[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<DbConnectionDto[]>;
        }));
    }

    protected processGetAll(response: HttpResponseBase): Observable<DbConnectionDto[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(DbConnectionDto.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param guid (optional) 
     * @return Success
     */
    getSingle(guid: string | undefined): Observable<DbConnectionDto> {
        let url_ = this.baseUrl + "/api/DbConnection/GetSingle?";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetSingle(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetSingle(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<DbConnectionDto>;
                }
            } else
                return _observableThrow(response_) as any as Observable<DbConnectionDto>;
        }));
    }

    protected processGetSingle(response: HttpResponseBase): Observable<DbConnectionDto> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = DbConnectionDto.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    createOrEdit(body: DbConnectionDto | undefined): Observable<DbConnectionDto> {
        let url_ = this.baseUrl + "/api/DbConnection/CreateOrEdit";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateOrEdit(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateOrEdit(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<DbConnectionDto>;
                }
            } else
                return _observableThrow(response_) as any as Observable<DbConnectionDto>;
        }));
    }

    protected processCreateOrEdit(response: HttpResponseBase): Observable<DbConnectionDto> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = DbConnectionDto.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param guid (optional) 
     * @return Success
     */
    delete(guid: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/DbConnection/Delete?";
        if (guid === null)
            throw new Error("The parameter 'guid' cannot be null.");
        else if (guid !== undefined)
            url_ += "guid=" + encodeURIComponent("" + guid) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDelete(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDelete(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processDelete(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param integrationId (optional) 
     * @return Success
     */
    getTop10JobLogs(integrationId: string | undefined): Observable<JobLogEntry[]> {
        let url_ = this.baseUrl + "/api/DbConnection/GetTop10JobLogs?";
        if (integrationId === null)
            throw new Error("The parameter 'integrationId' cannot be null.");
        else if (integrationId !== undefined)
            url_ += "integrationId=" + encodeURIComponent("" + integrationId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetTop10JobLogs(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetTop10JobLogs(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<JobLogEntry[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<JobLogEntry[]>;
        }));
    }

    protected processGetTop10JobLogs(response: HttpResponseBase): Observable<JobLogEntry[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(JobLogEntry.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class ExternalServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    request(body: RequestPayload | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/External/request";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRequest(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRequest(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processRequest(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class FileServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param files (optional) 
     * @return Success
     */
    upload(files: FileParameter[] | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/File/Upload";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = new FormData();
        if (files === null || files === undefined)
            throw new Error("The parameter 'files' cannot be null.");
        else
            files.forEach(item_ => content_.append("files", item_.data, item_.fileName ? item_.fileName : "files") );

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpload(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpload(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processUpload(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    getfile(fileName: string): Observable<void> {
        let url_ = this.baseUrl + "/api/File/Getfile/{fileName}";
        if (fileName === undefined || fileName === null)
            throw new Error("The parameter 'fileName' must be defined.");
        url_ = url_.replace("{fileName}", encodeURIComponent("" + fileName));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetfile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetfile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processGetfile(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    deleteFile(body: string[] | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/File/DeleteFile";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDeleteFile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDeleteFile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processDeleteFile(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class GenAIServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return Success
     */
    getAIResponse(question: string): Observable<AIResponseMessage> {
        let url_ = this.baseUrl + "/api/GenAI/GetAIResponse/{question}";
        if (question === undefined || question === null)
            throw new Error("The parameter 'question' must be defined.");
        url_ = url_.replace("{question}", encodeURIComponent("" + question));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetAIResponse(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetAIResponse(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<AIResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<AIResponseMessage>;
        }));
    }

    protected processGetAIResponse(response: HttpResponseBase): Observable<AIResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = AIResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    addAIMemory(): Observable<void> {
        let url_ = this.baseUrl + "/api/GenAI/AddAIMemory";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddAIMemory(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddAIMemory(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processAddAIMemory(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    mapColumns(body: MapColumns | undefined): Observable<MappedColumn[]> {
        let url_ = this.baseUrl + "/api/GenAI/MapColumns";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processMapColumns(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processMapColumns(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<MappedColumn[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<MappedColumn[]>;
        }));
    }

    protected processMapColumns(response: HttpResponseBase): Observable<MappedColumn[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(MappedColumn.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class TestConnectionServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param username (optional) 
     * @param password (optional) 
     * @param serverName (optional) 
     * @return Success
     */
    checkSqlConnection(username: string | undefined, password: string | undefined, serverName: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/TestConnection/CheckSqlConnection?";
        if (username === null)
            throw new Error("The parameter 'username' cannot be null.");
        else if (username !== undefined)
            url_ += "username=" + encodeURIComponent("" + username) + "&";
        if (password === null)
            throw new Error("The parameter 'password' cannot be null.");
        else if (password !== undefined)
            url_ += "password=" + encodeURIComponent("" + password) + "&";
        if (serverName === null)
            throw new Error("The parameter 'serverName' cannot be null.");
        else if (serverName !== undefined)
            url_ += "serverName=" + encodeURIComponent("" + serverName) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCheckSqlConnection(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCheckSqlConnection(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCheckSqlConnection(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param clientSecret (optional) 
     * @param clientId (optional) 
     * @param tokenEndpoint (optional) 
     * @param scope (optional) 
     * @return Success
     */
    checkBcConnection(clientSecret: string | undefined, clientId: string | undefined, tokenEndpoint: string | undefined, scope: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/TestConnection/CheckBcConnection?";
        if (clientSecret === null)
            throw new Error("The parameter 'clientSecret' cannot be null.");
        else if (clientSecret !== undefined)
            url_ += "clientSecret=" + encodeURIComponent("" + clientSecret) + "&";
        if (clientId === null)
            throw new Error("The parameter 'clientId' cannot be null.");
        else if (clientId !== undefined)
            url_ += "clientId=" + encodeURIComponent("" + clientId) + "&";
        if (tokenEndpoint === null)
            throw new Error("The parameter 'tokenEndpoint' cannot be null.");
        else if (tokenEndpoint !== undefined)
            url_ += "tokenEndpoint=" + encodeURIComponent("" + tokenEndpoint) + "&";
        if (scope === null)
            throw new Error("The parameter 'scope' cannot be null.");
        else if (scope !== undefined)
            url_ += "scope=" + encodeURIComponent("" + scope) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCheckBcConnection(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCheckBcConnection(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processCheckBcConnection(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    realtime(): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/TestConnection/realtime";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRealtime(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRealtime(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processRealtime(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class ServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return Success
     */
    weatherForecast(): Observable<WeatherForecast[]> {
        let url_ = this.baseUrl + "/WeatherForecast";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processWeatherForecast(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processWeatherForecast(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<WeatherForecast[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<WeatherForecast[]>;
        }));
    }

    protected processWeatherForecast(response: HttpResponseBase): Observable<WeatherForecast[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            if (Array.isArray(resultData200)) {
                result200 = [] as any;
                for (let item of resultData200)
                    result200!.push(WeatherForecast.fromJS(item));
            }
            else {
                result200 = <any>null;
            }
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

@Injectable()
export class WebhooksServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return Success
     */
    register(body: SubscriptionRequest | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/webhooks/register";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRegister(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRegister(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processRegister(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param validationToken (optional) 
     * @param body (optional) 
     * @return Success
     */
    bcNotifications(validationToken: string | undefined, body: WebhookRequest | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/webhooks/bc-notifications?";
        if (validationToken === null)
            throw new Error("The parameter 'validationToken' cannot be null.");
        else if (validationToken !== undefined)
            url_ += "validationToken=" + encodeURIComponent("" + validationToken) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processBcNotifications(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processBcNotifications(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processBcNotifications(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @return Success
     */
    deleteSubscription(connectionId: string, integrationId: string): Observable<void> {
        let url_ = this.baseUrl + "/api/webhooks/delete-subscription/{connectionId}/{integrationId}";
        if (connectionId === undefined || connectionId === null)
            throw new Error("The parameter 'connectionId' must be defined.");
        url_ = url_.replace("{connectionId}", encodeURIComponent("" + connectionId));
        if (integrationId === undefined || integrationId === null)
            throw new Error("The parameter 'integrationId' must be defined.");
        url_ = url_.replace("{integrationId}", encodeURIComponent("" + integrationId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDeleteSubscription(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDeleteSubscription(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processDeleteSubscription(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return _observableOf(null as any);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * @param connectionId (optional) 
     * @param integrationId (optional) 
     * @return Success
     */
    isSubscription(connectionId: string | undefined, integrationId: string | undefined): Observable<ResponseMessage> {
        let url_ = this.baseUrl + "/api/webhooks/IsSubscription?";
        if (connectionId === null)
            throw new Error("The parameter 'connectionId' cannot be null.");
        else if (connectionId !== undefined)
            url_ += "connectionId=" + encodeURIComponent("" + connectionId) + "&";
        if (integrationId === null)
            throw new Error("The parameter 'integrationId' cannot be null.");
        else if (integrationId !== undefined)
            url_ += "integrationId=" + encodeURIComponent("" + integrationId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "text/plain"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processIsSubscription(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processIsSubscription(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResponseMessage>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResponseMessage>;
        }));
    }

    protected processIsSubscription(response: HttpResponseBase): Observable<ResponseMessage> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResponseMessage.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export class AIResponseMessage implements IAIResponseMessage {
    isError!: boolean;
    message!: string | undefined;

    constructor(data?: IAIResponseMessage) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.isError = _data["isError"];
            this.message = _data["message"];
        }
    }

    static fromJS(data: any): AIResponseMessage {
        data = typeof data === 'object' ? data : {};
        let result = new AIResponseMessage();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["message"] = this.message;
        return data;
    }
}

export interface IAIResponseMessage {
    isError: boolean;
    message: string | undefined;
}

export class ConnectionIntegration implements IConnectionIntegration {
    guid!: string;
    integrationName!: string | undefined;
    sourceConnectionGuid!: string;
    sourceConnectionName!: string | undefined;
    sourceDatabase!: string | undefined;
    sourceTable!: string | undefined;
    destinationConnectionGuid!: string;
    destinationConnectionName!: string | undefined;
    destinationDatabase!: string | undefined;
    destinationTable!: string | undefined;
    sourcePrimaryKey!: string | undefined;
    destinationPrimaryKey!: string | undefined;
    jobFrequency!: string | undefined;
    isActive!: boolean;
    mappedColumns!: string | undefined;
    settings!: string | undefined;
    createdDate!: DateTime | undefined;
    lastModifiedDate!: DateTime | undefined;
    lastExecutionDate!: string | undefined;

    constructor(data?: IConnectionIntegration) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.guid = _data["guid"];
            this.integrationName = _data["integrationName"];
            this.sourceConnectionGuid = _data["sourceConnectionGuid"];
            this.sourceConnectionName = _data["sourceConnectionName"];
            this.sourceDatabase = _data["sourceDatabase"];
            this.sourceTable = _data["sourceTable"];
            this.destinationConnectionGuid = _data["destinationConnectionGuid"];
            this.destinationConnectionName = _data["destinationConnectionName"];
            this.destinationDatabase = _data["destinationDatabase"];
            this.destinationTable = _data["destinationTable"];
            this.sourcePrimaryKey = _data["sourcePrimaryKey"];
            this.destinationPrimaryKey = _data["destinationPrimaryKey"];
            this.jobFrequency = _data["jobFrequency"];
            this.isActive = _data["isActive"];
            this.mappedColumns = _data["mappedColumns"];
            this.settings = _data["settings"];
            this.createdDate = _data["createdDate"] ? DateTime.fromISO(_data["createdDate"].toString()) : <any>undefined;
            this.lastModifiedDate = _data["lastModifiedDate"] ? DateTime.fromISO(_data["lastModifiedDate"].toString()) : <any>undefined;
            this.lastExecutionDate = _data["lastExecutionDate"];
        }
    }

    static fromJS(data: any): ConnectionIntegration {
        data = typeof data === 'object' ? data : {};
        let result = new ConnectionIntegration();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["guid"] = this.guid;
        data["integrationName"] = this.integrationName;
        data["sourceConnectionGuid"] = this.sourceConnectionGuid;
        data["sourceConnectionName"] = this.sourceConnectionName;
        data["sourceDatabase"] = this.sourceDatabase;
        data["sourceTable"] = this.sourceTable;
        data["destinationConnectionGuid"] = this.destinationConnectionGuid;
        data["destinationConnectionName"] = this.destinationConnectionName;
        data["destinationDatabase"] = this.destinationDatabase;
        data["destinationTable"] = this.destinationTable;
        data["sourcePrimaryKey"] = this.sourcePrimaryKey;
        data["destinationPrimaryKey"] = this.destinationPrimaryKey;
        data["jobFrequency"] = this.jobFrequency;
        data["isActive"] = this.isActive;
        data["mappedColumns"] = this.mappedColumns;
        data["settings"] = this.settings;
        data["createdDate"] = this.createdDate ? this.createdDate.toString() : <any>undefined;
        data["lastModifiedDate"] = this.lastModifiedDate ? this.lastModifiedDate.toString() : <any>undefined;
        data["lastExecutionDate"] = this.lastExecutionDate;
        return data;
    }
}

export interface IConnectionIntegration {
    guid: string;
    integrationName: string | undefined;
    sourceConnectionGuid: string;
    sourceConnectionName: string | undefined;
    sourceDatabase: string | undefined;
    sourceTable: string | undefined;
    destinationConnectionGuid: string;
    destinationConnectionName: string | undefined;
    destinationDatabase: string | undefined;
    destinationTable: string | undefined;
    sourcePrimaryKey: string | undefined;
    destinationPrimaryKey: string | undefined;
    jobFrequency: string | undefined;
    isActive: boolean;
    mappedColumns: string | undefined;
    settings: string | undefined;
    createdDate: DateTime | undefined;
    lastModifiedDate: DateTime | undefined;
    lastExecutionDate: string | undefined;
}

export class CreateConnectionIntegrationDto implements ICreateConnectionIntegrationDto {
    sourceConnectionGuid!: string;
    sourceConnectionName!: string | undefined;
    sourceDatabase!: string | undefined;
    sourceTable!: string[] | undefined;
    destinationConnectionGuid!: string;
    destinationConnectionName!: string | undefined;
    destinationDatabase!: string | undefined;
    destinationTable!: string[] | undefined;
    sourcePrimaryKey!: string[] | undefined;
    destinationPrimaryKey!: string[] | undefined;
    jobFrequency!: string | undefined;
    mappedColumns!: string[] | undefined;
    settings!: string | undefined;

    constructor(data?: ICreateConnectionIntegrationDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.sourceConnectionGuid = _data["sourceConnectionGuid"];
            this.sourceConnectionName = _data["sourceConnectionName"];
            this.sourceDatabase = _data["sourceDatabase"];
            if (Array.isArray(_data["sourceTable"])) {
                this.sourceTable = [] as any;
                for (let item of _data["sourceTable"])
                    this.sourceTable!.push(item);
            }
            this.destinationConnectionGuid = _data["destinationConnectionGuid"];
            this.destinationConnectionName = _data["destinationConnectionName"];
            this.destinationDatabase = _data["destinationDatabase"];
            if (Array.isArray(_data["destinationTable"])) {
                this.destinationTable = [] as any;
                for (let item of _data["destinationTable"])
                    this.destinationTable!.push(item);
            }
            if (Array.isArray(_data["sourcePrimaryKey"])) {
                this.sourcePrimaryKey = [] as any;
                for (let item of _data["sourcePrimaryKey"])
                    this.sourcePrimaryKey!.push(item);
            }
            if (Array.isArray(_data["destinationPrimaryKey"])) {
                this.destinationPrimaryKey = [] as any;
                for (let item of _data["destinationPrimaryKey"])
                    this.destinationPrimaryKey!.push(item);
            }
            this.jobFrequency = _data["jobFrequency"];
            if (Array.isArray(_data["mappedColumns"])) {
                this.mappedColumns = [] as any;
                for (let item of _data["mappedColumns"])
                    this.mappedColumns!.push(item);
            }
            this.settings = _data["settings"];
        }
    }

    static fromJS(data: any): CreateConnectionIntegrationDto {
        data = typeof data === 'object' ? data : {};
        let result = new CreateConnectionIntegrationDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["sourceConnectionGuid"] = this.sourceConnectionGuid;
        data["sourceConnectionName"] = this.sourceConnectionName;
        data["sourceDatabase"] = this.sourceDatabase;
        if (Array.isArray(this.sourceTable)) {
            data["sourceTable"] = [];
            for (let item of this.sourceTable)
                data["sourceTable"].push(item);
        }
        data["destinationConnectionGuid"] = this.destinationConnectionGuid;
        data["destinationConnectionName"] = this.destinationConnectionName;
        data["destinationDatabase"] = this.destinationDatabase;
        if (Array.isArray(this.destinationTable)) {
            data["destinationTable"] = [];
            for (let item of this.destinationTable)
                data["destinationTable"].push(item);
        }
        if (Array.isArray(this.sourcePrimaryKey)) {
            data["sourcePrimaryKey"] = [];
            for (let item of this.sourcePrimaryKey)
                data["sourcePrimaryKey"].push(item);
        }
        if (Array.isArray(this.destinationPrimaryKey)) {
            data["destinationPrimaryKey"] = [];
            for (let item of this.destinationPrimaryKey)
                data["destinationPrimaryKey"].push(item);
        }
        data["jobFrequency"] = this.jobFrequency;
        if (Array.isArray(this.mappedColumns)) {
            data["mappedColumns"] = [];
            for (let item of this.mappedColumns)
                data["mappedColumns"].push(item);
        }
        data["settings"] = this.settings;
        return data;
    }
}

export interface ICreateConnectionIntegrationDto {
    sourceConnectionGuid: string;
    sourceConnectionName: string | undefined;
    sourceDatabase: string | undefined;
    sourceTable: string[] | undefined;
    destinationConnectionGuid: string;
    destinationConnectionName: string | undefined;
    destinationDatabase: string | undefined;
    destinationTable: string[] | undefined;
    sourcePrimaryKey: string[] | undefined;
    destinationPrimaryKey: string[] | undefined;
    jobFrequency: string | undefined;
    mappedColumns: string[] | undefined;
    settings: string | undefined;
}

export class CreateViewDto implements ICreateViewDto {
    connectionGuid!: string;
    databaseName!: string | undefined;
    viewName!: string | undefined;
    tableName!: string | undefined;
    mappedColumns!: MappedColumn[] | undefined;

    constructor(data?: ICreateViewDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.connectionGuid = _data["connectionGuid"];
            this.databaseName = _data["databaseName"];
            this.viewName = _data["viewName"];
            this.tableName = _data["tableName"];
            if (Array.isArray(_data["mappedColumns"])) {
                this.mappedColumns = [] as any;
                for (let item of _data["mappedColumns"])
                    this.mappedColumns!.push(MappedColumn.fromJS(item));
            }
        }
    }

    static fromJS(data: any): CreateViewDto {
        data = typeof data === 'object' ? data : {};
        let result = new CreateViewDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["connectionGuid"] = this.connectionGuid;
        data["databaseName"] = this.databaseName;
        data["viewName"] = this.viewName;
        data["tableName"] = this.tableName;
        if (Array.isArray(this.mappedColumns)) {
            data["mappedColumns"] = [];
            for (let item of this.mappedColumns)
                data["mappedColumns"].push(item.toJSON());
        }
        return data;
    }
}

export interface ICreateViewDto {
    connectionGuid: string;
    databaseName: string | undefined;
    viewName: string | undefined;
    tableName: string | undefined;
    mappedColumns: MappedColumn[] | undefined;
}

export class DateOnly implements IDateOnly {
    year!: number;
    month!: number;
    day!: number;
    dayOfWeek!: DayOfWeek;
    readonly dayOfYear!: number;
    readonly dayNumber!: number;

    constructor(data?: IDateOnly) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.year = _data["year"];
            this.month = _data["month"];
            this.day = _data["day"];
            this.dayOfWeek = _data["dayOfWeek"];
            (<any>this).dayOfYear = _data["dayOfYear"];
            (<any>this).dayNumber = _data["dayNumber"];
        }
    }

    static fromJS(data: any): DateOnly {
        data = typeof data === 'object' ? data : {};
        let result = new DateOnly();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["year"] = this.year;
        data["month"] = this.month;
        data["day"] = this.day;
        data["dayOfWeek"] = this.dayOfWeek;
        data["dayOfYear"] = this.dayOfYear;
        data["dayNumber"] = this.dayNumber;
        return data;
    }
}

export interface IDateOnly {
    year: number;
    month: number;
    day: number;
    dayOfWeek: DayOfWeek;
    dayOfYear: number;
    dayNumber: number;
}

export enum DayOfWeek {
    _0 = 0,
    _1 = 1,
    _2 = 2,
    _3 = 3,
    _4 = 4,
    _5 = 5,
    _6 = 6,
}

export class DbConnectionDto implements IDbConnectionDto {
    guid!: string;
    connectionName!: string | undefined;
    description!: string | undefined;
    type!: string | undefined;
    connectionCredJson!: string | undefined;

    constructor(data?: IDbConnectionDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.guid = _data["guid"];
            this.connectionName = _data["connectionName"];
            this.description = _data["description"];
            this.type = _data["type"];
            this.connectionCredJson = _data["connectionCredJson"];
        }
    }

    static fromJS(data: any): DbConnectionDto {
        data = typeof data === 'object' ? data : {};
        let result = new DbConnectionDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["guid"] = this.guid;
        data["connectionName"] = this.connectionName;
        data["description"] = this.description;
        data["type"] = this.type;
        data["connectionCredJson"] = this.connectionCredJson;
        return data;
    }
}

export interface IDbConnectionDto {
    guid: string;
    connectionName: string | undefined;
    description: string | undefined;
    type: string | undefined;
    connectionCredJson: string | undefined;
}

export class FilterCondition implements IFilterCondition {
    field!: string | undefined;
    operator!: string | undefined;
    value!: string | undefined;

    constructor(data?: IFilterCondition) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.field = _data["field"];
            this.operator = _data["operator"];
            this.value = _data["value"];
        }
    }

    static fromJS(data: any): FilterCondition {
        data = typeof data === 'object' ? data : {};
        let result = new FilterCondition();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["field"] = this.field;
        data["operator"] = this.operator;
        data["value"] = this.value;
        return data;
    }
}

export interface IFilterCondition {
    field: string | undefined;
    operator: string | undefined;
    value: string | undefined;
}

export class IsViewDto implements IIsViewDto {
    connectionGuid!: string;
    databaseName!: string | undefined;
    viewName!: string | undefined;

    constructor(data?: IIsViewDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.connectionGuid = _data["connectionGuid"];
            this.databaseName = _data["databaseName"];
            this.viewName = _data["viewName"];
        }
    }

    static fromJS(data: any): IsViewDto {
        data = typeof data === 'object' ? data : {};
        let result = new IsViewDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["connectionGuid"] = this.connectionGuid;
        data["databaseName"] = this.databaseName;
        data["viewName"] = this.viewName;
        return data;
    }
}

export interface IIsViewDto {
    connectionGuid: string;
    databaseName: string | undefined;
    viewName: string | undefined;
}

export class JobLogEntry implements IJobLogEntry {
    processId!: string | undefined;
    startTime!: DateTime;
    endTime!: DateTime;
    recordCount!: number;
    status!: string | undefined;

    constructor(data?: IJobLogEntry) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.processId = _data["processId"];
            this.startTime = _data["startTime"] ? DateTime.fromISO(_data["startTime"].toString()) : <any>undefined;
            this.endTime = _data["endTime"] ? DateTime.fromISO(_data["endTime"].toString()) : <any>undefined;
            this.recordCount = _data["recordCount"];
            this.status = _data["status"];
        }
    }

    static fromJS(data: any): JobLogEntry {
        data = typeof data === 'object' ? data : {};
        let result = new JobLogEntry();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["processId"] = this.processId;
        data["startTime"] = this.startTime ? this.startTime.toString() : <any>undefined;
        data["endTime"] = this.endTime ? this.endTime.toString() : <any>undefined;
        data["recordCount"] = this.recordCount;
        data["status"] = this.status;
        return data;
    }
}

export interface IJobLogEntry {
    processId: string | undefined;
    startTime: DateTime;
    endTime: DateTime;
    recordCount: number;
    status: string | undefined;
}

export class JobRequest implements IJobRequest {
    frequency!: string | undefined;
    reqeustGuid!: string;

    constructor(data?: IJobRequest) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.frequency = _data["frequency"];
            this.reqeustGuid = _data["reqeustGuid"];
        }
    }

    static fromJS(data: any): JobRequest {
        data = typeof data === 'object' ? data : {};
        let result = new JobRequest();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["frequency"] = this.frequency;
        data["reqeustGuid"] = this.reqeustGuid;
        return data;
    }
}

export interface IJobRequest {
    frequency: string | undefined;
    reqeustGuid: string;
}

export class MapColumns implements IMapColumns {
    sourceColumns!: string[] | undefined;
    destinationColumns!: string[] | undefined;

    constructor(data?: IMapColumns) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["sourceColumns"])) {
                this.sourceColumns = [] as any;
                for (let item of _data["sourceColumns"])
                    this.sourceColumns!.push(item);
            }
            if (Array.isArray(_data["destinationColumns"])) {
                this.destinationColumns = [] as any;
                for (let item of _data["destinationColumns"])
                    this.destinationColumns!.push(item);
            }
        }
    }

    static fromJS(data: any): MapColumns {
        data = typeof data === 'object' ? data : {};
        let result = new MapColumns();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.sourceColumns)) {
            data["sourceColumns"] = [];
            for (let item of this.sourceColumns)
                data["sourceColumns"].push(item);
        }
        if (Array.isArray(this.destinationColumns)) {
            data["destinationColumns"] = [];
            for (let item of this.destinationColumns)
                data["destinationColumns"].push(item);
        }
        return data;
    }
}

export interface IMapColumns {
    sourceColumns: string[] | undefined;
    destinationColumns: string[] | undefined;
}

export class MappedColumn implements IMappedColumn {
    source!: string | undefined;
    destination!: string | undefined;

    constructor(data?: IMappedColumn) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.source = _data["source"];
            this.destination = _data["destination"];
        }
    }

    static fromJS(data: any): MappedColumn {
        data = typeof data === 'object' ? data : {};
        let result = new MappedColumn();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["source"] = this.source;
        data["destination"] = this.destination;
        return data;
    }
}

export interface IMappedColumn {
    source: string | undefined;
    destination: string | undefined;
}

export class RequestPayload implements IRequestPayload {
    connectionId!: string | undefined;
    company!: string | undefined;
    action!: string | undefined;
    entity!: string | undefined;
    filter!: FilterCondition[] | undefined;
    data!: any | undefined;

    constructor(data?: IRequestPayload) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.connectionId = _data["connectionId"];
            this.company = _data["company"];
            this.action = _data["action"];
            this.entity = _data["entity"];
            if (Array.isArray(_data["filter"])) {
                this.filter = [] as any;
                for (let item of _data["filter"])
                    this.filter!.push(FilterCondition.fromJS(item));
            }
            this.data = _data["data"];
        }
    }

    static fromJS(data: any): RequestPayload {
        data = typeof data === 'object' ? data : {};
        let result = new RequestPayload();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["connectionId"] = this.connectionId;
        data["company"] = this.company;
        data["action"] = this.action;
        data["entity"] = this.entity;
        if (Array.isArray(this.filter)) {
            data["filter"] = [];
            for (let item of this.filter)
                data["filter"].push(item.toJSON());
        }
        data["data"] = this.data;
        return data;
    }
}

export interface IRequestPayload {
    connectionId: string | undefined;
    company: string | undefined;
    action: string | undefined;
    entity: string | undefined;
    filter: FilterCondition[] | undefined;
    data: any | undefined;
}

export class ResponseMessage implements IResponseMessage {
    isError!: boolean;
    message!: string | undefined;

    constructor(data?: IResponseMessage) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.isError = _data["isError"];
            this.message = _data["message"];
        }
    }

    static fromJS(data: any): ResponseMessage {
        data = typeof data === 'object' ? data : {};
        let result = new ResponseMessage();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        data["message"] = this.message;
        return data;
    }
}

export interface IResponseMessage {
    isError: boolean;
    message: string | undefined;
}

export class ResponseMessageList implements IResponseMessageList {
    isError!: boolean;
    message!: string[] | undefined;

    constructor(data?: IResponseMessageList) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.isError = _data["isError"];
            if (Array.isArray(_data["message"])) {
                this.message = [] as any;
                for (let item of _data["message"])
                    this.message!.push(item);
            }
        }
    }

    static fromJS(data: any): ResponseMessageList {
        data = typeof data === 'object' ? data : {};
        let result = new ResponseMessageList();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["isError"] = this.isError;
        if (Array.isArray(this.message)) {
            data["message"] = [];
            for (let item of this.message)
                data["message"].push(item);
        }
        return data;
    }
}

export interface IResponseMessageList {
    isError: boolean;
    message: string[] | undefined;
}

export class ServiceField implements IServiceField {
    tableName!: string | undefined;
    fieldNames!: string[] | undefined;

    constructor(data?: IServiceField) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.tableName = _data["tableName"];
            if (Array.isArray(_data["fieldNames"])) {
                this.fieldNames = [] as any;
                for (let item of _data["fieldNames"])
                    this.fieldNames!.push(item);
            }
        }
    }

    static fromJS(data: any): ServiceField {
        data = typeof data === 'object' ? data : {};
        let result = new ServiceField();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["tableName"] = this.tableName;
        if (Array.isArray(this.fieldNames)) {
            data["fieldNames"] = [];
            for (let item of this.fieldNames)
                data["fieldNames"].push(item);
        }
        return data;
    }
}

export interface IServiceField {
    tableName: string | undefined;
    fieldNames: string[] | undefined;
}

export class SubscriptionRequest implements ISubscriptionRequest {
    soruceConnectionId!: string | undefined;
    integrationId!: string | undefined;
    resource!: string | undefined;
    clientState!: string | undefined;

    constructor(data?: ISubscriptionRequest) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.soruceConnectionId = _data["soruceConnectionId"];
            this.integrationId = _data["integrationId"];
            this.resource = _data["resource"];
            this.clientState = _data["clientState"];
        }
    }

    static fromJS(data: any): SubscriptionRequest {
        data = typeof data === 'object' ? data : {};
        let result = new SubscriptionRequest();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["soruceConnectionId"] = this.soruceConnectionId;
        data["integrationId"] = this.integrationId;
        data["resource"] = this.resource;
        data["clientState"] = this.clientState;
        return data;
    }
}

export interface ISubscriptionRequest {
    soruceConnectionId: string | undefined;
    integrationId: string | undefined;
    resource: string | undefined;
    clientState: string | undefined;
}

export class WeatherForecast implements IWeatherForecast {
    date!: DateOnly;
    temperatureC!: number;
    readonly temperatureF!: number;
    summary!: string | undefined;

    constructor(data?: IWeatherForecast) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.date = _data["date"] ? DateOnly.fromJS(_data["date"]) : <any>undefined;
            this.temperatureC = _data["temperatureC"];
            (<any>this).temperatureF = _data["temperatureF"];
            this.summary = _data["summary"];
        }
    }

    static fromJS(data: any): WeatherForecast {
        data = typeof data === 'object' ? data : {};
        let result = new WeatherForecast();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["date"] = this.date ? this.date.toJSON() : <any>undefined;
        data["temperatureC"] = this.temperatureC;
        data["temperatureF"] = this.temperatureF;
        data["summary"] = this.summary;
        return data;
    }
}

export interface IWeatherForecast {
    date: DateOnly;
    temperatureC: number;
    temperatureF: number;
    summary: string | undefined;
}

export class WebhookNotification implements IWebhookNotification {
    subscriptionId!: string | undefined;
    clientState!: string | undefined;
    expirationDateTime!: DateTime;
    resource!: string | undefined;
    changeType!: string | undefined;
    lastModifiedDateTime!: DateTime;

    constructor(data?: IWebhookNotification) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.subscriptionId = _data["subscriptionId"];
            this.clientState = _data["clientState"];
            this.expirationDateTime = _data["expirationDateTime"] ? DateTime.fromISO(_data["expirationDateTime"].toString()) : <any>undefined;
            this.resource = _data["resource"];
            this.changeType = _data["changeType"];
            this.lastModifiedDateTime = _data["lastModifiedDateTime"] ? DateTime.fromISO(_data["lastModifiedDateTime"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): WebhookNotification {
        data = typeof data === 'object' ? data : {};
        let result = new WebhookNotification();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["subscriptionId"] = this.subscriptionId;
        data["clientState"] = this.clientState;
        data["expirationDateTime"] = this.expirationDateTime ? this.expirationDateTime.toString() : <any>undefined;
        data["resource"] = this.resource;
        data["changeType"] = this.changeType;
        data["lastModifiedDateTime"] = this.lastModifiedDateTime ? this.lastModifiedDateTime.toString() : <any>undefined;
        return data;
    }
}

export interface IWebhookNotification {
    subscriptionId: string | undefined;
    clientState: string | undefined;
    expirationDateTime: DateTime;
    resource: string | undefined;
    changeType: string | undefined;
    lastModifiedDateTime: DateTime;
}

export class WebhookRequest implements IWebhookRequest {
    value!: WebhookNotification[] | undefined;

    constructor(data?: IWebhookRequest) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            if (Array.isArray(_data["value"])) {
                this.value = [] as any;
                for (let item of _data["value"])
                    this.value!.push(WebhookNotification.fromJS(item));
            }
        }
    }

    static fromJS(data: any): WebhookRequest {
        data = typeof data === 'object' ? data : {};
        let result = new WebhookRequest();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        if (Array.isArray(this.value)) {
            data["value"] = [];
            for (let item of this.value)
                data["value"].push(item.toJSON());
        }
        return data;
    }
}

export interface IWebhookRequest {
    value: WebhookNotification[] | undefined;
}

export interface FileParameter {
    data: any;
    fileName: string;
}

export class ApiException extends Error {
    override message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}