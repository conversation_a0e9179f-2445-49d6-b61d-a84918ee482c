.app-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.hamburger {
  display: block;
  position: fixed;
  top: 18px;
  left: 20px;
  /* font-size: 24px; */
  cursor: pointer;
  font-size: 20px;
  z-index: 9999;
}

.sidebar {
  width: 210px;
  height: calc(100vh - 58px);
  position: fixed;
  top: 56px;
  left: 0;
  padding-top: 5px;
  border-right: 1px solid rgb(215, 219, 236);
  background-color: rgb(245, 246, 250);
  z-index: 1;
  overflow-y: auto;
  transition: transform 0.3s ease-in-out;
  transform: translateX(-100%);
  /* Sidebar is hidden by default */
}

.sidebar.open {
  transform: translateX(0);
  /* Sidebar is visible when 'open' */
}

.sidebar.closed {
  transform: translateX(-100%);
  /* Sidebar is hidden when 'closed' */
}

/* For main content, adjust margin depending on the sidebar state */
.main-content {
  padding: 20px;
  flex-grow: 1;
  background-color: #f4f5f7;
  min-height: calc(100vh - 58.21px);
  position: relative;
  overflow-y: auto;
  transition: margin-left 0.3s;
}

/* When sidebar is open, adjust the margin for desktop view */
.main-content.sidebar-open {
  margin-left: 210px;
}

nav ul {
  list-style: none;
  padding: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  color: var(--bg-color);
  cursor: pointer;
  flex-grow: 1;
  margin: 8px 0;
  padding: 12px 15px;
  border-radius: 4px;
  font-size: 1.05rem;
  line-height: 1.4;
  font-weight: 400;
  transition: all 0.7s;
  position: relative;
  gap: 30px;
}

.nav-item:hover {
  background: #ffffff;
  cursor: pointer;
  color: #00688c;

}

.nav-item:disabled {
  background-color: var(--active-color);
  color: rgb(30, 94, 255);
}

.nav-item.active {
  /* background-color: var(--bg-color);
  color: white; */
  background-color: #c9ebf6;
  color: black;
  text-decoration: none;
  outline: 0;
}

.nav-item i {
  font-size: 1.5rem;
  margin-right: 10px;
}

.nav-item span {
  flex-grow: 1;
}

.nav-section-title {
  z-index: 1;
  font-size: 0.75rem;
  letter-spacing: 0rem;
  font-weight: 700;
  line-height: 1.333;
  margin-top: 24px;
  margin-bottom: 0px;
  color: rgb(161, 167, 196);
  background-color: transparent;
  padding: 3.2px 12px;
}

.nav-item .badge {
  background-color: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 200px;
    height: calc(100vh - 60px);
    position: fixed;
    top: 60px;
    left: 0;
    padding: 20px;
    z-index: 9998;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    /* No margin shift for smaller screens */
  }
}

.nav-item.disabled {
  pointer-events: none;  /* Prevent clicking */
  opacity: 0.4;  /* Grayscale effect */
  color: #888;  /* Lighter text color */
}
