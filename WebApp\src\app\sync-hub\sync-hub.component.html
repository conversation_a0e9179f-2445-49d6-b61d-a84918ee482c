<div class="app-container">
  <div class="hamburger" (click)="toggleSidebar()">
    <i class="fas fa-bars"></i>
  </div>

  <div class="sidebar" [ngClass]="{'open': isSidebarOpen, 'closed': !isSidebarOpen}" *ngIf="isSidebarOpen">
    <nav>
      <ul>
        <li class="nav-item " routerLink="./connection" [ngClass]="isActive('connection') ? 'active':''" style="font-size: 1.1rem;">
          <!-- <i class="fa-solid fa-house"></i> -->
          <!-- <i class="bi bi-cast"></i> -->
          <span>Connections</span>
        </li>
        <li class="nav-item " routerLink="./connection-integration" [ngClass]="isActive('sql-sql') ? 'active':''">
          <!-- <i class="fa-solid fa-house"></i> -->
          <!-- <i class="bi bi-kanban"></i> -->
          <span>SQL <i class="fa fa-arrows-h" style="font-size:16px; padding-left: 10px;"></i> SQL</span>
        </li>
        <li class="nav-item " routerLink="./bc-sql-list" [ngClass]="isActive('bc-sql') ? 'active':''">
          <!-- <i class="fa-solid fa-house"></i> -->
          <!-- <i class="bi bi-kanban"></i> -->
          <span>NAV/BC <i class="fa fa-arrows-h" style="font-size:16px; padding-left: 10px;"></i> SQL</span>
        </li>
        <!-- <li class="nav-item" routerLink="./bc-sql-list" routerLinkActive="active">
          <span>SQL List</span>
        </li> -->
        <li class="nav-item" [class.disabled]="true" (click)="$event.preventDefault()">
          <span>CSV <i class="fa fa-arrows-h" style="font-size:16px; padding-left: 10px;"></i> SQL</span>
        </li>
        <li class="nav-item" [class.disabled]="true" (click)="$event.preventDefault()">
          <span>Excel <i class="fa fa-arrows-h" style="font-size:16px; padding-left: 10px;"></i> SQL</span>
        </li>
        <li class="nav-item" [class.disabled]="true" (click)="$event.preventDefault()">
          <span>SharePoint <i class="fa fa-arrows-h" style="font-size:16px; padding-left: 10px;"></i> SQL</span>
        </li>


      </ul>
    </nav>
  </div>
  <div class="main-content" [class.sidebar-open]="isSidebarOpen">
    <router-outlet></router-outlet>
  </div>
</div>
