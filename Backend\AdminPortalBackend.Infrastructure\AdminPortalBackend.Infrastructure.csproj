﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Hangfire" Version="1.8.14" />
    <PackageReference Include="Hangfire.MaximumConcurrentExecutions" Version="1.1.0" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.14" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.66.1" />
    <PackageReference Include="Microsoft.KernelMemory" Version="0.92.241112.1" />
    <PackageReference Include="Microsoft.KernelMemory.Core" Version="0.92.241112.1" />
    <PackageReference Include="Microsoft.OData.Client" Version="8.1.0" />
    <PackageReference Include="Microsoft.OData.Core" Version="8.1.0" />
    <PackageReference Include="Microsoft.OData.Edm" Version="8.1.0" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.32.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.Core" Version="1.29.0-alpha" />
    <PackageReference Include="Microsoft.Spatial" Version="8.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AdminPortalBackend.Core\AdminPortalBackend.Core.csproj" />
  </ItemGroup>

</Project>
