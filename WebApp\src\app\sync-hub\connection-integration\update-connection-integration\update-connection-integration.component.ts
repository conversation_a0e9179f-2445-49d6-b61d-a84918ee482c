import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit, OnDestroy } from '@angular/core';
import { DbConnectionServiceProxy, DbConnectionDto, ConnectionIntegrationServiceProxy, ConnectionIntegration, JobRequest } from '../../../../shared/service-proxies/service-proxies';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { StepsModule } from 'primeng/steps';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { StepperModule } from 'primeng/stepper';
import { MultiSelectModule } from 'primeng/multiselect';
import { TableModule } from 'primeng/table';
import { FloatLabelModule } from 'primeng/floatlabel';
import { Location } from '@angular/common';

import { ToastModule } from 'primeng/toast';
import { ToasterService } from '../../../toaster.service';
import { SignalRService } from '../../../signalr.service';
import { MatChipsModule } from '@angular/material/chips';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SubSink } from 'subsink';

interface ColumnMapping {
  source: string;
  destination: string;
}

interface IIntegrationSettings {
  BcToSql: string;
}

@Component({
  selector: 'app-update-connection-integration',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    StepsModule,
    DropdownModule,
    ButtonModule,
    InputTextModule,
    MultiSelectModule,
    TableModule,
    StepperModule,
    FloatLabelModule,
    ToastModule,
    MatChipsModule,
    RadioButtonModule
  ],
  templateUrl: './update-connection-integration.component.html',
  styleUrls: ['./update-connection-integration.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class UpdateConnectionIntegrationComponent implements OnInit, OnDestroy {
  private subs = new SubSink();

  integrationSettings: IIntegrationSettings = {
    BcToSql: null,

  };
  connectionData: ConnectionIntegration = new ConnectionIntegration();
  connections: DbConnectionDto[] = [];
  sourceConnectionName: string = '';
  destinationConnectionName: string = '';
  isSaving = false

  isExecuting = false
  isDropdownOpen: boolean = false;

  singlarMessage = [];
  isExecutingOnly = false

  // Source Variables
  sourceConnection: DbConnectionDto;
  sourceDatase: string = '';
  sourceTable: string = '';
  sourceColumns: string[] = [];
  SourceGuid: string | null = null;

  // Destination Variables
  destinationConnection: DbConnectionDto;
  destinationDatase: string = '';
  destinationTable: string = '';
  destinationColumns: string[] = [];
  destinationGuid: string | null = null;

  jobTypes: string[] = ['On Demand', 'Hourly', 'Daily', 'Weekly'];
  databases: string[] = [];
  destinationDatabases: string[] = [];
  tables: string[] = [];
  destinationTables: string[] = [];
  columnMappings: Array<{ source: string, destination: string }> = [{ source: '', destination: '' }];
  guid: string | null = null;

  sourcePrimaryKey: string[] = [];
  destinationPrimaryKey: string[] = [];
  selectedSourcePrimaryKey: string[] = [];
  previousSourcePrimarySelection: string[] = [];
  selectedDestinationPrimaryKey: string[] = [];
  previousDestPrimarySelection: string[] = [];

  constructor(
    private _dbconnectionServices: DbConnectionServiceProxy,
    private _connectionIntegrationService: ConnectionIntegrationServiceProxy,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private toasterService: ToasterService,
    private singlarService: SignalRService
  ) { }

  async ngOnInit() {
    this.guid = this.route.snapshot.paramMap.get('guid');
    await this.loadConnection();
    if (this.guid) {
      this.loadIntegrationData(this.guid);
    }
    // this.integrationSettings.BcToSql = JSON.parse(this.connectionData.settings).BcToSql

    //console.log(this.connectionData);

    this.subs.add(
      this.singlarService.message$.subscribe(res => {
        if (res.type == 'SendMessage') {
          var messagePart = res.message.split('~');
          if (messagePart.length == 2 && messagePart[0] == this.guid) {
            const currentDate = new Date();
            const hours = String(currentDate.getHours()).padStart(2, '0');
            const minutes = String(currentDate.getMinutes()).padStart(2, '0');
            const seconds = String(currentDate.getSeconds()).padStart(2, '0');
            const time = `${hours}:${minutes}:${seconds}`;
            this.singlarMessage.unshift(`[${time}]  ${messagePart[1]}`);
          }
        }
      })
    );
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  async executeJob(guid: string) {
    this.isExecutingOnly = true;
    try {
      let res = await this._connectionIntegrationService.executeJob(guid).toPromise();
      //console.log(res);
      if (!res.isError) {
        this.singlarMessage = [];
        this.isExecutingOnly = false;
      } else {
        this.toasterService.showToaster('error', res.message);
        return false;
      }
      return true;
    } catch (error) {
      //console.log(error);

      this.toasterService.showToaster('error', error.message);
      return false;
    } finally {
      this.isExecutingOnly = false;
    }
  }

  loadIntegrationData(guid: string): void {
    this._connectionIntegrationService.getSingle(guid).subscribe(
      (res) => {
        //console.log(res)
        this.connectionData = res;
        this.populateIntegrationData();
        this.loadDatabases();
        this.onSourceDatabaseSelected(this.sourceDatase)
        this.onDestinationDatabaseSelected(this.destinationDatase)
        this.loadSourceColumns();
        this.loadDestinationColumns();
        this.selectedSourcePrimaryKey = [...res.sourcePrimaryKey.split("@#")];
        this.sourcePrimaryKey = [...this.selectedSourcePrimaryKey]
        this.previousSourcePrimarySelection = [...this.selectedSourcePrimaryKey]
        this.selectedDestinationPrimaryKey = [...res.destinationPrimaryKey.split("@#")];
        this.destinationPrimaryKey = [...this.selectedDestinationPrimaryKey]
        this.previousDestPrimarySelection = [...this.selectedDestinationPrimaryKey]
        this.integrationSettings.BcToSql = JSON.parse(this.connectionData.settings).BcToSql

        //console.log(this.selectedSourcePrimaryKey);
        //console.log(this.sourcePrimaryKey);
        //console.log(this.previousSourcePrimarySelection);
      },
      (error) => {
        //console.error('Error fetching integration data:', error);
      }
    );
  }

  populateIntegrationData(): void {
    this.sourceConnectionName = this.connectionData.sourceConnectionName;
    this.SourceGuid = this.connectionData.sourceConnectionGuid;
    this.sourceConnection = this.connections.find(x => x.guid == this.SourceGuid);
    this.sourceDatase = this.connectionData.sourceDatabase;
    this.sourceTable = this.connectionData.sourceTable;
    //console.log(this.sourceTable);

    this.destinationConnectionName = this.connectionData.destinationConnectionName;
    this.destinationGuid = this.connectionData.destinationConnectionGuid;
    this.destinationConnection = this.connections.find(x => x.guid == this.destinationGuid);
    this.destinationDatase = this.connectionData.destinationDatabase;
    this.destinationTable = this.connectionData.destinationTable;

    const mappedColumns = JSON.parse(this.connectionData.mappedColumns);
    //console.log(mappedColumns);

    this.columnMappings = Object.keys(mappedColumns).map(sourceColumn => ({
      source: sourceColumn,
      destination: mappedColumns[sourceColumn]
    }));

    this.columnMappings.forEach((column, count) => {
      this.onSourceChange(column.source, count)
      this.onDestinationChange(column.destination, count)
      count++;
    })

  }

  loadDatabases(): void {
    if (this.SourceGuid) {
      this._connectionIntegrationService.getDatabase(this.SourceGuid).subscribe(
        (response) => {
          this.databases = response.message;
        },
        (error) => {
          //console.error('Error fetching source databases:', error);
        }
      );
    }
    if (this.destinationGuid) {
      this._connectionIntegrationService.getDatabase(this.destinationGuid).subscribe(
        (response) => {
          this.destinationDatabases = response.message;
        },
        (error) => {
          //console.error('Error fetching destination databases:', error);
        }
      );
    }
  }

  loadSourceColumns(): void {

    if (this.sourceDatase && this.sourceTable) {
      this._connectionIntegrationService.getColumn(this.SourceGuid, this.sourceDatase, this.sourceTable).subscribe(
        (res) => {
          this.sourceColumns = res.message;
        },
        (error) => {
          //console.error('Error fetching source columns:', error);
        }
      );
    }
  }

  loadDestinationColumns(): void {
    //console.log(this.destinationDatase);
    //console.log(this.destinationTable);
    //console.log(this.destinationGuid);


    if (this.destinationDatase && this.destinationTable) {
      this._connectionIntegrationService.getColumn(this.destinationGuid, this.destinationDatase, this.destinationTable).subscribe(
        (res) => {
          this.destinationColumns = res.message;

        },
        (error) => {
          //console.error('Error fetching destination columns:', error);
        }
      );
    }
  }

  async loadConnection() {
    this.connections = await this._dbconnectionServices.getAll().toPromise();
  }
  // navigate to previous routes function
  goBack() {
    this.location.back()
  }
  onSourceConnectionSelected(selectedConnection: DbConnectionDto): void {
    //console.log(selectedConnection);

    if (selectedConnection && selectedConnection.guid) {
      this.sourceConnectionName = selectedConnection.connectionName;
      this.SourceGuid = selectedConnection.guid;
      this.loadDatabases();
    } else {
      //console.error('No valid connection selected for source.');
    }
  }

  onSourceDatabaseSelected(selectedDatabase: string): void {
    this.sourceDatase = selectedDatabase;
    this.sourceColumns = [];
    if (selectedDatabase && this.SourceGuid) {
      this._connectionIntegrationService.getTables(this.SourceGuid, selectedDatabase).subscribe(
        (res) => {
          this.tables = res.message;
        },
        (error) => {
          //console.error('Error fetching source tables:', error);
        }
      );
    }
  }

  onSourceTableSelected(selectedTable: string): void {
    this.sourceTable = selectedTable;
    this.sourceColumns = [];
    this.loadSourceColumns();
  }

  onDestinationConnectionSelected(selectedConnection: DbConnectionDto): void {
    if (selectedConnection && selectedConnection.guid) {
      this.destinationConnectionName = selectedConnection.connectionName;
      this.destinationGuid = selectedConnection.guid;
      this.loadDatabases();
    } else {
      //console.error('No valid connection selected for destination.');
    }
  }

  onDestinationDatabaseSelected(selectedDatabase: string): void {
    this.destinationDatase = selectedDatabase;
    this.destinationColumns = [];
    if (selectedDatabase && this.destinationGuid) {
      this._connectionIntegrationService.getTables(this.destinationGuid, selectedDatabase).subscribe(
        (res) => {
          this.destinationTables = res.message || [];
        },
        (error) => {
          //console.error('Error fetching destination tables:', error);
        }
      );
    }
  }

  onDestinationTableSelected(selectedTable: string): void {
    this.destinationTable = selectedTable;
    this.destinationColumns = [];
    this.loadDestinationColumns();
  }

  addMapping(): void {
    this.columnMappings.push({ source: '', destination: '' });
  }

  removeMapping(index: number) {
    // Remove the destination column from the selectedDestinations list if it exists
    const removedDestination = this.columnMappings[index].destination;
    this.selectedDestinations = this.selectedDestinations.filter(col => col !== removedDestination);
    const removedSource = this.columnMappings[index].source;
    this.selectedSources = this.selectedSources.filter(col => col !== removedSource);

    // Now remove the mapping from the columnMappings array
    this.columnMappings.splice(index, 1);
  }
  selectedSources: string[] = []
  selectedDestinations: string[] = []
  private previousColumn: string | null = null;

  columnFocus(currentValue: string) {
    this.previousColumn = currentValue; // Store the current value before change
  }
  filteredSourceColumns(index: number) {
    return this.sourceColumns.filter(
      column =>
        !this.selectedSources.includes(column) ||
        this.columnMappings[index]?.source === column
    );

  }


  onSourceChange(newColumn: string, index: number) {
    //console.log(newColumn, index);

    // //console.log(this.filteredSourceColumns(index));

    // Remove the previous column from selectedSources if it exists
    if (this.previousColumn) {
      this.selectedSources = this.selectedSources.filter(
        col => col !== this.previousColumn
      );
      // //console.log(this.selectedSources);
    }

    // Add the new column to the list
    if (newColumn && !this.selectedSources.includes(newColumn)) {
      this.selectedSources.push(newColumn);
    }

    // Update the mapping
    this.columnMappings[index].source = newColumn;

    // //console.log("Updated Selected Sources:", this.selectedSources);
    // //console.log("Current Mapping:", this.columnMappings);
  }


  filteredDestinationColumns(index: number) {
    // Allow columns that are not selected or the one currently selected for this row
    return this.destinationColumns.filter(
      column =>
        !this.selectedDestinations.includes(column) ||
        this.columnMappings[index]?.destination === column
    );
  }

  onDestinationChange(newColumn: string, index: number) {
    // Remove the previous column from selectedDestinations if it exists
    if (this.previousColumn) {
      this.selectedDestinations = this.selectedDestinations.filter(
        col => col !== this.previousColumn
      );
    }

    // Add the new column to the list
    if (newColumn && !this.selectedDestinations.includes(newColumn)) {
      this.selectedDestinations.push(newColumn);
    }

    // Update the mapping
    this.columnMappings[index].destination = newColumn;

    // //console.log("Updated Selected Destinations:", this.selectedDestinations);
    // //console.log("Current Mapping:", this.columnMappings);
  }

  async save() {
    try {
      this.isSaving = true

      if (!this.connectionData.integrationName) {
        //console.error('Integration Name is required.');
        this.isSaving = false
        return;
      }

      const mappingObject = this.columnMappings.reduce((acc, item) => {
        if (item.source && item.destination) {
          acc[item.source] = item.destination;
        }
        return acc;
      }, {} as { [key: string]: string });

      if (Object.keys(mappingObject).length === 0) {
        this.isSaving = false
        //console.error('Mapped Columns are required.');
        return;
      }

      const jsonString = JSON.stringify(mappingObject);
      this.connectionData.mappedColumns = jsonString;
      this.connectionData.isActive = true;

      // Populate ConnectionIntegration model
      this.connectionData.guid = this.connectionData.guid || '00000000-0000-0000-0000-000000000000';
      this.connectionData.sourceConnectionGuid = this.SourceGuid || '';
      this.connectionData.sourceConnectionName = this.sourceConnectionName;
      this.connectionData.sourceDatabase = this.sourceDatase;
      this.connectionData.sourceTable = this.sourceTable;

      this.connectionData.destinationConnectionGuid = this.destinationGuid || '';
      this.connectionData.destinationConnectionName = this.destinationConnectionName;
      this.connectionData.destinationDatabase = this.destinationDatase;
      this.connectionData.destinationTable = this.destinationTable;
      this.connectionData.sourcePrimaryKey = this.selectedSourcePrimaryKey.join("@#");
      this.connectionData.destinationPrimaryKey = this.destinationPrimaryKey.join("@#");
      this.connectionData.settings = JSON.stringify(this.integrationSettings)

      if (this.connectionData.jobFrequency != 'On Demand') {
        var jobRequest = new JobRequest({
          frequency: this.connectionData.jobFrequency,
          reqeustGuid: this.connectionData.guid,
        });
        await this._connectionIntegrationService
          .scheduleJob(jobRequest)
          .toPromise();
      }

      let res = await this._connectionIntegrationService.edit(this.connectionData).toPromise()
      //console.log('Integration saved successfully:', res);
      //console.log(this.integrationSettings);

      this.isSaving = false
      this.toasterService.showToaster('success', "Data saved ")

    } catch (error) {
      this.isSaving = false
      this.toasterService.showToaster('error', error.message)
    }
  }

  clearMessage() {
    this.singlarMessage = []
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  async onSaveAndExecute() {
    this.isExecuting = true
    await this.save()
    await this.executeJob(this.guid)
    this.isExecuting = false

  }

  async onSave() {
    this.isSaving = true
    await this.save()
    this.isSaving = false
    this.router.navigate(['./sync-hub/connection-integration']);


  }



  addDestinationPrimaryKey(selectedTables: string[]) {
    // Replace the entire primary key selection instead of appending
    this.selectedDestinationPrimaryKey = [...selectedTables];
    this.previousDestPrimarySelection = [...selectedTables];
  }


  removeDestinationPrimaryKey(table: any) {
    const index = this.selectedDestinationPrimaryKey.indexOf(table);
    if (index >= 0) {
      this.selectedDestinationPrimaryKey.splice(index, 1);
      //console.log(`Table "${table}" removed successfully.`);
    }
  }
  addSourcePrimaryKey(selectedTables: string[]) {
    // Replace the entire primary key selection instead of appending
    this.selectedSourcePrimaryKey = [...selectedTables];
    this.previousSourcePrimarySelection = [...selectedTables];

    //console.log(this.selectedSourcePrimaryKey);
    //console.log(this.sourcePrimaryKey);
    //console.log(this.previousSourcePrimarySelection);
  }


  removeSourcePrimaryKey(table: any) {
    const index = this.selectedSourcePrimaryKey.indexOf(table);
    if (index >= 0) {
      this.selectedSourcePrimaryKey.splice(index, 1);
      //console.log(`Table "${table}" removed successfully.`);
    }
  }
}

