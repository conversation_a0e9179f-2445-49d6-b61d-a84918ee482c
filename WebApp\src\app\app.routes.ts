import { Routes } from '@angular/router';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';
import { HomeComponent } from './welcome/home.component';

export const routes: Routes = [
  // { path: '', component: WelcomeComponent },
  { path: 'home', component: HomeComponent },
  { path: 'not-found', component: PageNotFoundComponent },
  { path: 'sync-hub', loadChildren: () => import('./sync-hub/sync-hub.module').then(m => m.SyncHubModule), data: { preload: true } },

  { path: 'video', loadChildren: () => import('./video/video.module').then(m => m.VideoModule), data: { preload: true } },
  // { path: '', redirectTo: 'login', pathMatch: 'full' },
  { path: '**', redirectTo: 'sync-hub' },
  // { path: '**', redirectTo: 'not-found' },
];
