﻿using Newtonsoft.Json;

namespace AdminPortalBackend.Core.Models;

public class SubscriptionResponse
{
    [JsonProperty("@odata.etag")] 
    public string ETag { get; set; }
    public string SubscriptionId { get; set; }
    public string NotificationUrl { get; set; }
    public string Resource { get; set; }
    public string ClientState { get; set; }
    public DateTimeOffset LastModifiedDateTime { get; set; }
    public DateTimeOffset ExpirationDateTime { get; set; }
    public string ConnectionId { get; set; }
    public string IntegrationId { get; set; }
}
