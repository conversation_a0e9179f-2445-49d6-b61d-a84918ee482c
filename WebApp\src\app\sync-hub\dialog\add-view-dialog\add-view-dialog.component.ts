import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ConnectionIntegrationServiceProxy, GenAIServiceProxy, MapColumns, MappedColumn, } from '../../../../shared/service-proxies/service-proxies';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { ToasterService } from '../../../toaster.service';

@Component({
  selector: 'app-add-view-dialog',
  standalone: true,
  imports: [CommonModule, DropdownModule, FormsModule, ServiceProxyModule],
  templateUrl: './add-view-dialog.component.html',
  styleUrls: ['./add-view-dialog.component.css']
})
export class AddViewDialogComponent implements OnInit {
  constructor(public dialogRef: MatDialogRef<AddViewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any, private genAiService: GenAIServiceProxy,
    private toasterService: ToasterService,
    private integrationService: ConnectionIntegrationServiceProxy) { }
  fieldName: string[] = [];
  mapTo: string[] = [];
  mappingData: MappedColumn[] = []
  viewName: string = ''
  isMapping: boolean = false;

  async ngOnInit() {
    var res = await this.integrationService.getPropertiesForEntity(this.data.entity, this.data.guid).toPromise();
    if (res.isError == false) {
      this.fieldName = res.message;
      //console.log(this.fieldName);
    }

    this.mappingData = this.fieldName.map(field => (new MappedColumn({
      source: field,
      destination: ''
    })));

    this.integrationService.getFieldFromService(this.data.entity).subscribe(res => {
      if (res) {
        this.mapTo = res.fieldNames;
        this.viewName = res.tableName;
      }
    })
  }

  remove(index: number) {
    this.mappingData.splice(index, 1);
  }

  onSave() {
    this.dialogRef.close({ viewName: this.viewName, mappingData: this.mappingData });
  }
  async mapWithAi() {
    var mappedColumns: MapColumns = new MapColumns({
      sourceColumns: this.fieldName,
      destinationColumns: this.mapTo
    })
    this.isMapping = true;
    var res = await this.genAiService.mapColumns(mappedColumns).toPromise()
    this.mappingData = [...res]
    this.toasterService.showToaster('success', "Mapped Fields Successfully ");
    this.isMapping = false;
  }
}
