import {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HTTP_ROOT_INTERCEPTOR_FNS,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptorHandler,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  PRIMARY_HTTP_BACKEND,
  provideHttpClient,
  withFetch,
  withHttpTransferCache,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration
} from "./chunk-SXOKWTQP.js";
import "./chunk-AL5VSKTC.js";
import "./chunk-2UJK5BIY.js";
import "./chunk-JND6LT5A.js";
import "./chunk-664N5FMB.js";
import "./chunk-532FTKWE.js";
import "./chunk-X6JV76XL.js";
export {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  provideHttpClient,
  withFetch,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration,
  HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS,
  HttpInterceptorHandler as ɵHttpInterceptingHandler,
  HttpInterceptorHandler as ɵHttpInterceptorHandler,
  PRIMARY_HTTP_BACKEND as ɵPRIMARY_HTTP_BACKEND,
  withHttpTransferCache as ɵwithHttpTransferCache
};
//# sourceMappingURL=@angular_common_http.js.map
