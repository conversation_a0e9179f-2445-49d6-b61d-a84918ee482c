import { Component, Inject } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule, NgForm } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS, MatFormFieldModule } from '@angular/material/form-field'; import { DbConnectionDto, DbConnectionServiceProxy, TestConnectionServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';



import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { ToasterService } from '../../../toaster.service';

@Component({
  selector: 'app-o-data-connection-dialog',
  standalone: true,
  imports: [MatFormFieldModule, MatInputModule, FormsModule, MatButtonModule, ServiceProxyModule, MatSelectModule, CommonModule, ToastModule, ButtonModule, RippleModule],
  providers: [
    {
      provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
      useValue: {
        subscriptSizing: 'dynamic'
      }
    }, MessageService
  ],
  templateUrl: './o-data-connection-dialog.component.html',
  styleUrl: './o-data-connection-dialog.component.css'
})
export class ODataConnectionDialogComponent {
  odata: any = {
    connectionName: '',
    type: '',
    description: '',
    baseUrl: '',
    endpointUrl: '',
    environmentName: '',
    tokenEndpoint: '',
    clientId: '',
    clientSecret: '',
    scope: 'https://api.businesscentral.dynamics.com/.default',
  };
  oDataApi = new DbConnectionDto()
  isUpdate: boolean = false;
  isError = true
  isLoading = false
  isSaving = false
  serviceType: string = ""
  urlEndPoint: string = ""
  isNav: boolean = false
  constructor(
    private dialogRef: MatDialogRef<ODataConnectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,  // Injecting the data
    private _dbConnectionServices: DbConnectionServiceProxy,
    private _testConnectionService: TestConnectionServiceProxy, private toasterService: ToasterService
  ) {

  }

  ngOnInit(): void {
    if (this.data.type === 'nav') {
      this.nav();
    } else {
      this.isNav = false;
    }

    if ('guid' in this.data) {
      this.isUpdate = true
      this._dbConnectionServices.getSingle(this.data.guid).subscribe((res) => {
        this.oDataApi = res
        //console.log(res)
        this.odata.connectionName = this.oDataApi.connectionName
        this.odata.type = this.oDataApi.type
        this.odata.description = this.oDataApi.description

        if (this.oDataApi.connectionCredJson) {
          const connectionCredObj = JSON.parse(this.oDataApi.connectionCredJson);
          //console.log(connectionCredObj);

          this.serviceType = this.oDataApi.type == 'BCODataWebService' ? 'ODataV4/' : this.removeBaseUrl(connectionCredObj);
          // this.odata.serverName = connectionCredObj.serverName;
          this.odata.tokenEndpoint = connectionCredObj.tokenEndpoint;
          this.odata.clientId = connectionCredObj.clientId;
          this.odata.clientSecret = connectionCredObj.clientSecret;
          this.odata.scope = connectionCredObj.scope;
          this.odata.baseUrl = connectionCredObj.baseUrl;
          //console.log(this.odata.scope == 'CustomBasicAuth');

          if (this.odata.scope == 'CustomBasicAuth') {
            this.nav();
          }
        }
        //console.log((this.odata));


        //console.log(this.oDataApi.type == 'BCODataWebService');

        this.urlEndPoint = `${this.odata.baseUrl}${this.serviceType}`

      })

    }
  }
  nav() {
    this.isNav = true;
    this.odata.type = 'BCODataWebService';
    this.odata.scope = 'CustomBasicAuth'
    this.onTypeChange();
  }
  onTypeChange() {

    this.serviceType = this.odata.type == 'BCODataWebService' ? 'ODataV4/' : '';
    this.urlEndPoint = `${this.odata.baseUrl}${this.serviceType}`
    //console.log(this.odata.type);
  }
  onApiChange() {

    this.urlEndPoint = `${this.odata.baseUrl}${this.serviceType}`

  }
  removeBaseUrl(data) {
    const { endpointUrl, baseUrl } = data;

    if (endpointUrl.startsWith(baseUrl)) {
      return endpointUrl.substring(baseUrl.length);
    }

    return endpointUrl
  }
  // add new box
  // type == 'BCODataWebService' ('ODataV4/') disabled
  // else show blank

  // new box enpoint url
  // baseurl + serviceType   --> disabled
  onSubmit(form: any): void {
    this.isSaving = true
    if (this.isUpdate) {
      this.oDataApi.guid = this.data.guid
    } else {
      this.oDataApi.guid = '00000000-0000-0000-0000-000000000000';
    }
    this.oDataApi.connectionName = this.odata.connectionName
    this.oDataApi.type = this.odata.type
    this.oDataApi.description = this.odata.description

    const tenantId = this.odata.baseUrl.startsWith("https://api.businesscentral.dynamics.com/v2.0/") ? this.odata.baseUrl.split("/")[4] : null;
    // const serviceType = this.oDataApi.type == 'BCODataWebService' ? 'ODataV4/' : 'api/v2.0/';

    const connectionCredJson = {
      // serverName: this.odata.serverName,
      tokenEndpoint: `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`,
      clientSecret: this.odata.clientSecret,
      scope: this.odata.scope,
      clientId: this.odata.clientId,
      baseUrl: this.odata.baseUrl,
      endpointUrl: this.odata.baseUrl + this.serviceType
    };
    this.oDataApi.connectionCredJson = JSON.stringify(connectionCredJson);
    //console.log(this.oDataApi)
    this._dbConnectionServices.createOrEdit(this.oDataApi).subscribe((res) => {
      if (res) {
        this.isSaving = false
        this.dialogRef.close(res);
      }
    });
  }


  testBcConnection() {
    this.isLoading = true
    let clientSecret = this.odata.clientSecret
    let clientId = this.odata.clientId
    let tokenEndpoint = ''
    //console.log(this.isNav);

    if (!this.isNav) {
      //console.log('changing');

      const tenantId = this.odata.baseUrl.startsWith("https://api.businesscentral.dynamics.com/v2.0/") ? this.odata.baseUrl.split("/")[4] : null;
      tokenEndpoint = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`
    }
    //console.log(clientSecret, clientId, tokenEndpoint, this.odata.scope);


    this._testConnectionService.checkBcConnection(clientSecret, clientId, tokenEndpoint, this.odata.scope).subscribe((res) => {
      //console.log(res)
      if (res) {
        this.isLoading = false
        if (!res.isError) {
          this.isError = false
          this.toasterService.showToaster('success', res.message)
        } else {
          this.isError = true
          this.toasterService.showToaster('error', res.message)
        }
      }
    })
  }

  onClose(): void {
    this.dialogRef.close();
  }


}
