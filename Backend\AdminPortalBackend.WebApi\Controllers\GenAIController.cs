﻿using AdminPortalBackend.Infrastructure.SignalRHub;
using AdminPortalBackend.Infrastructure;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using AdminPortalBackend.Core.Models;
using Dapper;
using Microsoft.SemanticKernel.Services;
using AdminPortalBackend.Core.Features;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class GenAIController(AIService aiService, IDbConnection dbConnection, ILogger<GenAIController> logger) : ControllerBase
    {
        [HttpGet("GetAIResponse/{question}")]
        public async Task<ActionResult<AIResponseMessage>> GetAIResponse(string question)
        {
            logger.LogInformation("Calling AI Service {MethodName} with {question}", nameof(GetAIResponse), question);
            var response = await aiService.GetAIAnswer(question);
            logger.LogInformation("Finishing AI Call {MethodName} with {response}", nameof(GetAIResponse), response.Message);

            return Ok(response);
        }

        [HttpGet("AddAIMemory")]
        public async Task<ActionResult> AddAIMemory()
        {
            string sqlQuery = "SELECT Id, Message, Status, Timestamp FROM [dbo].[Notes] WHERE Status != @Status";
            var draftNotes = dbConnection.Query<Note>(sqlQuery, new { Status = "Published" });

            foreach (var note in draftNotes)
            {
                try
                {
                    if (note.Status == "Updated")
                    {
                        await aiService.DeleteSingleMemory(note.Id.ToString());
                    }
                    string result = await aiService.AddMemory(note.Message, note.Id.ToString());

                    string updateQuery = "UPDATE [dbo].[Notes] SET Status = @Status WHERE Id = @Id";
                    await dbConnection.ExecuteAsync(updateQuery, new { Status = "Published", Id = note.Id });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, ex.ToString());
                    string updateQuery = "UPDATE [dbo].[Notes] SET Status = @Status WHERE Id = @Id";
                    await dbConnection.ExecuteAsync(updateQuery, new { Status = "Error", Id = note.Id });
                }
            }

            return Created();
        }
        [HttpPost("MapColumns")]
        public async Task<ActionResult<List<MappedColumn>>> MapColumns(MapColumns mapColumns)
        {
            var res = await aiService.MapColumns(mapColumns.SourceColumns, mapColumns.DestinationColumns);
            return Ok(res);
        }
    }
}
