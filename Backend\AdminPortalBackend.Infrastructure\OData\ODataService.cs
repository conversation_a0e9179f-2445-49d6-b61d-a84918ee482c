﻿using System.Data;
using System.Data.SqlClient;
using System.Text.Json;
using Dapper;
using System.Net.Http.Headers;
using System.Xml.Linq;
using Microsoft.Extensions.Configuration;
using AdminPortalBackend.Core.Features;
using Microsoft.Identity.Client;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.Repositories;
using AdminPortalBackend.Infrastructure.DataStore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Elastic.Clients.Elasticsearch;
using System.Text;
using AdminPortalBackend.Core.Models;
using System.Text.Json.Serialization;

namespace AdminPortalBackend.Infrastructure.OData;

public class ODataService(IHttpClientFactory httpClientFactory,
                        IDbConnection _dbConnection,
                        IDbConnectionRepository dbConnectionRepository,
                        IConfiguration configuration, ILogger<ODataService> logger)
    {
    public async Task<string> GetAccessTokenAsync(Guid connectionId)
    {
        try
        {
            var connection = await dbConnectionRepository.GetODataConnectionDetailAsync(connectionId);
            var accessToken = await GetAccessTokenAsync(connection);
            return accessToken;
        }
        catch (Exception ex)
        {
            logger.LogError($"Error in GetAccessTokenAsync(Guid connectionId): {ex.Message}", ex);
            throw;
        }
    }

    public async Task<string> GetAccessTokenAsync(ODataConnectionDetailDto dbConnection)
    {
        try
        {
            logger.LogInformation("Getting Access Token");

            if (dbConnection != null)
            {
                if (dbConnection.ExpiresOn > DateTime.UtcNow)
                {
                    return EncryptionHelper.Decrypt(dbConnection.AccessToken);
                }

            try
            {
                    if (dbConnection.ConnectionCreds.Scope != "CustomBasicAuth")
                    {
                        var secret = EncryptionHelper.Decrypt(dbConnection.ConnectionCreds.ClientSecret);
                        IConfidentialClientApplication app = ConfidentialClientApplicationBuilder.Create(dbConnection.ConnectionCreds.ClientId)
                            .WithClientSecret(secret)
                            .WithAuthority(dbConnection.ConnectionCreds.TokenEndpoint)
                            .Build();

                        string[] scopes = new string[] { dbConnection.ConnectionCreds.Scope };

                        AuthenticationResult result = null;

                        result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
                        dbConnection.AccessToken = EncryptionHelper.Encrypt(result.AccessToken);
                        dbConnection.ExpiresOn = result.ExpiresOn;
                    }
                    else
                    {
                        var password = EncryptionHelper.Decrypt(dbConnection.ConnectionCreds.ClientSecret);
                        var customAuth = new { AuthType = "CustomBasicAuth", UserName = dbConnection.ConnectionCreds.ClientId, Password = password };
                        dbConnection.AccessToken = EncryptionHelper.Encrypt(JsonSerializer.Serialize(customAuth));
                        dbConnection.ExpiresOn = DateTime.Now.AddYears(2);
                    }

                    try
                    {
                        await _dbConnection.ExecuteAsync(
                            "UPDATE DbConnections SET AccessToken = @AccessToken, ExpiresOn = @ExpiresOn WHERE Guid = @Guid",
                            new
                            {
                                AccessToken = dbConnection.AccessToken,
                                ExpiresOn = dbConnection.ExpiresOn,
                                Guid = dbConnection.Guid
                            });
                        logger.LogInformation("Saved AccessToken to DbConnection");
                    }
                    catch (Exception updateEx)
                    {
                        logger.LogError(updateEx, $"Error saving AccessToken to DbConnection: {updateEx.Message}", updateEx);
                        throw;
                    }
                }
                catch (MsalServiceException ex)
                {
                    logger.LogError($"Error acquiring token: {ex.Message}", ex);
                    throw new Exception($"Error acquiring token: {ex.Message}", ex);
                }

                return EncryptionHelper.Decrypt(dbConnection.AccessToken);
            }
            return null;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error in GetAccessTokenAsync(ODataConnectionDetailDto dbConnection): {ex.Message}", ex);
            throw;
        }
    }

    public async Task<List<Dictionary<string, object>>> PullDataFromOData(string resourceUrl, string token)
    {
        try
        {
            logger.LogInformation("Pulling data from OData");

            var httpClient = httpClientFactory.CreateClient();
            httpClient.SetAuthorizationHeader(token);

            var response = await httpClient.GetAsync(resourceUrl);
            response.EnsureSuccessStatusCode(); // Throw if not a success code

            var responseData = await response.Content.ReadAsStringAsync();
            var apiData = JsonSerializer.Deserialize<ODataApiResponse>(responseData);

            var transformedData = apiData.value.Select(row =>
                row.ToDictionary(
                    kvp => char.ToUpper(kvp.Key[0]) + kvp.Key.Substring(1),
                    kvp => kvp.Value
                )
            ).ToList();

            logger.LogInformation("Pulled data from OData Service");
            return transformedData;
        }
        catch (Exception ex)
        {
            logger.LogError($"Error pulling data from OData: {ex.Message}", ex);
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetBCSingleRecord(string resourceUrl, string token)
    {
        try
        {
            logger.LogInformation("Fetching BC single record");

            var httpClient = httpClientFactory.CreateClient();
            httpClient.SetAuthorizationHeader(token);

            var response = await httpClient.GetAsync(resourceUrl);
            response.EnsureSuccessStatusCode(); // Throw if not a success code

            var responseData = await response.Content.ReadAsStringAsync();
            var apiData = JsonSerializer.Deserialize<Dictionary<string, object>>(responseData);
            var transformedData = apiData.ToDictionary(kvp => char.ToUpper(kvp.Key[0]) + kvp.Key.Substring(1), kvp => kvp.Value);

            logger.LogInformation("Fetched BC Single Record");
            return transformedData;
        }
        catch (Exception ex)
        {
            logger.LogError(ex,$"Error fetching BC single record: {ex.Message}", ex);
            throw;
        }
    }

    public async Task<List<Dictionary<string, object>>> PullDataFromOData(Guid connectionId, string entityName)
    {
        try
        {
            var connection = await dbConnectionRepository.GetODataConnectionDetailAsync(connectionId);

            if (connection.Type == "BCODataRestApiService" && entityName == "Company")
                entityName = "companies";

            var accessToken = await GetAccessTokenAsync(connection);
            var data = await PullDataFromOData(connection.ConnectionCreds.EndpointUrl + entityName, accessToken);

            return data;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error in PullDataFromOData(Guid connectionId, string entityName): {ex.Message}", ex);
            throw;
        }
    }

    public async Task<Dictionary<string, object>> PostDataAsync(string postUrl, Dictionary<string, object> data, string token)
        {
        try { 
            var httpClient = httpClientFactory.CreateClient();
            httpClient.SetAuthorizationHeader(token);

            var jsonContent = JsonSerializer.Serialize(data);
            var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(postUrl, content);
            response.EnsureSuccessStatusCode(); // Throw if not a success code

            var responseData = await response.Content.ReadAsStringAsync();

            var apiData = JsonSerializer.Deserialize<Dictionary<string,object>>(responseData);

            return apiData;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error posting data to {postUrl}: {ex.Message}", ex);
            throw;
        }
    }

    public async Task<Dictionary<string, object>> PatchDataAsync(string patchUrl, Dictionary<string, object> data, string token)
    {
        try
        {
            var httpClient = httpClientFactory.CreateClient();
            httpClient.SetAuthorizationHeader(token);

            // Add the If-Match header to the request
            httpClient.DefaultRequestHeaders.Add("If-Match", "*");

            var jsonContent = JsonSerializer.Serialize(data);
        var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

            var response = await httpClient.PatchAsync(patchUrl, content);
            response.EnsureSuccessStatusCode(); // Throw if not a success code

            var responseData = await response.Content.ReadAsStringAsync();

            var apiData = JsonSerializer.Deserialize<Dictionary<string, object>>(responseData);

            return apiData;
        }
        catch (Exception ex)
        {
            logger.LogError($"Error patching data at {patchUrl}: {ex.Message}", ex);
            throw;
        }
    }

    public async Task DeleteDataAsync(string deleteUrl, string eTag, string token)
    {
        try
        {
            var httpClient = httpClientFactory.CreateClient();
            httpClient.SetAuthorizationHeader(token);

            // Add the If-Match header to the request
            httpClient.DefaultRequestHeaders.Add("If-Match", eTag);

            var response = await httpClient.DeleteAsync(deleteUrl);
            response.EnsureSuccessStatusCode(); // Throw if not a success code
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error deleting data at {deleteUrl}: {ex.Message}", ex);
            throw;
        }
    }

    public async Task TransferEntities(Guid connectionId)
    {
        try
        {
            var data = await PullDataFromOData(connectionId, String.Empty);
            var columns = new List<string> { "Name", "Kind", "Url" };
            DeleteTableRows("BCEntities", connectionId.ToString());
            CopyDataToSql("BCEntities", columns, data, connectionId, configuration.GetConnectionString("AdminConnection"));
        }
        catch (Exception ex)
        {
            logger.LogError($"Error in TransferEntities(Guid connectionId): {ex.Message}", ex);
            throw;
        }
    }

    public async Task TransferCompanyData(Guid connectionId)
    {
        try
        {
            var data = await PullDataFromOData(connectionId, "Company");

            //to handle Nav company data
            foreach (var item in data)
            {
                if (item.ContainsKey("Name") && item.Keys.Count == 1)
                {
                    item["Id"] = item["Name"];
                    item["Display_Name"] = item["Name"];
                }
            }

            var columns = new List<string> { "Id", "Name", "Display_Name" };

            DeleteTableRows("BCCompanies", connectionId.ToString());
            CopyDataToSql("BCCompanies", columns, data, connectionId, configuration.GetConnectionString("AdminConnection"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error in TransferCompanyData(Guid connectionId): {ex.Message}", ex);
            throw;
        }
    }

    public async Task<List<Dictionary<string, object>>> GetODataWebSeriviceData(Guid connectionId, string dataUrl)
    {
        var connection = await dbConnectionRepository.GetODataConnectionDetailAsync(connectionId);

        var accessToken = await GetAccessTokenAsync(connection);

        var data = await PullDataFromOData(connection.ConnectionCreds.EndpointUrl + dataUrl, accessToken);

        return data;
    }

    public async Task<string> GetPrimaryKeyName(Guid connectionId, string entityName)
    {
        var connection = await dbConnectionRepository.GetODataConnectionDetailAsync(connectionId);

        var accessToken = await GetAccessTokenAsync(connection);

        var httpClient = httpClientFactory.CreateClient();
        httpClient.SetAuthorizationHeader(accessToken);

        var response = await httpClient.GetAsync(connection.ConnectionCreds.EndpointUrl + "$metadata");
        response.EnsureSuccessStatusCode(); // Throw if not a success code

        var responseData = await response.Content.ReadAsStringAsync();

        XDocument doc;
        try
        {
            doc = XDocument.Parse(responseData);
        }
        catch (IOException ex)
        {
            throw new Exception("Failed to parse the metadata XML.", ex);
        }

        // Find the entity type by name
        var entityType = doc.Descendants(XName.Get("EntityType", "http://docs.oasis-open.org/odata/ns/edm"))
                            .FirstOrDefault(e => (string)e.Attribute("Name") == entityName);

        if (entityType == null)
        {
            throw new ArgumentException($"Entity '{entityName}' not found in the metadata.");
        }

        // Retrieve the primary key
        var keyProperties = entityType.Elements(XName.Get("Key", "http://docs.oasis-open.org/odata/ns/edm"))
            .SelectMany(key => key.Elements(XName.Get("PropertyRef", "http://docs.oasis-open.org/odata/ns/edm"))
                .Select(p => (string)p.Attribute("Name"))).ToList();

        return String.Join("@#", keyProperties);
    }
    public async Task<string> GetPrimaryKeyTypeAndLength(Guid connectionId, string entityName)
    {
        var connection = await dbConnectionRepository.GetODataConnectionDetailAsync(connectionId);

        var accessToken = await GetAccessTokenAsync(connection);

        var httpClient = httpClientFactory.CreateClient();
        httpClient.SetAuthorizationHeader(accessToken);

        var response = await httpClient.GetAsync(connection.ConnectionCreds.TokenEndpoint + "$metadata");
        response.EnsureSuccessStatusCode(); // Throw if not a success code

        var responseData = await response.Content.ReadAsStringAsync();

        XDocument doc;
        try
        {
            doc = XDocument.Parse(responseData);
        }
        catch (IOException ex)
        {
            throw new Exception("Failed to parse the metadata XML.", ex);
        }

        // Find the entity type by name
        var entityType = doc.Descendants(XName.Get("EntityType", "http://docs.oasis-open.org/odata/ns/edm"))
                            .FirstOrDefault(e => (string)e.Attribute("Name") == entityName);

        if (entityType == null)
        {
            throw new ArgumentException($"Entity '{entityName}' not found in the metadata.");
        }

        // Retrieve the primary key
        var keyProperty = entityType.Elements(XName.Get("Key", "http://docs.oasis-open.org/odata/ns/edm"))
            .SelectMany(key => key.Elements(XName.Get("PropertyRef", "http://docs.oasis-open.org/odata/ns/edm"))
                .Select(p => (string)p.Attribute("Name")))
            .FirstOrDefault();

        if (string.IsNullOrEmpty(keyProperty))
        {
            throw new Exception($"No primary key found for entity '{entityName}'.");
        }

        // Get the property details for the primary key
        var property = entityType.Elements(XName.Get("Property", "http://docs.oasis-open.org/odata/ns/edm"))
            .FirstOrDefault(p => (string)p.Attribute("Name") == keyProperty);

        if (property == null)
        {
            throw new Exception($"Property '{keyProperty}' not found in entity '{entityName}'.");
        }

        var propertyType = (string)property.Attribute("Type");
        var length = GetPropertyLength(property);

        // Convert to SQL type
        return ConvertToSqlType(propertyType, length);
    }

    private int? GetPropertyLength(XElement property)
    {
        // Check if the property has a 'MaxLength' attribute
        var maxLengthAttr = property.Attribute("MaxLength");
        if (maxLengthAttr != null && int.TryParse(maxLengthAttr.Value, out int maxLength))
        {
            return maxLength;
        }

        // Handle other types, if necessary, or return null
        return null;
    }

    private string ConvertToSqlType(string propertyType, int? length)
    {
        // Map OData types to SQL types
        switch (propertyType)
        {
            case string s when s.StartsWith("Edm.String"):
                return length.HasValue ? $"NVARCHAR({length})" : "NVARCHAR(MAX)";
            case string s when s.StartsWith("Edm.Int32"):
                return "INT";
            case string s when s.StartsWith("Edm.Int64"):
                return "BIGINT";
            case string s when s.StartsWith("Edm.Decimal"):
                return "DECIMAL(18, 2)"; // Example; adjust precision and scale as needed
                                         // Add more mappings as necessary
            default:
                return "VARCHAR(MAX)"; // Default case if type not recognized
        }
    }

    public async Task<string> RefreshMetadata(Guid connectionId)
    {
        var connection = await dbConnectionRepository.GetODataConnectionDetailAsync(connectionId);

        var accessToken = await GetAccessTokenAsync(connection);

        var httpClient = httpClientFactory.CreateClient();
        httpClient.SetAuthorizationHeader(accessToken);

        var response = await httpClient.GetAsync(connection.ConnectionCreds.EndpointUrl + "$metadata");
        response.EnsureSuccessStatusCode(); // Throw if not a success code

        var responseData = await response.Content.ReadAsStringAsync();

        XDocument doc;
        try
        {
            doc = XDocument.Parse(responseData);
        }
        catch (IOException ex)
        {
            throw new Exception("Failed to parse the metadata XML.", ex);
        }

        // Parse the Entity Types and Entity Sets
        var entityTypes = doc.Descendants(XName.Get("EntityType", "http://docs.oasis-open.org/odata/ns/edm"));
        var entitySets = doc.Descendants(XName.Get("EntitySet", "http://docs.oasis-open.org/odata/ns/edm"))
            .Where(entitySet => entitySet.Attribute("Name") != null && entitySet.Attribute("EntityType") != null)
                .ToDictionary(
                    entitySet => entitySet.Attribute("EntityType")?.Value.Replace("Microsoft.NAV.", "").Replace("NAV.", ""), // Value: EntityType
                    entitySet => entitySet.Attribute("Name")?.Value // Key: EntitySetName
                );

        // Collect all Properties and Primary Key Names
        var allProperties = entityTypes.SelectMany(entityType =>
        {
            // Get Primary Key (Key) properties
            var keyProperties = entityType.Elements(XName.Get("Key", "http://docs.oasis-open.org/odata/ns/edm"))
                                          .SelectMany(key => key.Elements(XName.Get("PropertyRef", "http://docs.oasis-open.org/odata/ns/edm"))
                                                               .Select(p => (string)p.Attribute("Name"))).ToList();

            // Collect entity properties and include primary key info
            return entityType.Elements(XName.Get("Property", "http://docs.oasis-open.org/odata/ns/edm"))
                             .Select(p => new
                             {
                                 EntityName = entitySets.GetValueOrDefault(entityType.Attribute("Name")?.Value),
                                 PropertyName = p.Attribute("Name")?.Value,
                                 PropertyType = p.Attribute("Type")?.Value,
                                 PrimaryKey = keyProperties,
                                 Size = p.Attribute("MaxLength")?.Value,
                             })
                             .Where(prop => prop.PropertyName != null && prop.PropertyType != null)
                             .Where(prop => prop.EntityName != null);
        }).ToList();

        // Bulk Insert
        using (var sqlConnection = new SqlConnection(configuration.GetConnectionString("AdminConnection")))
        {
            sqlConnection.Open();

            using (var transaction = sqlConnection.BeginTransaction())
            {
                // Step 1: Delete the existing metadata entries
                this.DeleteTableRows("BCEntityMetadata", connectionId.ToString());

                // Step 2: Create the DataTable for metadata without the PrimaryKey
                var dataTable = new DataTable();
                dataTable.Columns.Add("EntityName", typeof(string));
                dataTable.Columns.Add("PropertyName", typeof(string));
                dataTable.Columns.Add("PropertyType", typeof(string));
                dataTable.Columns.Add("ConnectionId", typeof(Guid));
                dataTable.Columns.Add("CreatedDate", typeof(DateTime));
                dataTable.Columns.Add("MaxLength", typeof(string));

                foreach (var property in allProperties)
                {
                    // Remove the PrimaryKey from metadata
                    dataTable.Rows.Add(property.EntityName, property.PropertyName, property.PropertyType, connectionId, DateTime.Now, property.Size);
                }

                // Step 3: Insert the data into the BCEntityMetadata table (without the PrimaryKey)
                using (var bulkCopy = new SqlBulkCopy(sqlConnection, SqlBulkCopyOptions.Default, transaction))
                {
                    bulkCopy.DestinationTableName = "BCEntityMetadata"; // Specify your table name
                    bulkCopy.WriteToServer(dataTable);
                }

                // Step 4: Update the BCEntities table with the PrimaryKey
                foreach (var property in allProperties)
                {
                    var primaryKey = string.Join("@#", property.PrimaryKey);

                    // Assuming the BCEntities table has columns: EntityName, PrimaryKey
                    var updateQuery = @"
                        UPDATE BCEntities
                        SET PrimaryKey = @PrimaryKey
                        WHERE Name = @EntityName AND ConnectionId = @ConnectionId";

                    using (var command = new SqlCommand(updateQuery, sqlConnection, transaction))
                    {
                        command.Parameters.AddWithValue("@PrimaryKey", primaryKey);
                        command.Parameters.AddWithValue("@EntityName", property.EntityName);
                        command.Parameters.AddWithValue("@ConnectionId", connectionId);

                        command.ExecuteNonQuery();
                    }
                }

                // Step 5: Commit the transaction
                transaction.Commit();
            }
        }


        return responseData;
    }

    public async void CopyDataToSql(string destinationTable, List<string> columns, List<Dictionary<string, object>> apiData, Guid connectionId, string connectionString = "")
    {
        logger.LogInformation("Copying Data to SQL");
        try
        {

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                var dataTable = new DataTable();
                foreach (var column in columns)
                {
                    dataTable.Columns.Add(column, typeof(string));
                }
                dataTable.Columns.Add("ConnectionId", typeof(Guid));
                dataTable.Columns.Add("CreatedDate", typeof(DateTime));

                for (var i = 0; i < apiData.Count; i++)
                {
                    var row = apiData[i];
                    var dataRow = dataTable.NewRow();
                    foreach (var column in columns)
                    {
                        if (row.ContainsKey(column))
                            dataRow[column] = row[column];
                        if (column == "Display_Name")
                        {
                            if (row.ContainsKey("DisplayName"))
                            {
                                dataRow["Display_Name"] = row["DisplayName"];
                            }
                            else if (row.ContainsKey("display_name"))
                            {
                                dataRow["Display_Name"] = row["display_name"];
                            }
                        }
                    }
                    dataRow["ConnectionId"] = connectionId;
                    dataRow["CreatedDate"] = DateTime.Now;
                    dataTable.Rows.Add(dataRow);
                }

                using (var bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = destinationTable;
                    bulkCopy.WriteToServer(dataTable);
                }
            }
        }catch (Exception ex)
        {
            logger.LogError("Error: Transfering data to SQL");
        }
    }

    public void DeleteTableRows(string tableName, string connectionId)
    {
        try
        {
            logger.LogInformation("Deleting rows");
            var query = $"DELETE FROM {tableName} WHERE ConnectionId = @ConnectionId";

            var parameters = new { ConnectionId = connectionId };

            _dbConnection.Execute(query, parameters);
        }catch (Exception ex)
        {
            logger.LogError(ex, "Error: Deleting rows");
        }
    }




    private string ConvertEdmTypeToSql(string edmType)
        {
            // Basic mapping of EDM types to SQL types
            switch (edmType)
            {
                case "Edm.String":
                    return "VARCHAR(MAX)";
                case "Edm.Int32":
                    return "INT";
                case "Edm.Boolean":
                    return "BIT";
                case "Edm.DateTime":
                    return "DATETIME";
                // Add more mappings as needed
                default:
                    return "VARCHAR(MAX)"; // Fallback to a default type
            }
    }
}

public class BCEntityMetadata
{
    public string EntityName { get; set; }
    public string PropertyName { get; set; }
    public string PropertyType { get; set; }
    public string Size { get; set; }
    public string ConnectionId { get; set; }
}

public class ODataApiResponse
{
    [JsonPropertyName("@odata.nextLink")]
    public string NextLink { get; set; }
    public List<Dictionary<string, object>> value { get; set; }
}
