﻿using AdminPortalBackend.Core.Contracts;
using AdminPortalBackend.Infrastructure;
using AdminPortalBackend.Infrastructure.DataStore;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.SignalRHub;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ATestController(ODataService oDataService,
                                 SyncManager syncManager,
                                 ILogger<ATestController> logger,
                                 IHubContext<MessageHub, IMessageClient> messageHub,
                                 AIService aiService) : ControllerBase
    {
        [HttpGet("entities/{connectionId}")]
        public async Task<IActionResult> TransferEntities(Guid connectionId)
        {
            await oDataService.TransferEntities(connectionId);

            return Ok();
        }

        [HttpGet("metadata/{connectionId}")]
        public async Task<IActionResult> TransferMetadata(Guid connectionId)
        {
            var data = await oDataService.RefreshMetadata(connectionId);

            return Ok();
        }

        [HttpGet("companies/{connectionId}")]
        public async Task<IActionResult> TransferCompanyData(Guid connectionId)
        {
            await oDataService.TransferCompanyData(connectionId);

            return Ok();
        }

        [HttpGet("test-bc-url/{token}")]
        public async Task<IActionResult> TestBCUrl(string token)
        {
            var result = await oDataService.PullDataFromOData("http://dii.desco.com:7058/DII-NAV9938/ODataV4/", token);

            return Ok(result);
        }

        [HttpGet("sqltobc")]
        public async Task<IActionResult> SqlToBCTest()
        {
            await syncManager.ProcessSQLToBCUpdate("AC0E8638-3E1A-42A9-A1B0-2082369DB16E", "TestDB");

            return Ok();
        }

        [HttpGet("navtosql")]
        public async Task<IActionResult> NavToSqlTest()
        {
            await syncManager.ProcessNavToSqlUpdate();

            return Ok();
        }

        [HttpGet("navtosql/create-job")]
        public async Task<IActionResult> NavToSqlCreateJob()
        {
            RecurringJob.AddOrUpdate(
                    "ProcessNavToSqlUpdate",
                    () => syncManager.ProcessNavToSqlUpdate(),
                    "*/5 * * * *" 
                );

            return Ok();
        }

        [HttpGet("bctosql")]
        public async Task<IActionResult> BCToSqlTest()
        {
            await messageHub.Clients.All.SendMessage("from server");
            //await syncManager.ProcessBCToSqlUpdate();
            using (logger.BeginScope(new Dictionary<string, object> { { "PersonId", 5 } }))
            {            
                logger.LogInformation("testing logger");
                logger.LogInformation("testing logger2");
            }
            
            return Ok();
        }
    }
}
