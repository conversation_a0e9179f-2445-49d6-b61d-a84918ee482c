{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AdminPortalBackend.WebApi/1.0.0": {"dependencies": {"AdminPortalBackend.Core": "1.0.0", "AdminPortalBackend.Infrastructure": "1.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.8", "SendGrid": "9.29.3", "Serilog.AspNetCore": "8.0.0", "Serilog.Sinks.MSSqlServer": "6.5.0", "Stripe.net": "45.14.0", "Swashbuckle.AspNetCore": "6.4.0"}, "runtime": {"AdminPortalBackend.WebApi.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "Azure.AI.OpenAI/2.1.0-beta.1": {"dependencies": {"Azure.Core": "1.43.0", "OpenAI": "2.1.0-beta.1"}, "runtime": {"lib/netstandard2.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "2.100.24.50101"}}}, "Azure.Core/1.43.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.43.0.0", "fileVersion": "1.4300.24.46205"}}}, "Azure.Identity/1.6.0": {"dependencies": {"Azure.Core": "1.43.0", "Microsoft.Identity.Client": "4.66.1", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.600.22.20503"}}}, "Dapper/2.1.35": {"runtime": {"lib/net7.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.35.13827"}}}, "FluentValidation/11.5.1": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.5.1.0"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.5.1", "FluentValidation.DependencyInjectionExtensions": "11.5.1"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.3.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"dependencies": {"FluentValidation": "11.5.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.5.1.0"}}}, "Hangfire/1.8.14": {"dependencies": {"Hangfire.AspNetCore": "1.8.14", "Hangfire.Core": "1.8.14", "Hangfire.SqlServer": "1.8.14"}}, "Hangfire.AspNetCore/1.8.14": {"dependencies": {"Hangfire.NetCore": "1.8.14"}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.Core/1.8.14": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.NetCore/1.8.14": {"dependencies": {"Hangfire.Core": "1.8.14", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1"}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.SqlServer/1.8.14": {"dependencies": {"Hangfire.Core": "1.8.14"}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MediatR/12.4.1": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.8": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.8.0", "fileVersion": "8.0.824.36908"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.0.1": {"dependencies": {"Azure.Identity": "1.6.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.0.1", "Microsoft.Identity.Client": "4.66.1", "Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2", "Microsoft.SqlServer.Server": "1.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime.Caching": "5.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.0.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1"}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.ObjectPool/6.0.3": {}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24518.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "Microsoft.Identity.Client/4.66.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.66.1.0", "fileVersion": "4.66.1.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.66.1", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OData.Client/8.1.0": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Microsoft.OData.Core": "8.1.0"}, "runtime": {"lib/net8.0/Microsoft.OData.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.OData.Core/8.1.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "6.0.3", "Microsoft.OData.Edm": "8.1.0", "Microsoft.Spatial": "8.1.0"}, "runtime": {"lib/net8.0/Microsoft.OData.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.OData.Edm/8.1.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.6.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.OData.Edm.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Microsoft.SemanticKernel/1.24.1": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.24.1", "Microsoft.SemanticKernel.Core": "1.24.1"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Abstractions/1.24.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.VectorData.Abstractions": "9.0.0-preview.1.24518.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.24.1": {"dependencies": {"Azure.AI.OpenAI": "2.1.0-beta.1", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.24.1", "Microsoft.SemanticKernel.Core": "1.24.1"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.24.1": {"dependencies": {"Microsoft.SemanticKernel.Core": "1.24.1", "OpenAI": "2.1.0-beta.1"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Core/1.24.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.SemanticKernel.Abstractions": "1.24.1", "System.Numerics.Tensors": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Plugins.Core/1.24.1-alpha": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.SemanticKernel.Core": "1.24.1"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Plugins.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Spatial/8.1.0": {"runtime": {"lib/net8.0/Microsoft.Spatial.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.51017"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "OpenAI/2.1.0-beta.1": {"dependencies": {"System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SendGrid/9.29.3": {"dependencies": {"Newtonsoft.Json": "13.0.3", "starkbank-ecdsa": "1.3.3"}, "runtime": {"lib/netstandard2.0/SendGrid.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Serilog/3.1.1": {"runtime": {"lib/net7.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Serilog": "3.1.1", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.0", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Serilog": "3.1.1", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Serilog": "3.1.1"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.MSSqlServer/6.5.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.0.1", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Serilog": "3.1.1", "Serilog.Sinks.PeriodicBatching": "3.1.0", "System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/Serilog.Sinks.MSSqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "starkbank-ecdsa/1.3.3": {"runtime": {"lib/netstandard2.1/StarkbankEcdsa.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Stripe.net/45.14.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/Stripe.net.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Swashbuckle.AspNetCore/6.4.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.100.24.46703"}}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Tensors/8.0.0": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/5.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "5.0.20.51904"}}}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.5": {"runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "AdminPortalBackend.Core/1.0.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "FluentValidation.AspNetCore": "11.3.0", "MediatR": "12.4.1", "Newtonsoft.Json": "13.0.3"}, "runtime": {"AdminPortalBackend.Core.dll": {}}}, "AdminPortalBackend.Infrastructure/1.0.0": {"dependencies": {"AdminPortalBackend.Core": "1.0.0", "Dapper": "2.1.35", "Hangfire": "1.8.14", "Hangfire.SqlServer": "1.8.14", "Microsoft.Identity.Client": "4.66.1", "Microsoft.OData.Client": "8.1.0", "Microsoft.OData.Core": "8.1.0", "Microsoft.OData.Edm": "8.1.0", "Microsoft.SemanticKernel": "1.24.1", "Microsoft.SemanticKernel.Plugins.Core": "1.24.1-alpha", "Microsoft.Spatial": "8.1.0", "Newtonsoft.Json": "13.0.3", "System.ComponentModel.Annotations": "5.0.0", "System.Data.SqlClient": "4.8.6", "System.Text.Json": "8.0.5"}, "runtime": {"AdminPortalBackend.Infrastructure.dll": {}}}}}, "libraries": {"AdminPortalBackend.WebApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "Azure.AI.OpenAI/2.1.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-mbCkF0ORgHr7YAA3KntkCqlwSQFaffwvIktJGzbm+wwwzuMvSYsv6KjyX9jzrhgob44HAh6IlkGTKDdMUUfSjg==", "path": "azure.ai.openai/2.1.0-beta.1", "hashPath": "azure.ai.openai.2.1.0-beta.1.nupkg.sha512"}, "Azure.Core/1.43.0": {"type": "package", "serviceable": true, "sha512": "sha512-XE6GHvFAv0djbzNSC3jJPDytcDRmg2CUYlh4V4HjIjX9xLP90OYDPz1KyF02qJJJTF9wLuG4C0XIQm22MGkCww==", "path": "azure.core/1.43.0", "hashPath": "azure.core.1.43.0.nupkg.sha512"}, "Azure.Identity/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON>Msb6rD2PK9P0SyibFfEhvWWttdrYhyPF4f41uzdB/44yQlV+2Wehxyg489Rj6gbPvSPgbKq0xsHJBhipZA==", "path": "azure.identity/1.6.0", "hashPath": "azure.identity.1.6.0.nupkg.sha512"}, "Dapper/2.1.35": {"type": "package", "serviceable": true, "sha512": "sha512-YKRwjVfrG7GYOovlGyQoMvr1/IJdn+7QzNXJxyMh0YfFF5yvDmTYaJOVYWsckreNjGsGSEtrMTpnzxTUq/tZQw==", "path": "dapper/2.1.35", "hashPath": "dapper.2.1.35.nupkg.sha512"}, "FluentValidation/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-0h1Q5lNOLLyYTWMJmyNoMqhY4CBRvvUWvJP1R4F2CnmmzuWwvB0A8aVmw5+lOuwYnwUwCRrdeMLbc81F38ahNQ==", "path": "fluentvalidation/11.5.1", "hashPath": "fluentvalidation.11.5.1.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-iWM0LS1MDYX06pcjMEQKqHirl2zkjHlNV23mEJSoR1IZI7KQmTa0RcTtGEJpj5+iHvBCfrzP2mYKM4FtRKVb+A==", "path": "fluentvalidation.dependencyinjectionextensions/11.5.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512"}, "Hangfire/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-8ve7Di0xvy0ZxCxibcDEzjVVx/H4mJiYSrc5A7Oj2Q62y5vB+Fq6k03zkFj96Xlgs5ivVSeEGfreF/NECJ7tlQ==", "path": "hangfire/1.8.14", "hashPath": "hangfire.1.8.14.nupkg.sha512"}, "Hangfire.AspNetCore/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-aTPzKN/g9SW30QB1SLTOMuckKqTVL1YAhtEpWll4LPbPTgyeBHJTChdRhrN127fj+mqJ6P4P7+HJBhNYUFTfNw==", "path": "hangfire.aspnetcore/1.8.14", "hashPath": "hangfire.aspnetcore.1.8.14.nupkg.sha512"}, "Hangfire.Core/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-tj/+J8/UdaxydFX6VQr5IEyBtVbAOvkQ8X8tIQKwY9zlpmK83hP4iHEQQQ26zzGUpcE1HlPc6PBUv0NgUDXS3A==", "path": "hangfire.core/1.8.14", "hashPath": "hangfire.core.1.8.14.nupkg.sha512"}, "Hangfire.NetCore/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-fBLdsxWYFdrQuenvVHEj/z8nOXoOTqpWIl4qYoinBAUCVmp4qlxfFsY3Aq3VVbwket0wBH472aG2LAmYm6hjxw==", "path": "hangfire.netcore/1.8.14", "hashPath": "hangfire.netcore.1.8.14.nupkg.sha512"}, "Hangfire.SqlServer/1.8.14": {"type": "package", "serviceable": true, "sha512": "sha512-OrsxbJD0UYanIk4E1oqwffZSWRfltYXJa89Icn+fcWmOLGyWTiAg9j5UX4MoS2RaS3WyZG8xbZzbhoRqnujo8g==", "path": "hangfire.sqlserver/1.8.14", "hashPath": "hangfire.sqlserver.1.8.14.nupkg.sha512"}, "MediatR/12.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-0tLxCgEC5+r1OCuumR3sWyiVa+BMv3AgiU4+pz8xqTc+2q1WbUEXFOr7Orm96oZ9r9FsldgUtWvB2o7b9jDOaw==", "path": "mediatr/12.4.1", "hashPath": "mediatr.12.4.1.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-J145j2LgD4kkkNkrf5DW/pKzithZRKN5EFY+KAO3SqweMyDfv4cgKgtOIsv2bhrOLGqPJixuZkZte7LfK1seYQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.8", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.8.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uu8dfrsx081cSbEevWuZAvqdmANDGJkbLBL2G3j0LAZxX1Oy8RCVAaC4Lcuak6jNicWP6CWvHqBTIEmQNSxQlw==", "path": "microsoft.data.sqlclient/5.0.1", "hashPath": "microsoft.data.sqlclient.5.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-y0X5MxiNdbITJYoafJ2ruaX6hqO0twpCGR/ipiDOe85JKLU8WL4TuAQfDe5qtt3bND5Je26HnrarLSAMMnVTNg==", "path": "microsoft.data.sqlclient.sni.runtime/5.0.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.0.1.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "path": "microsoft.extensions.logging.abstractions/8.0.1", "hashPath": "microsoft.extensions.logging.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-IbQUEZr/LxxpPxkXDmKaemMeQNPjdHfk87HtTsI18a3RVgad0NOJSRaJ20hcesqL45PLcpQHR8xrPP7wZKbFQQ==", "path": "microsoft.extensions.objectpool/6.0.3", "hashPath": "microsoft.extensions.objectpool.6.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24518.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vqk6n5xgL41dmUUT57iela7UkyGykWGoP/xGDxjx6Kfx9amVRnXma4Me7zwz15I5NUra6xPkBCLYArZ1jHhsyQ==", "path": "microsoft.extensions.vectordata.abstractions/9.0.0-preview.1.24518.1", "hashPath": "microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.24518.1.nupkg.sha512"}, "Microsoft.Identity.Client/4.66.1": {"type": "package", "serviceable": true, "sha512": "sha512-mE+m3pZ7zSKocSubKXxwZcUrCzLflC86IdLxrVjS8tialy0b1L+aECBqRBC/ykcPlB4y7skg49TaTiA+O2UfDw==", "path": "microsoft.identity.client/4.66.1", "hashPath": "microsoft.identity.client.4.66.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OData.Client/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TN0F9ulVEMN4Kjwyo4qYAWEwXC/M2kSPQPc4jVks3nTHbU6NAFa0vE0B+67XOaf0DobvGvsmTgUqMnSGm3tfcw==", "path": "microsoft.odata.client/8.1.0", "hashPath": "microsoft.odata.client.8.1.0.nupkg.sha512"}, "Microsoft.OData.Core/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-8BBwX+FQ5XryKCMp7rgw6h7eTszg2dgEJi1++FPJpbwk11I4zpUaBdC5vAba03RUTXOsIMTLrx04RJJXYe1ycA==", "path": "microsoft.odata.core/8.1.0", "hashPath": "microsoft.odata.core.8.1.0.nupkg.sha512"}, "Microsoft.OData.Edm/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-X3Qm/fQQvnVlrYHqjcuR+V9P89Oh/l8ymyJNmwhZHWxkTAb3y56DYpgmMRB17WWVRmBxb/VYeFnsMSCijbgaLw==", "path": "microsoft.odata.edm/8.1.0", "hashPath": "microsoft.odata.edm.8.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.SemanticKernel/1.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-vNn8MuJYPEU5zLIvtpkunUS0PIAmaRUC6NJPj7rJqS41Sez0Sg8b7+kvv5zAXGkKslH8puTL2ALfCZ0L/c8E6A==", "path": "microsoft.semantickernel/1.24.1", "hashPath": "microsoft.semantickernel.1.24.1.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-uLS6LljMkF6Q6KyGF1bzvx601fUYGGpXJgGPmKZIBKDEOLWagicoWf0LRHqOSvN3AJcF4c1I9OZVC4OR5KZ2AA==", "path": "microsoft.semantickernel.abstractions/1.24.1", "hashPath": "microsoft.semantickernel.abstractions.1.24.1.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-Qx+AleMjXk5ZWU2tOHS4G3Ox73O3aITlqxJ09CEqMgqhei/ht+PWMzzzklyP4oKHpC9PRlgIAou8QwO4iRt6SA==", "path": "microsoft.semantickernel.connectors.azureopenai/1.24.1", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.24.1.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-l9zT7xYtRjYrynF+EJSDkAOHEi9AFGoxPurmLDzYOEToZKfAy3rI8NANAeeQ0PZmUo23BU87IQq0gz2gyob82w==", "path": "microsoft.semantickernel.connectors.openai/1.24.1", "hashPath": "microsoft.semantickernel.connectors.openai.1.24.1.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-dJRLAUiimUPGv40UXKVWjdLs0M4hfrClAyhN4V25oZgyyU7NZ74aK3MgZ/Lw6a7TxsvkC7C8wyNLeZrsXDjiZA==", "path": "microsoft.semantickernel.core/1.24.1", "hashPath": "microsoft.semantickernel.core.1.24.1.nupkg.sha512"}, "Microsoft.SemanticKernel.Plugins.Core/1.24.1-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-DAEJjQqPZf8Ofe1bTh8V5i72ZSOHs/FBGn3YAG+GKPWDj3huQl/f7hPYGKuRB+d+3KUhejJ7OEcHSrCBVlXk7w==", "path": "microsoft.semantickernel.plugins.core/1.24.1-alpha", "hashPath": "microsoft.semantickernel.plugins.core.1.24.1-alpha.nupkg.sha512"}, "Microsoft.Spatial/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-iEL7UpDP/nyNh8if1yJDPekBh5q16yvOhRnCD5RYSlKamHZZeOBa3Vo+/rlbYKb5R6zemBchcfGXrCUFtZCKPQ==", "path": "microsoft.spatial/8.1.0", "hashPath": "microsoft.spatial.8.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenAI/2.1.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-C9TLSmUvcORI5BIz9llR+/Sj7xJmFJY4CoejGamzBg77XJ8DXjAHEXvpJGtqH8toBDSXRPn+9DD3Rs946AhCCA==", "path": "openai/2.1.0-beta.1", "hashPath": "openai.2.1.0-beta.1.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SendGrid/9.29.3": {"type": "package", "serviceable": true, "sha512": "sha512-nb/zHePecN9U4/Bmct+O+lpgK994JklbCCNMIgGPOone/DngjQoMCHeTvkl+m0Nglvm0dqMEshmvB4fO8eF3dA==", "path": "sendgrid/9.29.3", "hashPath": "sendgrid.9.29.3.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.AspNetCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FAjtKPZ4IzqFQBqZKPv6evcXK/F0ls7RoXI/62Pnx2igkDZ6nZ/jn/C/FxVATqQbEQvtqP+KViWYIe4NZIHa2w==", "path": "serilog.aspnetcore/8.0.0", "hashPath": "serilog.aspnetcore.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nR0iL5HwKj5v6ULo3/zpP8NMcq9E2pxYA6XKTSWCbugVs4YqPyvaqaKOY+OMpPivKp7zMEpax2UKHnDodbRB0Q==", "path": "serilog.settings.configuration/8.0.0", "hashPath": "serilog.settings.configuration.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "path": "serilog.sinks.console/5.0.0", "hashPath": "serilog.sinks.console.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.MSSqlServer/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WAmP6bMPJ0zxWflBn3eXUXg3KtrjHeuBcWW9+1QSaYrA4EdlviZEJ7ciwEL0GzqIr4/y1WjmYm5vgpEPuVmYXw==", "path": "serilog.sinks.mssqlserver/6.5.0", "hashPath": "serilog.sinks.mssqlserver.6.5.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "starkbank-ecdsa/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog==", "path": "starkbank-ecdsa/1.3.3", "hashPath": "starkbank-ecdsa.1.3.3.nupkg.sha512"}, "Stripe.net/45.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-/Z7RrvZV3v7eTOZgxThWn1RJ6bVtL/m7ncRcXcheF7w8/lFHsL+DAxgaEPktq+M/dChCfkvcghrUYOrkc0BVQg==", "path": "stripe.net/45.14.0", "hashPath": "stripe.net.45.14.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "path": "swashbuckle.aspnetcore/6.4.0", "hashPath": "swashbuckle.aspnetcore.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Tensors/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fhODzTe9ON9IzmRfyVeA6L8yXOciMtpq1YufkRVBliggcVKZE+XDxqIn46+yF4PWR6wNPuDpXtPpuY86VcKxUA==", "path": "system.numerics.tensors/8.0.0", "hashPath": "system.numerics.tensors.8.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-30D6MkO8WF9jVGWZIP0hmCN8l9BTY4LCsAzLIe4xFSXzs+AjDotR7DpSmj27pFskDURzUvqYYY0ikModgBTxWw==", "path": "system.runtime.caching/5.0.0", "hashPath": "system.runtime.caching.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-HxozeSlipUK7dAroTYwIcGwKDeOVpQnJlpVaOkBz7CM4TsE5b/tKlQBZecTjh6FzcSbxndYaxxpsBMz+wMJeyw==", "path": "system.runtime.compilerservices.unsafe/4.6.0", "hashPath": "system.runtime.compilerservices.unsafe.4.6.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "AdminPortalBackend.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdminPortalBackend.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}