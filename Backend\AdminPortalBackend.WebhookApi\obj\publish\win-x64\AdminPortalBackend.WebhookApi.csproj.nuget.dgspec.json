{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj", "projectName": "AdminPortalBackend.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj", "projectName": "AdminPortalBackend.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Hangfire": {"target": "Package", "version": "[1.8.14, )"}, "Hangfire.MaximumConcurrentExecutions": {"target": "Package", "version": "[1.1.0, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.14, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.66.1, )"}, "Microsoft.KernelMemory": {"target": "Package", "version": "[0.92.241112.1, )"}, "Microsoft.KernelMemory.Core": {"target": "Package", "version": "[0.92.241112.1, )"}, "Microsoft.OData.Client": {"target": "Package", "version": "[8.1.0, )"}, "Microsoft.OData.Core": {"target": "Package", "version": "[8.1.0, )"}, "Microsoft.OData.Edm": {"target": "Package", "version": "[8.1.0, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.32.0, )"}, "Microsoft.SemanticKernel.Plugins.Core": {"target": "Package", "version": "[1.29.0-alpha, )"}, "Microsoft.Spatial": {"target": "Package", "version": "[8.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj", "projectName": "AdminPortalBackend.WebhookApi", "projectPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\obj\\publish\\win-x64\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj"}, "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.8, )"}, "SendGrid": {"target": "Package", "version": "[9.29.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.MSSqlServer": {"target": "Package", "version": "[6.5.0, )"}, "Stripe.net": {"target": "Package", "version": "[45.14.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}