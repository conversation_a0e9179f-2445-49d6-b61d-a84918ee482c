<!-- <div class="page-header">
  <h2>Connection List</h2>
  <button class="Btn" (click)="AddDialog()">Add Connections</button>
</div> -->

<div class="connection-type-container mb-2 pt-0">
  <button style="border: none;margin: 2px;" classs="m-2" mat-raised-button pTooltip="Add SQL Server Connection"
    tooltipPosition="top" class="connection-btn" (click)="chooseDataBase('sql')">
    <span><img src="../../../../assets/img/sql-logo.png" alt="SQL" style="width: 65px;"></span>
    <div>SQL</div>
  </button>

  <ng-template #tooltipContent style="background-color: var(--bg-color) !important;">
    <div class="flex align-items-center ">
      <span> </span>
    </div>
  </ng-template>

  <button style="border: none; margin: 2px;" class="m-2" mat-raised-button class="connection-btn"
    (click)="chooseDataBase('oData')" pTooltip="Add Business Central Connection" tooltipPosition="top">
    <span><img src="../../../../assets/img/business-central-logo.png" alt="BC" style="width: 65px;"></span>
    <div>BC</div>
  </button>

  <button style="border: none; margin: 2px;" class="m-2" mat-raised-button class="connection-btn"
    (click)="chooseDataBase('nav')" pTooltip="Add NAV Connection" tooltipPosition="top">
    <span><img src="../../../../assets/img/nav.png" alt="BC" style="width: 65px;"></span>
    <div>NAV</div>
  </button>

  <!-- CSV Button -->
  <button style="border: none; margin: 2px;  margin-left: 20px;" class="m-2" mat-raised-button class="connection-btn"
    (click)="chooseDataBase('csv')" pTooltip="Add CSV Connection" tooltipPosition="top" [class.disabled]="true">
    <span><img src="../../../../assets/img/csv.png" alt="CSV" style="width: 65px;"></span>
    <div>CSV</div>
  </button>

  <!-- Excel Button -->
  <button style="border: none; margin: 2px;" class="m-2" mat-raised-button class="connection-btn"
    (click)="chooseDataBase('excel')" pTooltip="Add Excel Connection" tooltipPosition="top" [class.disabled]="true">
    <span><img src="../../../../assets/img/Excel.png" alt="Excel" style="width: 65px;"></span>
    <div>Excel</div>
  </button>

  <!-- SharePoint Button -->
  <button style="border: none; margin: 2px;" class="m-2" mat-raised-button class="connection-btn"
    (click)="chooseDataBase('sharepoint')" pTooltip="Add SharePoint Connection" tooltipPosition="top"
    [class.disabled]="true">
    <span><img src="../../../../assets/img/Sharepoint.png" alt="SharePoint" style="width: 65px;"></span>
    <div>SharePoint</div>
  </button>

</div>
<div class="card">
  @if (isDataLoaded) {

  <p-table [value]="tableData" [rows]="10" [paginator]="tableData.length > 10" [showCurrentPageReport]="false"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [rowsPerPageOptions]="[10, 25]">

    <ng-template pTemplate="header">
      <tr>
        <th>Connection Name</th>
        <th>Description</th>
        <th>Type</th>
        <th>Action </th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-item>
      <tr>

        <td><img *ngIf="item.type == 'Ms SQL Server'" src="../../../../assets/img/sql-logo.png" alt="SQL"
            style="width: 25px;">
          <img
            *ngIf="(item.type == 'BCODataWebService' || item.type == 'BCODataRestApiService') && !isNav(item.connectionCredJson)"
            src="../../../../assets/img/business-central-logo.png" alt="BC" style="width: 25px;">
          <img *ngIf="isNav(item.connectionCredJson)" src="../../../../assets/img/nav.png" alt="BC"
            style="width: 25px;"> {{ item.connectionName }}
        </td>
        <td>{{ item.description }}</td>
        <td>{{ fetchType(item.type) }}</td>
        <td>
          <button class="action-button edit-button" style=" color: var(--bg-color);" (click)="editItem(item)"
            title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="action-button delete-button" (click)="deleteClicked(item.guid)" title="Delete"
            style="margin-left: 5px;">
            <i class="fas fa-trash-alt"></i>
          </button>
        </td>

      </tr>


    </ng-template>

    <ng-template pTemplate="paginatorleft">
      <p-button type="button" icon="pi pi-plus" styleClass="p-button-text" />
    </ng-template>
    <ng-template pTemplate="paginatorright">
      <p-button type="button" icon="pi pi-cloud" styleClass="p-button-text" />
    </ng-template>




  </p-table>

}@else {
  <div style="    height: 45vh;
  display: flex
;
  align-items: center;
  justify-content: center;">

    <span  class="loader"><i class="pi pi-spin pi-spinner"
      style="font-size: 5rem"></i></span>
  </div>
  }
</div>
