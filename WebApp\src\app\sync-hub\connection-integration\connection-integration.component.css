/* Add this CSS to your styles.css or component-specific CSS file */
.disabled-row {
  background-color: #f0f0f0;
  /* Example light gray for disabled rows */
  color: #a0a0a0;
  /* Optional: make text color lighter */
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: 0 20px 20px 0;
}

.page-header h2 {
  margin: 0;
}

.page-heading {
  font-size: 25px;
  font-weight: 500;
  color: #333;
}

.add-button {
  padding: 8px 16px;
  font-size: 16px;
  color: white;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: #0056b3;
}

.card {
  background: #ffffff !important;
}

.video-tab-label {
  display: inline-block;
  background-color: #e0f7fa;
  padding: 5px 10px;
  border-radius: 5px;
}

.activeBtn {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
}

/* Optional: Add hover effects */
/* .activeBtn:hover {
  background-color: rgba(0, 0, 0, 0.1); /* Light background on hover */
/* } */


.action-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
}

.edit-button {
  color: var(--bg-color);
}

/* .edit-button:hover {
  background-color: var(--hover-color);
  color: white;
} */

.delete-button {
  color: #f44336;
}

/*
.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
} */

.action-button i {
  font-size: 20px;
}


.Btn {
  font-size: 16px;
  font-weight: 400;
  padding: 6px 10px;
}

/* Add executing row styles */
.executing-row {
  background-color: rgba(144, 238, 144, 0.2) !important;
}
