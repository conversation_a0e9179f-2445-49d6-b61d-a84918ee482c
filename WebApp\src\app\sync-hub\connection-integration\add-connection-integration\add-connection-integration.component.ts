import { Component, CUSTOM_ELEMENTS_SCHEMA, Host, OnInit } from '@angular/core';
import { DbConnectionServiceProxy, DbConnectionDto, ConnectionIntegrationServiceProxy, ConnectionIntegration, CreateConnectionIntegrationDto } from '../../../../shared/service-proxies/service-proxies';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatStepperModule } from '@angular/material/stepper';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { concat } from 'rxjs';
import { TableColumnMappingDialogComponent } from '../../dialog/table-column-mapping-dialog/table-column-mapping-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { ButtonModule } from 'primeng/button';
import { StepperModule } from 'primeng/stepper';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';

import { StepsModule } from 'primeng/steps';
import { TabViewModule } from 'primeng/tabview';
import { Location } from '@angular/common';

import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { ToasterService } from '../../../toaster.service';

@Component({
  selector: 'app-add-connection-integration',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatStepperModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatChipsModule,
    MatIconModule,
    StepperModule,
    StepperModule,
    ButtonModule,
    InputTextModule,
    ToggleButtonModule,
    IconFieldModule,
    InputIconModule,
    CommonModule,
    MultiSelectModule,
    DropdownModule,
    TableModule,
    StepsModule,
    TabViewModule,
    ToastModule
  ],
  templateUrl: './add-connection-integration.component.html',
  styleUrls: ['./add-connection-integration.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AddConnectionIntegrationComponent implements OnInit {
  active: number | undefined = 0;
  connectionData: CreateConnectionIntegrationDto = new CreateConnectionIntegrationDto();
  connections: DbConnectionDto[] = [];
  sourceConnectionName: string = '';
  destinationConnectionName: string = '';
  isSaving = false

  // Source Variables
  sourceConnection: DbConnectionDto;
  sourceDatase: string = '';
  sourceTable: string = '';
  sourceColumns: string[] = [];
  SourceGuid: string | null = null;

  // Destination Variables
  destinationConnection: DbConnectionDto;
  destinationDatase: string = '';
  destinationTable: string = '';
  destinationColumns: string[] = [];
  destinationGuid: string | null = null;

  jobTypes: string[] = ['On Demand', 'Hourly', 'Daily', 'Weekly'];

  databases: string[] = [];
  destinationDatabases: string[] = [];
  tables: string[] = [];
  destinationTables: string[] = [];
  columnMappings: Array<{ source: string, destination: string }> = [{ source: '', destination: '' }];

  sourceMappingsTable: Array<{ sourceTable: string, destinationTable: string }> = [];
  guid: string | null = null;
  dropdownLoading = {
    conections: false,
    database: false,
    tables: false,
    destinationDatabase: false,

  }

  constructor(
    private _dbconnectionServices: DbConnectionServiceProxy,
    private _connectionIntegrationService: ConnectionIntegrationServiceProxy,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private location: Location, private toasterService: ToasterService
  ) { }

  async ngOnInit() {
    this.guid = this.route.snapshot.paramMap.get('guid');
    await this.loadConnection();

  }

  goBack() {
    this.location.back()
  }

  sourcesTableLists: string[] = [];
  sourceSelectedTables: string;


  previousSelection: string[] = [];

  addTables(selectedTables: string[]) {
    const [newTable] = selectedTables.filter(item => !this.previousSelection.includes(item));

    if (newTable && !this.sourcesTableLists.includes(newTable)) {
      this.sourcesTableLists.push(newTable);
    }

    this.previousSelection = [...selectedTables];
    this.sourcesTableLists = this.previousSelection

  }


  remove(table: any) {
    const index = this.sourcesTableLists.indexOf(table);
    if (index >= 0) {
      this.sourcesTableLists.splice(index, 1);
      //console.log(`Table "${table}" removed successfully.`);
    }
  }

  // Method to add a single table from Angular Material Select
  addTable(table: string) {
    if (table && !this.sourcesTableLists.includes(table)) {
      this.sourcesTableLists.push(table);
    }
  }
  removetable(index: number): void {
    this.sourceMappingsTable.splice(index, 1);
  }

  connectionMappingDataArray = [];

  mappingTable(item: any, index: number) {
    const dialogRef = this.dialog.open(TableColumnMappingDialogComponent, {
      maxHeight: '80vh',
      width: '750px',
      disableClose: true,
      data: {
        sourceTable: item.sourceTable,
        destinationTable: item.destinationTable,
        sourceGuid: this.SourceGuid,
        sourceDataBase: this.sourceDatase,
        destinationGuid: this.destinationGuid,
        destinationDataBase: this.destinationDatase,
        mappingData: this.connectionMappingDataArray[index]
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const mappingArray = result.columnMappings.map(item => ({
          source: item.source,
          destination: item.destination
        }));

        if (mappingArray.length === 0) {
          //console.error('Mapped Columns are required.');
          return;
        }

        //console.log(result)
        //console.log(mappingArray)
        const mappingObject = mappingArray.reduce((acc, item) => {
          if (item.source && item.destination) {
            acc[item.source] = item.destination;
          }
          return acc;
        }, {} as { [key: string]: string });

        //console.log(mappingObject)

        // Prepare connection data
        const connectionData = {
          sourcePrimaryKey: result.sourcePrimaryKey,
          destinationPrimaryKey: result.destinationPrimaryKey,
          mappedColumns: JSON.stringify(mappingObject) // Directly use the mapping array
        };


        // Push the connection data to the array
        this.connectionMappingDataArray[index] = connectionData;

        // Log the current connection mapping data array
        //console.log('Current Connection Data Array:', this.connectionMappingDataArray);
      }
    });
  }



  loadDatabases(): void {
    if (this.SourceGuid) {
      this.dropdownLoading.database = true
      this._connectionIntegrationService.getDatabase(this.SourceGuid).subscribe(
        (response) => {
          this.databases = response.message;
          this.dropdownLoading.database = false
        },
        (error) => {
          //console.error('Error fetching source databases:', error);
        }
      );
    }
    if (this.destinationGuid) {
      this.dropdownLoading.destinationDatabase = true
      this._connectionIntegrationService.getDatabase(this.destinationGuid).subscribe(
        (response) => {
          this.destinationDatabases = response.message;
          this.dropdownLoading.destinationDatabase = false
        },
        (error) => {
          //console.error('Error fetching destination databases:', error);
        }
      );
    }
  }

  loadSourceColumns(): void {

    if (this.sourceDatase && this.sourceTable) {
      this._connectionIntegrationService.getColumn(this.SourceGuid, this.sourceDatase, this.sourceTable).subscribe(
        (res) => {
          this.sourceColumns = res.message;
        },
        (error) => {
          //console.error('Error fetching source columns:', error);
        }
      );
    }
  }

  loadDestinationColumns(): void {
    //console.log(this.destinationDatase);
    //console.log(this.destinationTable);
    //console.log(this.destinationGuid);


    if (this.destinationDatase && this.destinationTable) {
      this._connectionIntegrationService.getColumn(this.destinationGuid, this.destinationDatase, this.destinationTable).subscribe(
        (res) => {
          this.destinationColumns = res.message;

          if (this.guid == '00000000000000') {
            const sourceColumns = this.sourceColumns; // Assuming this.sourceColumns is an array of column names
            const destinationColumns = this.destinationColumns; // Assuming this.destinationColumns is also an array of column names

            // Finding common columns
            const commonColumns = sourceColumns.filter(column => destinationColumns.includes(column));

            // Logging the common columns
            //console.log('Common Columns:', commonColumns);

            commonColumns.forEach(x => {
              this.columnMappings.push({ source: x, destination: x });
            })

          }


        },
        (error) => {
          //console.error('Error fetching destination columns:', error);
        }
      );
    }
  }

  async loadConnection() {
    this.dropdownLoading.conections = true
    this.connections = (await this._dbconnectionServices.getAll().toPromise()).filter(x => x.type.toLowerCase() == 'ms sql server');
    this.dropdownLoading.conections = false
  }

  onSourceConnectionSelected(selectedConnection: DbConnectionDto): void {
    //console.log(selectedConnection);

    if (selectedConnection && selectedConnection.guid) {
      this.sourceConnectionName = selectedConnection.connectionName;
      this.SourceGuid = selectedConnection.guid;
      this.loadDatabases();
    } else {
      //console.error('No valid connection selected for source.');
    }
  }

  onSourceDatabaseSelected(selectedDatabase: string): void {
    this.sourceDatase = selectedDatabase;
    this.sourceColumns = [];
    this.sourceSelectedTables = ''

    if (selectedDatabase && this.SourceGuid) {
      this.dropdownLoading.tables = true
      this._connectionIntegrationService.getTables(this.SourceGuid, selectedDatabase).subscribe(
        (res) => {
          this.tables = res.message;
          this.dropdownLoading.tables = false
        },
        (error) => {
          //console.error('Error fetching source tables:', error);
        }
      );
    }

  }

  onSourceTableSelected(selectedTable: string): void {
    this.sourceTable = selectedTable;
    this.sourceColumns = [];
    this.loadSourceColumns();
  }

  clearDestinationColumn() {
    this.sourceMappingsTable = []
    this.destinationDatase = ''
  }

  onDestinationConnectionSelected(selectedConnection: DbConnectionDto): void {
    if (selectedConnection && selectedConnection.guid) {
      this.destinationConnectionName = selectedConnection.connectionName;
      this.destinationGuid = selectedConnection.guid;
      this.loadDatabases();
    } else {
      //console.error('No valid connection selected for destination.');
    }
  }
  onDestinationDatabaseSelected(selectedDatabase: string): void {

    this.destinationDatase = selectedDatabase;
    this.destinationColumns = [];
    this.sourceMappingsTable = []
    if (selectedDatabase && this.destinationGuid) {
      this._connectionIntegrationService.getTables(this.destinationGuid, selectedDatabase).subscribe(
        (res) => {
          this.destinationTables = res.message || [];

          this.destinationTables.push('Create New');

          if (this.sourceDatase != this.destinationDatase) {
            // Finding common columns
            const commonTables = this.sourcesTableLists.filter(column => this.destinationTables.includes(column));

            // Logging the common columns
            //console.log('Common Columns:', commonTables);

            commonTables.forEach(x => {
              this.sourceMappingsTable.push({ sourceTable: x, destinationTable: x });
            })

            const otherTables = this.sourcesTableLists.filter(column => !this.destinationTables.includes(column));

            otherTables.forEach(x => {
              this.sourceMappingsTable.push({ sourceTable: x, destinationTable: 'Create New' });
            })
          } else {


            this.sourcesTableLists.forEach(x => {
              this.sourceMappingsTable.push({ sourceTable: x, destinationTable: 'Create New' });
            })
          }
          //console.log(this.destinationTables);

          //this.destinationTables = this.destinationTables.filter(t => !this.sourcesTableLists.includes(t))
          //console.log(this.destinationTables);



          //console.log(this.sourceMappingsTable)
        },
        (error) => {
          //console.error('Error fetching destination tables:', error);
        }
      );
    }
  }

  onDestinationTableSelected(selectedTable: string): void {
    this.destinationTable = selectedTable;
    this.destinationColumns = [];
    this.loadDestinationColumns();
  }

  async save() {
    try {
      this.isSaving = true
      // Assign the source connection details
      this.connectionData.sourceConnectionGuid = this.SourceGuid || ''; // Use empty string if no SourceGuid
      this.connectionData.sourceConnectionName = this.sourceConnectionName;
      this.connectionData.sourceDatabase = this.sourceDatase;
      this.connectionData.sourceTable = this.sourcesTableLists;
      // Assign the destination connection details
      this.connectionData.destinationConnectionGuid = this.destinationGuid || '';
      this.connectionData.destinationConnectionName = this.destinationConnectionName;
      this.connectionData.destinationDatabase = this.destinationDatase;
      this.connectionData.destinationTable = this.sourceMappingsTable.map(x => x.destinationTable);
      // Prepare arrays for mapped columns and primary keys
      const mappedColumnsArray = this.connectionMappingDataArray.map(item => item.mappedColumns);
      const sourcePrimaryKeysArray = this.connectionMappingDataArray.map(item => item.sourcePrimaryKey).flat();
      const destinationPrimaryKeysArray = this.connectionMappingDataArray.map(item => item.destinationPrimaryKey).flat();
      // Assign prepared data to connectionData
      this.connectionData.mappedColumns = mappedColumnsArray; // Will be an array of arrays
      this.connectionData.sourcePrimaryKey = sourcePrimaryKeysArray; // Flattened array of source primary keys
      this.connectionData.destinationPrimaryKey = destinationPrimaryKeysArray; // Flattened array of destination primary keys

      //console.log(this.connectionData);

      var existRes = await this._connectionIntegrationService.checkTableExist(this.connectionData.destinationConnectionGuid, this.connectionData.destinationDatabase, this.connectionData.destinationTable).toPromise();

      // if (existRes.message.length) {
      //   this.toasterService.showToaster('error', existRes.message.join(', ') + ' already exists');
      //   this.isSaving = false
      //   return;
      // }
      // Call the API to save the connection integration data
      this._connectionIntegrationService.create(this.connectionData).subscribe(
        (res) => {
          //console.log('Integration saved successfully:', res);
          if (!res.isError) {
            this.toasterService.showToaster('success', res.message)
            this.isSaving = false
            // Navigate to another page after successful save
            this.router.navigate(['./sync-hub/connection-integration']);
          } else {
            this.isSaving = false
            this.toasterService.showToaster('error', res.message)
          }
        },
        (error) => {
          this.isSaving = false
          this.toasterService.showToaster('error', error.message)
        }
      )
    } catch (error) {
      this.isSaving = false
      this.toasterService.showToaster('Error: ', error.message)
    }
    // (error) => {
    //   //console.error('Error saving integration:', error);
    // }
    // );
  }

}
