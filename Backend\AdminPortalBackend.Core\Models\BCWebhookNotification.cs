﻿namespace AdminPortalBackend.Core.Models;
public class BCWebhookNotification
{
    public int Id { get; set; }
    public string SubscriptionId { get; set; }
    public string ClientState { get; set; }
    public DateTimeOffset? ExpirationDateTime { get; set; }
    public string Resource { get; set; }
    public string ChangeType { get; set; }
    public DateTimeOffset? LastModifiedDateTime { get; set; }
    public string Status { get; set; }
}