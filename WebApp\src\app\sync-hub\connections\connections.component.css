

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.page-heading {
  font-size: 25px;
  font-weight: 500;
  color: #333;
}

.add-button {
  padding: 8px 16px;
  font-size: 16px;
  color: white;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: #0056b3;
}

.card {
  background: #ffffff !important;
}

.video-tab-label {
  display: inline-block;
  background-color: #e0f7fa;
  padding: 5px 10px;
  border-radius: 5px;
}

.action-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
}

.edit-button {
  color: rgb(30, 94, 255);
}

.edit-button:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

.delete-button {
  color: #f44336;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.action-button i {
  font-size: 20px;
}

.connection-btn.disabled {
  pointer-events: none;   /* Prevent button clicks */
  opacity: 0.2;           /* Grayed out effect */
  background-color: #f0f0f0; /* Optional: make background lighter */
}
