.integration-form {
  max-width: 800px; /* Set a maximum width for the form */
  margin: 0px 0; /* Center the form */
  padding: 20px; /* Inner padding */
  border-radius: 10px; /* Rounded corners */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
  background-color: #ffffff; /* White background */
  font-family: Arial, sans-serif; /* Clean font */
}

.mapping-section {
  display: flex; /* Flex layout for better alignment */
  align-items: center; /* Center items vertically */
  margin-bottom: 15px; /* Space between mapping sections */
}

.delete-btn {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.delete-btn:hover {
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}
.border-white{
  border-color: white !important;
}
.btn.action-btn {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
  font-size: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  width: 35px;
}
.p-dropdown {

  width: 90%;
}
.mapping-cell {
  flex: 1; /* Each cell takes equal width */
  margin-right: 10px; /* Space between cells */
}

.mapping-cell:last-child {
  margin-right: 0; /* No margin for the last cell */
}

.custom-dropdown {
  width: 100%; /* Full width for dropdowns */
}

.btn {
  transition: background-color 0.3s, transform 0.2s; /* Smooth transition */
}

.btn:hover {
  transform: translateY(-2px); /* Lift effect on hover */
}

.save-btn {
  background-color: var(--bg-color); /* Button background color */
  color: white; /* Button text color */
  padding: 12px 20px; /* Padding for button */
  border: none; /* No border */
  border-radius: 5px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor on hover */
  width: 100%; /* Full width for the button */
}

.save-btn:hover {
  background-color: #0056b3; /* Darker shade on hover */
}

.same-width {
  width: 100%; /* Apply uniform width to each dropdown container */
}

::ng-deep .p-dropdown {
  width: 100% !important; /* Ensure the dropdown itself takes full width */
}
.p-dropdown {
  width: 95%;
  display: inline-flex;
  cursor: pointer;
  position: relative;
  -webkit-user-select: none;
  user-select: none;
}

.mapping-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}


/*
.loader {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
  border-top: 4px solid #000000;
  border-right: 4px solid transparent;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
.loader::after {
  content: '';
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border-bottom: 4px solid var(--bg-color);
  border-left: 4px solid transparent;
}
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} */

::ng-deep .p-inputtext{
  width: 100%;
  padding: 0.25rem 0.25rem;
  font-size: 0.9rem;
}

::ng-deep   .p-multiselect {
  width: 100%;
  padding: 0.25rem 0.25rem;
}

::ng-deep .p-multiselect .p-multiselect-label {
  width: 100%;
  padding:0;
  font-size: 0.9rem;
}
