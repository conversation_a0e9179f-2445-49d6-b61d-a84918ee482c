import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ServiceProxyModule } from '../../shared/service-proxies/service-proxy.module';
import { SyncHubComponent } from './sync-hub.component';
import { ConnectionsComponent } from './connections/connections.component';
import { ConnectionIntegrationComponent } from './connection-integration/connection-integration.component';
import { AddConnectionIntegrationComponent } from './connection-integration/add-connection-integration/add-connection-integration.component';
import { SearchingComponent } from '../searching/searching.component';
import { BcSqlComponent } from './bc-sql/bc-sql.component';
import { UpdateConnectionIntegrationComponent } from './connection-integration/update-connection-integration/update-connection-integration.component';
import { BcSqlListComponent } from './bc-sql-list/bc-sql-list.component';
import { UpdateBcSqlListComponent } from './bc-sql-list/update-bc-sql-list/update-bc-sql-list.component';
import { SettingsComponent } from './settings/settings.component';

@NgModule({
  declarations: [],
  imports: [
    RouterModule.forChild([
      {
        path: "",
        children: [
          {
            path: '', component: SyncHubComponent,
            // canActivate: [UserGuard],
            children: [
              { path: "", redirectTo: "connection", pathMatch: "full" },
              { path: 'sync-hub', component: SearchingComponent, },
              { path: 'connection', component: ConnectionsComponent },
              { path: 'connection-integration', component: ConnectionIntegrationComponent },
              { path: 'connection-integration/:guid', component: UpdateConnectionIntegrationComponent },
              { path: 'add-connection-integration', component: AddConnectionIntegrationComponent },
              { path: 'bc-sql', component: BcSqlComponent },
              { path: 'settings', component: SettingsComponent },
              { path: 'bc-sql/:guid', component: UpdateBcSqlListComponent },
              {path:'bc-sql-list',component:BcSqlListComponent}

            ],
          },
          { path: "**", redirectTo: "" },
        ]
      },
    ]),
    CommonModule, ServiceProxyModule
  ]
})
export class SyncHubModule { }
