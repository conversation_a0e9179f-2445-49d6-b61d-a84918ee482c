import { Component, Inject } from '@angular/core';
import {
  Mat<PERSON><PERSON>ogContent,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { SqlConnectionDialogComponent } from '../sql-connection-dialog/sql-connection-dialog.component';
import { ODataConnectionDialogComponent } from '../o-data-connection-dialog/o-data-connection-dialog.component';

@Component({
  selector: 'app-connection-type-dialog',
  standalone: true,
  imports: [MatFormFieldModule, MatInputModule, FormsModule, MatButtonModule, MatDialogContent, MatIcon],

  templateUrl: './connection-type-dialog.component.html',
  styleUrl: './connection-type-dialog.component.css'
})
export class ConnectionTypeDialogComponent {


  constructor(private dialog: MatDialog, private dialogRef: MatDialogRef<ConnectionTypeDialogComponent>) { }
  chooseDataBase(dataBase: string) {
    //console.log(dataBase)
    if (dataBase == 'sql') {
      this.SqlConnection()
    } else {
      this.odataConnection()
    }
  }


  onClose(): void {
    this.dialogRef.close();
  }
  // create the conenction to the sql
  SqlConnection(videoData?: any): void {
    const dialogRef = this.dialog.open(SqlConnectionDialogComponent, {
      width: '500px',
      height: '600px',
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        //console.log(result);
        this.dialogRef.close(result);

      }
    });
  }

  // create the conenction to the oData
  odataConnection(videoData?: any): void {
    const dialogRef = this.dialog.open(ODataConnectionDialogComponent, {
      width: '400px',
      height: '600px',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        //console.log(result);
        this.dialogRef.close(result);

      }
    });
  }



}
