﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Features
{
    public class MapColumns
    {
        public List<string> SourceColumns { get; set; }
        public List<string> DestinationColumns { get; set; }
    }

    public class MappedColumn
    {
        [JsonPropertyName("source")]      // Ensure proper mapping to JSON key "source"
        public string Source { get; set; }

        [JsonPropertyName("destination")] // Ensure proper mapping to JSON key "destination"
        public string Destination { get; set; }
    }
    public class CreateViewDto
    {
        public Guid ConnectionGuid { get; set; }
        public string DatabaseName { get; set; }
        public string ViewName { get; set; }
        public string TableName { get; set; }
        public List<MappedColumn> MappedColumns { get; set; }
    }

    public class IsViewDto
    {
        public Guid ConnectionGuid { get; set; }
        public string DatabaseName { get; set; }
        public string ViewName { get; set; }
    }
    }
