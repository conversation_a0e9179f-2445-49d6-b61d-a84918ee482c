{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webapi\\controllers\\filecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|solutionrelative:adminportalbackend.webapi\\controllers\\filecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|solutionrelative:adminportalbackend.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\infrastructureserviceregistration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\infrastructureserviceregistration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\datastore\\syncmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\datastore\\syncmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\datastore\\datastorefactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\datastore\\datastorefactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\aiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\aiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\signalrhub\\messagehub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\signalrhub\\messagehub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\adminportalbackend.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\adminportalbackend.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\datastore\\sqlserverdatastore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\datastore\\sqlserverdatastore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\coreserviceregistration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\coreserviceregistration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\profiles\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\profiles\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\extensions\\dictionaryextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\extensions\\dictionaryextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\extensions\\connectionintegrationextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\extensions\\connectionintegrationextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webapi\\adminportalbackend.webapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|solutionrelative:adminportalbackend.webapi\\adminportalbackend.webapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\contracts\\features\\responsemessagelist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\contracts\\features\\responsemessagelist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\contracts\\features\\responsemessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\contracts\\features\\responsemessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webapi\\controllers\\connectionintegrationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5EEE4481-18A3-4B7F-AF72-72328B89E151}|AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj|solutionrelative:adminportalbackend.webapi\\controllers\\connectionintegrationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\repositiories\\iconnectionintegrationrepositoy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\repositiories\\iconnectionintegrationrepositoy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\repositories\\connectionintegrationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\repositories\\connectionintegrationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\adminportalbackend.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\adminportalbackend.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webhookapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|solutionrelative:adminportalbackend.webhookapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webhookapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|solutionrelative:adminportalbackend.webhookapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webhookapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|solutionrelative:adminportalbackend.webhookapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.infrastructure\\repositories\\dbconnectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29EB4D98-E0F5-40FA-AF48-9DB05D2A7477}|AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj|solutionrelative:adminportalbackend.infrastructure\\repositories\\dbconnectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.webhookapi\\adminportalbackend.webhookapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{AE6689BE-8A6A-4735-9085-E8F2379163E0}|AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj|solutionrelative:adminportalbackend.webhookapi\\adminportalbackend.webhookapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|c:\\users\\<USER>\\source\\repos\\admin-portal\\backend\\adminportalbackend.core\\entities\\connectionintegration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{49D3FF86-7276-4D0E-8C3D-22866982237F}|AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj|solutionrelative:adminportalbackend.core\\entities\\connectionintegration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 34, "Children": [{"$type": "Document", "DocumentIndex": 18, "Title": "ConnectionIntegrationRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\Repositories\\ConnectionIntegrationRepository.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\Repositories\\ConnectionIntegrationRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\Repositories\\ConnectionIntegrationRepository.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\Repositories\\ConnectionIntegrationRepository.cs", "ViewState": "AgIAACkFAAAAAAAAAAAkwLkFAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-23T16:26:58.167Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 17, "Title": "IConnectionIntegrationRepositoy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Repositiories\\IConnectionIntegrationRepositoy.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Repositiories\\IConnectionIntegrationRepositoy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Repositiories\\IConnectionIntegrationRepositoy.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Repositiories\\IConnectionIntegrationRepositoy.cs", "ViewState": "AgIAABMAAAAAAAAAAAAxwBYAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-25T13:08:25.778Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 16, "Title": "ConnectionIntegrationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\Controllers\\ConnectionIntegrationController.cs", "RelativeDocumentMoniker": "AdminPortalBackend.WebApi\\Controllers\\ConnectionIntegrationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\Controllers\\ConnectionIntegrationController.cs", "RelativeToolTip": "AdminPortalBackend.WebApi\\Controllers\\ConnectionIntegrationController.cs", "ViewState": "AgIAANMAAAAAAAAAAAAYwOEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-24T11:57:37.384Z", "IsPinned": true}, {"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:138:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:141:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:156:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:153:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:152:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:151:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:150:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:149:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:148:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:147:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:146:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:145:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:144:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:143:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:142:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:136:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:154:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:155:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:2132056560:0:{83107a3e-496a-485e-b455-16ddb978e55e}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "SyncManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\DataStore\\SyncManager.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\DataStore\\SyncManager.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\DataStore\\SyncManager.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\DataStore\\SyncManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-30T09:58:28.861Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "InfrastructureServiceRegistration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\InfrastructureServiceRegistration.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\InfrastructureServiceRegistration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\InfrastructureServiceRegistration.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\InfrastructureServiceRegistration.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAcwC4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T14:24:51.795Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "FileController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\Controllers\\FileController.cs", "RelativeDocumentMoniker": "AdminPortalBackend.WebApi\\Controllers\\FileController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\Controllers\\FileController.cs", "RelativeToolTip": "AdminPortalBackend.WebApi\\Controllers\\FileController.cs", "ViewState": "AgIAAGEAAAAAAAAAAAAQwH0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T17:30:20.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\Program.cs", "RelativeDocumentMoniker": "AdminPortalBackend.WebApi\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\Program.cs", "RelativeToolTip": "AdminPortalBackend.WebApi\\Program.cs", "ViewState": "AgIAABsAAAAAAAAAAAAAABwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T19:27:55.174Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "MessageHub.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\SignalRHub\\MessageHub.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\SignalRHub\\MessageHub.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\SignalRHub\\MessageHub.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\SignalRHub\\MessageHub.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-30T09:23:15.622Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "DataStoreFactory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\DataStore\\DataStoreFactory.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\DataStore\\DataStoreFactory.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\DataStore\\DataStoreFactory.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\DataStore\\DataStoreFactory.cs", "ViewState": "AgIAADEAAAAAAAAAAAAAwJgAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-12T09:29:35.828Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "AIService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AIService.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\AIService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AIService.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\AIService.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAIwH8AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-29T13:18:12.318Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "AdminPortalBackend.Infrastructure.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\AdminPortalBackend.Infrastructure.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-01-09T19:24:44.628Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "SqlServerDataStore.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\DataStore\\SqlServerDataStore.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\DataStore\\SqlServerDataStore.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\DataStore\\SqlServerDataStore.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\DataStore\\SqlServerDataStore.cs", "ViewState": "AgIAABEBAAAAAAAAAAAowBoBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T13:07:33.736Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "CoreServiceRegistration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\CoreServiceRegistration.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\CoreServiceRegistration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\CoreServiceRegistration.cs", "RelativeToolTip": "AdminPortalBackend.Core\\CoreServiceRegistration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T14:13:55.311Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "MappingProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Profiles\\MappingProfile.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Profiles\\MappingProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Profiles\\MappingProfile.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Profiles\\MappingProfile.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T05:54:19.544Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "DictionaryExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Extensions\\DictionaryExtensions.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Extensions\\DictionaryExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Extensions\\DictionaryExtensions.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Extensions\\DictionaryExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T14:46:45.055Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ConnectionIntegrationExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Extensions\\ConnectionIntegrationExtensions.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Extensions\\ConnectionIntegrationExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Extensions\\ConnectionIntegrationExtensions.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Extensions\\ConnectionIntegrationExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T14:46:43.809Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "AdminPortalBackend.WebApi.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj", "RelativeDocumentMoniker": "AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj", "RelativeToolTip": "AdminPortalBackend.WebApi\\AdminPortalBackend.WebApi.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-01-09T17:38:58.725Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "ResponseMessageList.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessageList.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessageList.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessageList.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessageList.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T17:28:58.366Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "ResponseMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessage.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessage.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Contracts\\Features\\ResponseMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T17:28:56.926Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "AdminPortalBackend.Core.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj", "RelativeToolTip": "AdminPortalBackend.Core\\AdminPortalBackend.Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-01-09T14:15:42.694Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\appsettings.Development.json", "RelativeDocumentMoniker": "AdminPortalBackend.WebhookApi\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\appsettings.Development.json", "RelativeToolTip": "AdminPortalBackend.WebhookApi\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-28T13:49:14.394Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\appsettings.json", "RelativeDocumentMoniker": "AdminPortalBackend.WebhookApi\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\appsettings.json", "RelativeToolTip": "AdminPortalBackend.WebhookApi\\appsettings.json", "ViewState": "AgIAAAoAAAAAAAAAAAAqwAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-28T13:49:11.524Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\Program.cs", "RelativeDocumentMoniker": "AdminPortalBackend.WebhookApi\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\Program.cs", "RelativeToolTip": "AdminPortalBackend.WebhookApi\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-28T13:48:59.353Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ConnectionIntegration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Entities\\ConnectionIntegration.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Core\\Entities\\ConnectionIntegration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Core\\Entities\\ConnectionIntegration.cs", "RelativeToolTip": "AdminPortalBackend.Core\\Entities\\ConnectionIntegration.cs", "ViewState": "AgIAABEAAAAAAAAAAAA4wBgAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-24T18:34:31.209Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "DbConnectionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\Repositories\\DbConnectionRepository.cs", "RelativeDocumentMoniker": "AdminPortalBackend.Infrastructure\\Repositories\\DbConnectionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.Infrastructure\\Repositories\\DbConnectionRepository.cs", "RelativeToolTip": "AdminPortalBackend.Infrastructure\\Repositories\\DbConnectionRepository.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAEwBMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-24T12:00:53.07Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "AdminPortalBackend.WebhookApi.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj", "RelativeDocumentMoniker": "AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admin-portal\\Backend\\AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj", "RelativeToolTip": "AdminPortalBackend.WebhookApi\\AdminPortalBackend.WebhookApi.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-12-26T15:16:28.372Z"}]}]}]}