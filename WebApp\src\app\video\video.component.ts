import { Component } from '@angular/core';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ChatComponent } from '../chat/chat.component';


@Component({
  selector: 'app-video',
  standalone: true,
  imports: [RouterOutlet, CommonModule, RouterLink, RouterLinkActive, ChatComponent
  ],
  templateUrl: './video.component.html',
  styleUrl: './video.component.css'
})
export class VideoComponent {


  isSidebarOpen = true;

  isChatOpen: boolean = false;

  toggleChat() {
    this.isChatOpen = !this.isChatOpen;
  }
  toggleSidebar() {
    console.log("hi")
    this.isSidebarOpen = !this.isSidebarOpen;
  }

}
