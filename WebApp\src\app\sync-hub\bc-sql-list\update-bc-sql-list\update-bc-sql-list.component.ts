import { Component, inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';

import { ButtonModule } from 'primeng/button';
import { StepperModule } from 'primeng/stepper';

import { InputSwitchModule } from 'primeng/inputswitch';

import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import {
  ATestServiceProxy,
  ConnectionIntegration,
  ConnectionIntegrationServiceProxy,
  CreateViewDto,
  DbConnectionDto,
  DbConnectionServiceProxy,
  IsViewDto,
  JobRequest,
  MappedColumn,
  ServiceProxy,
  SubscriptionRequest,
  WebhooksServiceProxy,
} from '../../../../shared/service-proxies/service-proxies';
import { ActivatedRoute, Route, Router } from '@angular/router';
import { BcSqlComponent } from '../../bc-sql/bc-sql.component';
import { CommonModule, Location } from '@angular/common';

import { RadioButtonModule } from 'primeng/radiobutton';
import { ToastModule } from 'primeng/toast';
import { ToasterService } from '../../../toaster.service';
import { SignalRService } from '../../../signalr.service';
import { IViewRefrence } from '../../../app.interface';
import { MatDialog } from '@angular/material/dialog';
import { AddViewDialogComponent } from '../../dialog/add-view-dialog/add-view-dialog.component';
import { SubSink } from 'subsink';

interface IIntegrationSettings {
  BcToSql: string;
  SqlToBc: boolean;
  ViewName: string;
}

@Component({
  selector: 'app-update-bc-sql-list',
  standalone: true,
  imports: [
    ButtonModule,
    StepperModule,
    FormsModule,
    DropdownModule,
    CommonModule,
    InputSwitchModule,
    RadioButtonModule,
    ToastModule,
  ],
  templateUrl: './update-bc-sql-list.component.html',
  styleUrl: './update-bc-sql-list.component.css',
})
export class UpdateBcSqlListComponent implements OnInit, OnDestroy {
  private subs = new SubSink();

  integrationSettings: IIntegrationSettings = {
    BcToSql: null,
    SqlToBc: false,
    ViewName: null,
  };
  isRealTimeChecked = false;
  selectedConnectionType: string;
  isSaving = false;
  isExecuting = false;
  isExecutingOnly = false
  isRefreshing = false
  isDropdownOpen: boolean = false;
  connections: DbConnectionDto[];
  constructor(
    private _dbConnectionService: DbConnectionServiceProxy,
    private route: ActivatedRoute,
    private _connectionIntegrationService: ConnectionIntegrationServiceProxy,
    private router: Router,
    private _webHookService: WebhooksServiceProxy,
    private toasterService: ToasterService,
    private singlarService: SignalRService,
    private dialog: MatDialog,
    private _aTestService: ATestServiceProxy,
  ) { }
  location = inject(Location)
  entityNames: string[] = [];
  guid: string | null;
  bcConnections: DbConnectionDto[] = [];
  sqlConnections: DbConnectionDto[] = [];
  destinationDatabases: string[] = [];
  companyNames: string[] = [];
  intergrationData: ConnectionIntegration = new ConnectionIntegration();
  bcConnectionName: string = '';
  selectedSourceConnection: DbConnectionDto;
  selectedDestinationConnection: DbConnectionDto;
  isNewSetup: boolean;
  oldDestinationTable: string;
  existingEntities: string[] = [];
  columnMappings: Array<{ source: string; destination: string }> = [
    { source: '', destination: '' },
  ];

  sourceColumns: string[];
  singlarMessage = [];

  date = new Date

  jobTypes: string[] = ['On Demand', 'Hourly', 'Daily', 'Weekly',];
  async ngOnInit() {
    this.guid = this.route.snapshot.paramMap.get('guid');

    if (this.guid) {
      await this.loadIntegrationData(this.guid);
      this.isNewSetup =
        this.intergrationData.jobFrequency == null ? true : false;
      //console.log(this.intergrationData);

      this.oldDestinationTable =
        this.intergrationData.sourceDatabase +
        '_' +
        this.intergrationData.sourceTable;
    }
    await this.loadConnection();
    await this.loadCompanies();
    await this.loadDatabases();
    await this.loadColumns();

    //console.log(this.integrationSettings);
    const mappedColumns = JSON.parse(this.intergrationData.mappedColumns);
    this.columnMappings = Object.keys(mappedColumns).map((sourceColumn) => ({
      source:
        this.selectedSourceConnection.type == 'BCODataWebService'
          ? sourceColumn
          : sourceColumn.charAt(0).toLowerCase() + sourceColumn.slice(1),
      destination: mappedColumns[sourceColumn],
    }));

    this.columnMappings.forEach((column, count) => {


      this.onSourceChange(column.source, count)
      this.onDestinationChange(column.destination, count)
    })



    this.bcConnectionName = this.bcConnections[0].connectionName;
    this.integrationSettings.BcToSql = this.integrationSettings.BcToSql;
    this.selectedConnectionType = this.selectedSourceConnection.type;

    if (
      this.integrationSettings.BcToSql == 'Real-time' &&
      this.selectedConnectionType == 'BCODataRestApiService'
    ) {
      this.isRealTimeChecked = true;
    }
    // if (this.selectedConnectionType === 'BCODataWebService') {
    //   this.integrationSettings.BcToSql = 'Clean Load';
    // }
    this.subs.add(
      this.singlarService.message$.subscribe(res => {
        if (res.type == 'SendMessage') {
          var messagePart = res.message.split('~');

          if (messagePart.length == 2 && messagePart[0] == this.guid) {
            const currentDate = new Date();
            const hours = String(currentDate.getHours()).padStart(2, '0');
            const minutes = String(currentDate.getMinutes()).padStart(2, '0');
            const seconds = String(currentDate.getSeconds()).padStart(2, '0');
            const time = `${hours}:${minutes}:${seconds}`;
            this.singlarMessage.unshift(`[${time}]  ${messagePart[1]}`);
          }
        }
      })
    );
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  goBack() {
    this.location.back()
  }
  clearMessage() {
    this.singlarMessage = []
  }

  async loadConnection() {
    var res = await this._dbConnectionService.getAll().toPromise();
    this.bcConnections = res.filter((r) => r.type.startsWith('BC'));
    this.selectedSourceConnection = this.bcConnections.find(
      (r) => r.guid == this.intergrationData.sourceConnectionGuid
    );
    this.sqlConnections = res.filter((r) => !r.type.startsWith('BC'));
    this.selectedDestinationConnection = this.sqlConnections.find(
      (r) => r.guid == this.intergrationData.destinationConnectionGuid
    );
  }
  async loadDatabases() {
    var res = await this._connectionIntegrationService
      .getDatabase(this.selectedDestinationConnection.guid)
      .toPromise();

    if (res.isError == false) {
      this.destinationDatabases = res.message;

    }
  }

  async loadCompanies() {
    var res = await this._connectionIntegrationService
      .getCompanyNames(this.selectedSourceConnection.guid)
      .toPromise();

    if (res.isError == false) {
      this.companyNames = res.message;
    }
    await this.LoadAllEntities();
  }
  async LoadAllEntities() {
    var res = await this._connectionIntegrationService
      .getEntityNames(this.selectedSourceConnection.guid)
      .toPromise();
    if (res.isError == false) {
      this.entityNames = res.message;
    }
  }
  async loadIntegrationData(guid: string) {
    this.intergrationData = await this._connectionIntegrationService
      .getSingle(guid)
      .toPromise();
    if (this.intergrationData.settings) {
      this.integrationSettings = JSON.parse(this.intergrationData.settings);
    }
  }

  sourceConnectionChange() {
    this.loadCompanies();
    this.selectedConnectionType = this.selectedSourceConnection.type;
    //console.log(this.selectedConnectionType);
  }
  destinationConnectionChange() {
    this.loadDatabases();
  }

  // Called when a BC to SQL option is selected
  onBcToSqlSelect(option: string): void {
    if (option === 'Real-time') {
      this.intergrationData.jobFrequency = 'Realtime';
      this.isRealTimeChecked = true;
      //console.log(this.intergrationData.jobFrequency);
    } else {
      this.isRealTimeChecked = false;
    }
  }

  // Called when the SQL to BC toggle is changed
  onSqlToBcToggle(isEnabled: boolean): void {
    this.integrationSettings.SqlToBc = isEnabled;
  }
  async executeJob(guid: string) {
    this.isExecutingOnly = true;
    try {
      let res = await this._connectionIntegrationService.executeJob(guid).toPromise();
      //console.log(res);
      if (!res.isError) {
        this.singlarMessage = [];
        this.isExecutingOnly = false;
      } else {
        this.toasterService.showToaster('error', res.message);
        return false;
      }
      return true;
    } catch (error) {
      //console.log(error);

      this.toasterService.showToaster('error', error.message);
      return false;
    } finally {
      this.isExecutingOnly = false;
    }
  }


  async loadColumns() {
    //console.log(this.intergrationData);

    var res = await this._connectionIntegrationService
      .getColumn(
        this.intergrationData.destinationConnectionGuid,
        this.intergrationData.destinationDatabase,
        this.intergrationData.destinationTable
      )
      .toPromise();
    //console.log(res);

    if (res.isError == false) {
      this.sourceColumns = res.message;
    }
  }

  addMapping(): void {
    this.columnMappings.push({ source: '', destination: '' });
  }

  removeMapping(index: number) {
    // Remove the destination column from the selectedDestinations list if it exists
    const removedDestination = this.columnMappings[index].destination;
    this.selectedDestinations = this.selectedDestinations.filter(col => col !== removedDestination);
    const removedSource = this.columnMappings[index].source;
    this.selectedSources = this.selectedSources.filter(col => col !== removedSource);

    // Now remove the mapping from the columnMappings array
    this.columnMappings.splice(index, 1);
  }
  selectedSources: string[] = []
  selectedDestinations: string[] = []
  private previousColumn: string | null = null;

  columnFocus(currentValue: string) {
    this.previousColumn = currentValue; // Store the current value before change
  }
  filteredSourceColumns(index: number) {
    return this.sourceColumns.filter(
      column =>
        !this.selectedSources.includes(column) ||
        this.columnMappings[index]?.source === column
    );

  }


  onSourceChange(newColumn: string, index: number) {
    // //console.log(newColumn, index);

    // //console.log(this.filteredSourceColumns(index));

    // Remove the previous column from selectedSources if it exists
    if (this.previousColumn) {
      this.selectedSources = this.selectedSources.filter(
        col => col !== this.previousColumn
      );
      // //console.log(this.selectedSources);
    }

    // Add the new column to the list
    if (newColumn && !this.selectedSources.includes(newColumn)) {
      this.selectedSources.push(newColumn);
    }

    // Update the mapping
    this.columnMappings[index].source = newColumn;

    // //console.log("Updated Selected Sources:", this.selectedSources);
    // //console.log("Current Mapping:", this.columnMappings);
  }


  filteredDestinationColumns(index: number) {
    // Allow columns that are not selected or the one currently selected for this row
    return this.sourceColumns.filter(
      column =>
        !this.selectedDestinations.includes(column) ||
        this.columnMappings[index]?.destination === column
    );
  }

  onDestinationChange(newColumn: string, index: number) {
    // Remove the previous column from selectedDestinations if it exists
    if (this.previousColumn) {
      this.selectedDestinations = this.selectedDestinations.filter(
        col => col !== this.previousColumn
      );
    }

    // Add the new column to the list
    if (newColumn && !this.selectedDestinations.includes(newColumn)) {
      this.selectedDestinations.push(newColumn);
    }

    // Update the mapping
    this.columnMappings[index].destination = newColumn;

    // //console.log("Updated Selected Destinations:", this.selectedDestinations);
    // //console.log("Current Mapping:", this.columnMappings);
  }


  async save() {
    try {
      //console.log(this.selectedSourceConnection.type);

      this.intergrationData.sourceConnectionGuid =
        this.selectedSourceConnection.guid;
      this.intergrationData.destinationConnectionGuid =
        this.selectedDestinationConnection.guid;
      this.intergrationData.sourceConnectionName =
        this.selectedSourceConnection.connectionName;
      this.intergrationData.destinationConnectionName =
        this.selectedDestinationConnection.connectionName;
      //console.log(this.integrationSettings);

      this.intergrationData.settings = JSON.stringify(this.integrationSettings);

      const mappingObject = this.columnMappings.reduce((acc, item) => {
        if (item.source && item.destination) {
          let source =
            this.selectedSourceConnection.type == 'BCODataWebService'
              ? item.source
              : item.source.charAt(0).toUpperCase() + item.source.slice(1);
          acc[source] = item.destination;
        }
        return acc;
      }, {} as { [key: string]: string });

      if (Object.keys(mappingObject).length === 0) {
        //console.error('Mapped Columns are required.');
        return false;
      }

      const jsonString = JSON.stringify(mappingObject);
      this.intergrationData.mappedColumns = jsonString;

      this.existingEntities = [];
      var newDestinationTable =
        this.intergrationData.sourceDatabase +
        '_' +
        this.intergrationData.sourceTable;
      if (this.oldDestinationTable != newDestinationTable) {
        var res = await this._connectionIntegrationService
          .checkTableExist(
            this.intergrationData.destinationConnectionGuid,
            this.intergrationData.destinationDatabase,
            [newDestinationTable]
          )
          .toPromise();
        if (res.message.length) {
          this.existingEntities = res.message;
          return false;
        } else {
          this.existingEntities = [];
        }
      }
      if (this.integrationSettings.BcToSql == 'Differential Load') {
        var isDiff = await this._connectionIntegrationService.isSystemRowVersionAvailable(this.intergrationData.sourceTable, this.intergrationData.sourceConnectionGuid).toPromise();
        if (isDiff.isError == true) {
          this.toasterService.showToaster('error', isDiff.message);
          return false;
        }
      }

      await this._connectionIntegrationService
        .editTableForODataWebService(this.intergrationData)
        .toPromise();
      if (
        this.integrationSettings.BcToSql == 'Real-time' &&
        this.selectedSourceConnection.type == 'BCODataRestApiService'
      ) {
        //add webhook
        const connectionCredObj = JSON.parse(
          this.selectedSourceConnection.connectionCredJson
        );
        var company = await this._connectionIntegrationService
          .getCompanyId(
            this.intergrationData.sourceConnectionGuid,
            this.intergrationData.sourceDatabase
          )
          .toPromise();
        var subscriptionRequest = new SubscriptionRequest({
          integrationId: this.intergrationData.guid,
          soruceConnectionId: this.intergrationData.sourceConnectionGuid,
          resource: `${connectionCredObj.endpointUrl}Companies(${company.message})/${this.intergrationData.sourceTable}`,
          clientState: 'SomeSharedSecretForTheNotificationUrl',
        });
        //register webhook for real-time syncing to BC using webhook service.
        await this._webHookService.register(subscriptionRequest).toPromise();
      } else {
        //console.log('delete');

        var isSubscription = await this._webHookService.isSubscription(this.intergrationData.sourceConnectionGuid, this.intergrationData.guid).toPromise();
        if (isSubscription.isError) {
          //delte webhook
          await this._webHookService
            .deleteSubscription(
              this.intergrationData.sourceConnectionGuid,
              this.intergrationData.guid
            )
            .toPromise();
        }
        //console.log(this.integrationSettings.BcToSql);

        if (this.intergrationData.jobFrequency != 'On Demand') {
          var jobRequest = new JobRequest({
            frequency: this.intergrationData.jobFrequency,
            reqeustGuid: this.intergrationData.guid,
          });
          await this._connectionIntegrationService
            .scheduleJob(jobRequest)
            .toPromise();
        }
      }
      if (this.integrationSettings.SqlToBc) {
        await this._connectionIntegrationService
          .createTrigger(
            this.intergrationData.guid,
            this.intergrationData.destinationConnectionGuid,
            this.intergrationData.destinationDatabase,
            this.intergrationData.sourceDatabase + '_' + this.intergrationData.sourceTable,
            this.intergrationData.sourceConnectionGuid
          )
          .toPromise();
      } else {
        await this._connectionIntegrationService
          .deleteTrigger(
            this.intergrationData.destinationConnectionGuid,
            this.intergrationData.destinationDatabase,
            this.intergrationData.sourceDatabase +
            '_' +
            this.intergrationData.sourceTable
          )
          .toPromise();
      }

      if (this.mappingData.length) {

      }
      this.toasterService.showToaster('success', 'Data is saved ');
      return true;
    } catch (error) {
      this.isSaving = false;
      this.toasterService.showToaster('error', error.message);
      return false;
    }
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  async onSaveAndExecute() {
    this.isExecuting = true
    var res = await this.save()
    if (res) {
      await this.executeJob(this.intergrationData.guid)
    }
    this.isExecuting = false
  }

  async onSave() {
    this.isSaving = true
    var res = await this.save()
    this.isSaving = false
    if (res) {
      this.router.navigate(['/sync-hub/bc-sql-list']);
    }

  }
  mappingData: MappedColumn[] = [];
  async refreshEntities() {
    this.isRefreshing = true;

    await this._aTestService.metadata(this.intergrationData.sourceConnectionGuid).toPromise();


    this.LoadAllEntities();
    this.isRefreshing = false;

  }
  async onAddView() {

    if (this.integrationSettings.ViewName == undefined) {
      var res = await this._connectionIntegrationService.getFieldFromService(this.intergrationData.sourceTable).toPromise()
      if (res) {
        //console.log(res);

        this.integrationSettings.ViewName = this.intergrationData.sourceDatabase + '$' + res.tableName;
      }
    }

    var isViewDto: IsViewDto = new IsViewDto({
      connectionGuid: this.intergrationData.destinationConnectionGuid,
      databaseName: this.intergrationData.destinationDatabase,
      viewName: this.integrationSettings.ViewName
    })
    //console.log(isViewDto);

    var res2 = await this._connectionIntegrationService.checkIfViewExists(isViewDto).toPromise();
    //console.log(res2);

    if (!res2.isError) {
      this.toasterService.showToaster('error', res2.message);
      return;
    }

    const dialogRef = this.dialog.open(AddViewDialogComponent, {
      maxHeight: '80vh',
      width: '800px',
      data: {
        guid: this.selectedSourceConnection.guid,
        company: this.intergrationData.sourceDatabase,
        entity: this.intergrationData.sourceTable
      }
    });
    dialogRef.afterClosed().subscribe(async result => {
      if (result) {

        var createViewDto: CreateViewDto = new CreateViewDto(
          {
            connectionGuid: this.intergrationData.destinationConnectionGuid,
            databaseName: this.intergrationData.destinationDatabase,
            viewName: this.intergrationData.sourceDatabase + '$' + result.viewName,
            tableName: this.intergrationData.sourceDatabase + '_' + this.intergrationData.sourceTable,
            mappedColumns: result.mappingData
          }
        );
        this.integrationSettings.ViewName = createViewDto.viewName;
        try {
          let res = await this._connectionIntegrationService.createView(createViewDto).toPromise();
          if (res.isError) {
            this.toasterService.showToaster('error', res.message);
          } else {
            this.toasterService.showToaster('success', 'View created successfully');
          }
        } catch (error) {
          this.toasterService.showToaster('error', error.message);
        }
      }
    });
  }
}
