﻿using AdminPortalBackend.Core.Features;
using Microsoft.KernelMemory;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.Text;
using System.Text.Json;

namespace AdminPortalBackend.Infrastructure;

public class AIService(Kernel kernel, IKernelMemory memory)
{
    public async Task<AIResponseMessage> GetAIAnswer(string question)
    {
        var memoryAnswer = await memory.AskAsync(question, minRelevance: 0.4);

        var response = new AIResponseMessage();

        if (memoryAnswer.NoResult == false)
        {
            response.IsError = false;
            response.Message = memoryAnswer.Result;
        }
        else
        {
            response.IsError = true;
            response.Message = "I am not sure.";
        }

        return response;
    }

    public async Task<string> AddMemory(string text, string id)
    {
        var res = await memory.ImportTextAsync(text, id);
        
        return res;
    }

    public async Task DeleteSingleMemory(string id)
    {
        await memory.DeleteDocumentAsync(id);
    }
    public async Task<List<MappedColumn>> MapColumns(List<string> sourceColumns, List<string> destinationColumns)
    {
        string promptTemplate = @"You are an AI designed to map source columns and destination columns from context into a specific JSON output format. 
                                
                            Your task is to map source and destination column names from the input JSON into the desired output structure. 
                            Follow these instructions to ensure proper transformation.
                           
                        Instructions:
                            Parse the provided JSON input.
                            Identify the sourceColumns array and the destinationColumns array from the context.
                            Match each element in the sourceColumns array with its corresponding element in the destinationColumns array.
                            If the names differ slightly (e.g., CreatedAt vs. AddedAt or Document_Type vs. Document Type), match them based on similarity of meaning.
                            Ensure that the JSON output is properly formatted.
                            User Input:
                            Source Columns:Document_Type, No
                            Destination Columns:Document Type, No
                            Assistant:
                            [
                              {
                                ""source"": ""Document_Type"",
                                ""destination"": ""Document Type""
                              },
                              {
                                ""source"": ""No"",
                                ""destination"": ""No""
                              }
                            ]
                            User Input:
                            Source Columns:<<SourceColumns>>
                            Destination Columns:<<DestinationColumns>>
                            Assistant:
                            Always Output a valid json. Do NOT include anything extra description or anything other than a valid json.
";
#pragma warning disable 
        var executionSettings = new OpenAIPromptExecutionSettings
        {
            ResponseFormat = typeof(AIMapResponse)
        };
        var prompt = promptTemplate.Replace("<<SourceColumns>>", string.Join(", ", sourceColumns)).Replace("<<DestinationColumns>>", string.Join(", ", destinationColumns));

        var response = await kernel.InvokePromptAsync<string>(prompt, new (executionSettings));

        try
        {
            // Deserialize the response into a list of MappedColumn objects
            var mappedColumns = JsonSerializer.Deserialize<AIMapResponse>(response);

            // If deserialization is successful, return the list
            return mappedColumns.MapColumns ?? new List<MappedColumn>();  // Return empty list if no valid mappings
        }
        catch (JsonException ex)
        {
            // Log or handle the exception as needed, for now we return an empty list
            Console.WriteLine($"Error parsing JSON response: {ex.Message}");
            return new List<MappedColumn>();  // Return empty list on error
        }
    }

    public async IAsyncEnumerable<string> GetAIAnswerStream(string question)
    {
        // Create chat history and settings for streaming
        var chatHistory = new ChatHistory();
        var searchResults = await memory.SearchAsync(question, minRelevance: 0.4);
        if (searchResults.Results.Any())
        {
            
            // Add system message to constrain responses
            chatHistory.AddSystemMessage("You must only answer using the information provided in these system messages and do not generate any additional information.");
            //chatHistory.AddSystemMessage();
            searchResults.Results.ForEach(item => chatHistory.AddSystemMessage(item.Partitions[0].Text));

            // Add user question
            chatHistory.AddUserMessage(question);

            // Get the chat completion service from the kernel
            var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>();

            // Use streaming API
            var streamingResult = chatCompletionService.GetStreamingChatMessageContentsAsync(
                chatHistory,
                executionSettings: new OpenAIPromptExecutionSettings { MaxTokens = 1000 });

            await foreach (var content in streamingResult)
            {
                yield return content.Content;
            }
        }
        else
        {
            yield return "I am not sure";
        }

    }
}

public class AIResponseMessage
{
    public bool IsError { get; set; }
    public string Message { get; set; }
}

public class AIMapResponse
{
    public List<MappedColumn> MapColumns { get; set; }
}