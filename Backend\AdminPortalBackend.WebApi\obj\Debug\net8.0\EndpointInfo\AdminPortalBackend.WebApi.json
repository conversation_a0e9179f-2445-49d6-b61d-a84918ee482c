{"openapi": "3.0.1", "info": {"title": "AdminPortalBackend.WebApi", "version": "1.0"}, "paths": {"/api/AppSetting/AddOrEditWebHookNotification": {"post": {"tags": ["AppSetting"], "parameters": [{"name": "notificationUrl", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/AppSetting/GetWebhookNotificationUrl": {"post": {"tags": ["AppSetting"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ATest/entities/{connectionId}": {"get": {"tags": ["ATest"], "parameters": [{"name": "connectionId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ATest/metadata/{connectionId}": {"get": {"tags": ["ATest"], "parameters": [{"name": "connectionId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ATest/companies/{connectionId}": {"get": {"tags": ["ATest"], "parameters": [{"name": "connectionId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ATest/test-bc-url/{token}": {"get": {"tags": ["ATest"], "parameters": [{"name": "token", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ATest/sqltobc": {"get": {"tags": ["ATest"], "responses": {"200": {"description": "Success"}}}}, "/api/ATest/bctosql": {"get": {"tags": ["ATest"], "responses": {"200": {"description": "Success"}}}}, "/api/ConnectionIntegration/GetDatabase/{guid}": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/GetTables/{guid}/{databaseName}": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "databaseName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/GetColumn/{guid}/{databaseName}/{tableName}": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "databaseName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tableName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/Edit": {"post": {"tags": ["ConnectionIntegration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}}}}, "/api/ConnectionIntegration/Create": {"post": {"tags": ["ConnectionIntegration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConnectionIntegrationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateConnectionIntegrationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateConnectionIntegrationDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/GetCompanyId": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "connectionGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "database", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/CreateTableForODataWebService": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "isExecute", "in": "query", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConnectionIntegrationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateConnectionIntegrationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateConnectionIntegrationDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/GetSqlSqlAllAsync": {"get": {"tags": ["ConnectionIntegration"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionIntegration"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionIntegration"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}}}}}, "/api/ConnectionIntegration/GetBcSqlAllAsync": {"get": {"tags": ["ConnectionIntegration"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionIntegration"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionIntegration"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}}}}}, "/api/ConnectionIntegration/GetSingle": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}}}}, "/api/ConnectionIntegration/Delete": {"delete": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}}}}, "/api/ConnectionIntegration/ScheduleJob": {"post": {"tags": ["ConnectionIntegration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/JobRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/JobRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/DisableJob": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/DeleteJob": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/ExecuteJob": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "integrationId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/GetEntityNames/{guid}": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/GetPropertiesForEntity": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "entityName", "in": "query", "schema": {"type": "string"}}, {"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/GetCompanyNames/{guid}": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "guid", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/CreateTrigger": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "integrationGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "destinationGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "databaseName", "in": "query", "schema": {"type": "string"}}, {"name": "tableName", "in": "query", "schema": {"type": "string"}}, {"name": "sourceGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/DeleteTrigger": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "destinationGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "databaseName", "in": "query", "schema": {"type": "string"}}, {"name": "tableName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/EditTableForODataWebService": {"post": {"tags": ["ConnectionIntegration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConnectionIntegration"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/CheckTableExist": {"post": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "destinationGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "destinationDatabase", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/IsSystemRowVersionAvailable": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "entityName", "in": "query", "schema": {"type": "string"}}, {"name": "connectionGuid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/CreateView": {"post": {"tags": ["ConnectionIntegration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateViewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateViewDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateViewDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/CheckIfViewExists": {"post": {"tags": ["ConnectionIntegration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsViewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IsViewDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IsViewDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/GetFieldNames": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "tableName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/GetTableNames": {"get": {"tags": ["ConnectionIntegration"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageList"}}}}}}}, "/api/ConnectionIntegration/GetFieldFromService": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "serviceName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceField"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceField"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceField"}}}}}}}, "/api/ConnectionIntegration/ProcessSqlDelete": {"put": {"tags": ["ConnectionIntegration"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/ConnectionIntegration/GetDestinationPrimaryKey/{destinationTable}": {"get": {"tags": ["ConnectionIntegration"], "parameters": [{"name": "destinationTable", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/DbConnection/GetAll": {"get": {"tags": ["DbConnection"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DbConnectionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DbConnectionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DbConnectionDto"}}}}}}}}, "/api/DbConnection/GetSingle": {"get": {"tags": ["DbConnection"], "parameters": [{"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}}}}}}, "/api/DbConnection/CreateOrEdit": {"post": {"tags": ["DbConnection"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DbConnectionDto"}}}}}}}, "/api/DbConnection/Delete": {"delete": {"tags": ["DbConnection"], "parameters": [{"name": "guid", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/DbConnection/GetTop10JobLogs": {"get": {"tags": ["DbConnection"], "parameters": [{"name": "integrationId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobLogEntry"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobLogEntry"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobLogEntry"}}}}}}}}, "/api/External/request": {"post": {"tags": ["External"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestPayload"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RequestPayload"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RequestPayload"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/File/Upload": {"post": {"tags": ["File"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/File/Getfile/{fileName}": {"get": {"tags": ["File"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/File/DeleteFile": {"delete": {"tags": ["File"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/GenAI/GetAIResponse/{question}": {"get": {"tags": ["GenAI"], "parameters": [{"name": "question", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AIResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AIResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AIResponseMessage"}}}}}}}, "/api/GenAI/AddAIMemory": {"get": {"tags": ["GenAI"], "responses": {"200": {"description": "Success"}}}}, "/api/GenAI/MapColumns": {"post": {"tags": ["GenAI"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapColumns"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MapColumns"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MapColumns"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MappedColumn"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MappedColumn"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MappedColumn"}}}}}}}}, "/api/TestConnection/CheckSqlConnection": {"post": {"tags": ["TestConnection"], "parameters": [{"name": "username", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}, {"name": "serverName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/TestConnection/CheckBcConnection": {"post": {"tags": ["TestConnection"], "parameters": [{"name": "clientSecret", "in": "query", "schema": {"type": "string"}}, {"name": "clientId", "in": "query", "schema": {"type": "string"}}, {"name": "tokenEndpoint", "in": "query", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/api/TestConnection/realtime": {"post": {"tags": ["TestConnection"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "/api/webhooks/register": {"post": {"tags": ["Webhook"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubscriptionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubscriptionRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/webhooks/bc-notifications": {"post": {"tags": ["Webhook"], "parameters": [{"name": "validationToken", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WebhookRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WebhookRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/webhooks/delete-subscription/{connectionId}/{integrationId}": {"delete": {"tags": ["Webhook"], "parameters": [{"name": "connectionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "integrationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/webhooks/IsSubscription": {"get": {"tags": ["Webhook"], "parameters": [{"name": "connectionId", "in": "query", "schema": {"type": "string"}}, {"name": "integrationId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}}, "components": {"schemas": {"AIResponseMessage": {"type": "object", "properties": {"isError": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ConnectionIntegration": {"type": "object", "properties": {"guid": {"type": "string", "format": "uuid"}, "integrationName": {"type": "string", "nullable": true}, "sourceConnectionGuid": {"type": "string", "format": "uuid"}, "sourceConnectionName": {"type": "string", "nullable": true}, "sourceDatabase": {"type": "string", "nullable": true}, "sourceTable": {"type": "string", "nullable": true}, "destinationConnectionGuid": {"type": "string", "format": "uuid"}, "destinationConnectionName": {"type": "string", "nullable": true}, "destinationDatabase": {"type": "string", "nullable": true}, "destinationTable": {"type": "string", "nullable": true}, "sourcePrimaryKey": {"type": "string", "nullable": true}, "destinationPrimaryKey": {"type": "string", "nullable": true}, "jobFrequency": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "mappedColumns": {"type": "string", "nullable": true}, "settings": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "lastModifiedDate": {"type": "string", "format": "date-time", "nullable": true}, "lastExecutionDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateConnectionIntegrationDto": {"type": "object", "properties": {"sourceConnectionGuid": {"type": "string", "format": "uuid"}, "sourceConnectionName": {"type": "string", "nullable": true}, "sourceDatabase": {"type": "string", "nullable": true}, "sourceTable": {"type": "array", "items": {"type": "string"}, "nullable": true}, "destinationConnectionGuid": {"type": "string", "format": "uuid"}, "destinationConnectionName": {"type": "string", "nullable": true}, "destinationDatabase": {"type": "string", "nullable": true}, "destinationTable": {"type": "array", "items": {"type": "string"}, "nullable": true}, "sourcePrimaryKey": {"type": "array", "items": {"type": "string"}, "nullable": true}, "destinationPrimaryKey": {"type": "array", "items": {"type": "string"}, "nullable": true}, "jobFrequency": {"type": "string", "nullable": true}, "mappedColumns": {"type": "array", "items": {"type": "string"}, "nullable": true}, "settings": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateViewDto": {"type": "object", "properties": {"connectionGuid": {"type": "string", "format": "uuid"}, "databaseName": {"type": "string", "nullable": true}, "viewName": {"type": "string", "nullable": true}, "tableName": {"type": "string", "nullable": true}, "mappedColumns": {"type": "array", "items": {"$ref": "#/components/schemas/MappedColumn"}, "nullable": true}}, "additionalProperties": false}, "DateOnly": {"type": "object", "properties": {"year": {"type": "integer", "format": "int32"}, "month": {"type": "integer", "format": "int32"}, "day": {"type": "integer", "format": "int32"}, "dayOfWeek": {"$ref": "#/components/schemas/DayOfWeek"}, "dayOfYear": {"type": "integer", "format": "int32", "readOnly": true}, "dayNumber": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "DayOfWeek": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "DbConnectionDto": {"type": "object", "properties": {"guid": {"type": "string", "format": "uuid"}, "connectionName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "connectionCredJson": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FilterCondition": {"type": "object", "properties": {"field": {"type": "string", "nullable": true}, "operator": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IsViewDto": {"type": "object", "properties": {"connectionGuid": {"type": "string", "format": "uuid"}, "databaseName": {"type": "string", "nullable": true}, "viewName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "JobLogEntry": {"type": "object", "properties": {"processId": {"type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "recordCount": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "JobRequest": {"type": "object", "properties": {"frequency": {"type": "string", "nullable": true}, "reqeustGuid": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "MapColumns": {"type": "object", "properties": {"sourceColumns": {"type": "array", "items": {"type": "string"}, "nullable": true}, "destinationColumns": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "MappedColumn": {"type": "object", "properties": {"source": {"type": "string", "nullable": true}, "destination": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RequestPayload": {"type": "object", "properties": {"connectionId": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}, "action": {"type": "string", "nullable": true}, "entity": {"type": "string", "nullable": true}, "filter": {"type": "array", "items": {"$ref": "#/components/schemas/FilterCondition"}, "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "ResponseMessage": {"type": "object", "properties": {"isError": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResponseMessageList": {"type": "object", "properties": {"isError": {"type": "boolean"}, "message": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ServiceField": {"type": "object", "properties": {"tableName": {"type": "string", "nullable": true}, "fieldNames": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "SubscriptionRequest": {"type": "object", "properties": {"soruceConnectionId": {"type": "string", "nullable": true}, "integrationId": {"type": "string", "nullable": true}, "resource": {"type": "string", "nullable": true}, "clientState": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"$ref": "#/components/schemas/DateOnly"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WebhookNotification": {"type": "object", "properties": {"subscriptionId": {"type": "string", "nullable": true}, "clientState": {"type": "string", "nullable": true}, "expirationDateTime": {"type": "string", "format": "date-time"}, "resource": {"type": "string", "nullable": true}, "changeType": {"type": "string", "nullable": true}, "lastModifiedDateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "WebhookRequest": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/WebhookNotification"}, "nullable": true}}, "additionalProperties": false}}}}