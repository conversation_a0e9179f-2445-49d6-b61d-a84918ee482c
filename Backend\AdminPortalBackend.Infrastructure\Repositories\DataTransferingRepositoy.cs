﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text.Json;
using System.Threading.Tasks;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.OData;
using Dapper;
using Microsoft.OData;
using Microsoft.VisualBasic;

namespace AdminPortalBackend.Infrastructure.Repositories
{
    public class DataTransferingRepositoy(IDbConnection _dbConnection, ODataService _oDataService, IDbConnectionRepository _dbConnectionRepo) 
    {
        private const int BatchSize = 250; // Set batch size

        

        
        private async Task EnsureODataStagingTableExists(Guid sourceConnectionId, string destinationConnectionString, string destinationTable, string destinationPrimaryKey)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(destinationConnectionString))
                {
                    await connection.OpenAsync(); // Ensure the connection is open

                    string stagingTable = $"{destinationTable}_Staging";
                    var tableName = destinationTable.Split('_')[1];

                    // Check if staging table exists and create if it doesn't
                    string createTableSql = $@"
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{stagingTable}')
                    BEGIN
                        SELECT * INTO [{stagingTable}] FROM [{destinationTable}] WHERE 1 = 0;
                    END";

                    await connection.ExecuteAsync(createTableSql);
                }
            }
            catch (Exception ex) { 
            
            }
        }

        
        
        
        
        

        public async Task TransferDataFromApiToDb(Guid guid)
        {
            //var sql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";
            //var integration = await _dbConnection.QuerySingleOrDefaultAsync<ConnectionIntegration>(sql, new { Guid = guid });
            //if (integration == null)
            //{
            //    return; // Integration not found, exit early
            //}

            //var destinationConnectionString = await GetConnectionString(integration.DestinationConnectionGuid, integration.DestinationDatabase);
            //if (destinationConnectionString == null)
            //{
            //    return; // Unable to get destination connection string
            //}
            
            //var sourceConnection = await _dbConnectionRepo.GetConnection(integration.SourceConnectionGuid);
            //if (sourceConnection == null)
            //{
            //    return; // Unable to get destination connection string
            //}

            //var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);

            //// Ensure the staging table exists
            //await EnsureODataStagingTableExists(integration.SourceConnectionGuid, destinationConnectionString, integration.DestinationTable, integration.DestinationPrimaryKey);

            //// Clear staging table before transferring data
            //await ClearStagingTable(destinationConnectionString, integration.DestinationTable);

            ////// Fetch data from the API
            //var sourceDataUrl = $"Company('{integration.SourceDatabase}')/{integration.SourceTable}";
            //if (sourceConnection.Type == "BCODataRestApiService")
            //{
            //    sourceDataUrl = $"Companies(f97d039b-d795-ef11-8a6b-000d3a7b95ff)/{integration.SourceTable}";
            //}
            ////var apiData = sourceConnection.Type == "BCODataWebService" || sourceConnection.Type == "BCODataRestApiService" ? await _oDataService.GetODataWebSeriviceData(integration.SourceConnectionGuid, dataUrl) : await FetchDataFromApi(integration.SourceConnectionGuid);

            ////if (apiData == null || apiData.Count == 0)
            ////{
            ////    return; // No data fetched from API
            ////}
            //// pull data from odata service and save to stagging table
            //await SaveODataToStagging(integration.SourceDatabase, sourceDataUrl, integration.SourceConnectionGuid, destinationConnectionString, integration.DestinationTable, mappedColumns);
            //// Merge data from staging to destination table
            //await MergeStagingToDestination(destinationConnectionString, integration.DestinationTable, integration.DestinationPrimaryKey);
        }

        public async Task SaveODataToStagging(string sourceDatabase, string sourceDataUrl, Guid sourceConnectionGuid, string destinationConnectionString, string destinationTable, Dictionary<string, string> mappedColumns)
        {
            var hasMoreData = true;
            int skip = 0; // To track the offset for pagination
            int batchSize = 10;
            while (hasMoreData)
            {
                // Construct the data URL with pagination
                var dataUrl = $"{sourceDataUrl}?$skip={skip}&$top={batchSize}";
                var apiData = await _oDataService.GetODataWebSeriviceData(sourceConnectionGuid, dataUrl);

                // Check if we received any data
                if (apiData.Count == 0)
                {
                    hasMoreData = false; // No more data to fetch
                    break;
                }

                // Transfer the fetched data to the staging table
                await TransferApiDataToStaging(destinationConnectionString, destinationTable, apiData, mappedColumns);

                // Increment the skip for the next batch
                skip += batchSize;
            }
        }

        private async Task<List<Dictionary<string, object>>> FetchDataFromApi(Guid sourceConnectionGuid)
        {
            var dbConnection = await _dbConnection.QueryFirstOrDefaultAsync<DbConnection>(
                "SELECT * FROM DbConnections WHERE Guid = @Guid",
                new { Guid = sourceConnectionGuid }
            );

            if (dbConnection != null)
            {
                var connectionCreds = JsonSerializer.Deserialize<ApiCredDto>(dbConnection.ConnectionCredJson);
                if (connectionCreds != null)
                {
                    using (var httpClient = new HttpClient())
                    {
                        // Set the headers for the request
                        if (connectionCreds.Headers != null)
                        {
                            foreach (var header in connectionCreds.Headers)
                            {
                                httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                            }
                        }

                        // Call the API
                        var response = await httpClient.GetAsync(connectionCreds.ApiUrl);
                        response.EnsureSuccessStatusCode(); // Throw if not a success code

                        var responseData = await response.Content.ReadAsStringAsync();

                        // Deserialize the response into a list of dictionaries
                        var apiData = JsonSerializer.Deserialize<List<Dictionary<string, object>>>(responseData);

                        // Transform keys and parse dates
                        var transformedData = apiData.Select(row =>
                            row.ToDictionary(
                                kvp => char.ToUpper(kvp.Key[0]) + kvp.Key.Substring(1), // Capitalize first letter
                                kvp => ParseDateIfString(kvp.Value) // Parse dates if the value is a string
                            )
                        ).ToList();

                        return transformedData; // Return the transformed data
                    }
                }
            }

            return null; // Return null if unable to fetch data
        }

        private object ParseDateIfString(object value)
        {
            try
            {
                if (DateTime.TryParse(value.ToString(), out DateTime parsedDate))
                {
                    return parsedDate; // Return DateTime object if parsing is successful
                }
            }
            catch (Exception ex) { 
            
                return value; // Return original value if it's not a string or if parsing fails
            }

            return value; // Return original value if it's not a string or if parsing fails
        }


        private async Task TransferApiDataToStaging(string destinationConnectionString, string destinationTable, List<Dictionary<string, object>> apiData, Dictionary<string, string> mappedColumns)
        {
            //var columns = await GetColumnNamesAsync(destinationConnectionString, destinationTable + "_Staging");
            //using (SqlConnection connection = new SqlConnection(destinationConnectionString))
            //{
            //    // here get the updated data from table and send post request
            //    await connection.OpenAsync();

            //    foreach (var row in apiData)
            //    {
            //        // Create a DataTable to hold the current batch
            //        var dataTable = new DataTable(destinationTable);
            //        foreach (var column in columns)
            //        {
            //            dataTable.Columns.Add(column); // Add columns based on mapped columns
            //        }

            //        // Create a new DataRow
            //        var newRow = dataTable.NewRow();
            //        foreach (var map in mappedColumns)
            //        {
            //            // Map API response data to the DataRow
            //            if (row.ContainsKey(map.Key))
            //            {
            //                var val = row[map.Key];
            //                var val2 = newRow[map.Value];
            //                newRow[map.Value] = row[map.Key];
            //            }
            //        }

            //        dataTable.Rows.Add(newRow);

            //        // Use SqlBulkCopy to transfer data to the staging table
            //        using (SqlBulkCopy bulkCopy = new SqlBulkCopy(connection))
            //        {
            //            bulkCopy.DestinationTableName = $"[{destinationTable}_Staging]";
            //            await bulkCopy.WriteToServerAsync(dataTable);
            //        }
            //    }
            //    await connection.CloseAsync();
            //}
        }

        public Task TransferData(Guid integrationGuid)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetConnectionString(Guid guid, string databaseName)
        {
            throw new NotImplementedException();
        }
    }
}
