<div class="page-header ">
  <h2>Video Gallery</h2>
  <button class="Btn" (click)="AddVideoDialog()">Add +</button>
</div>

<div class="card">
  <p-table [value]="videos" [paginator]="true" [rows]="5" [showCurrentPageReport]="true"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [rowsPerPageOptions]="[5, 10, 25]">
    <ng-template pTemplate="header">
      <tr>
        <th>Video</th>
        <th>Title</th>
        <th>Item No</th>
        <th>Status</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-video>
      <tr>
        <td>
          <video width="100%" height="100" controls>
            <source [src]="video.videoLink" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </td>
        <td>{{ video.title }}</td>
        <td>{{ video.itemNo }}</td>
        <td>
          <span class="video-tab-label" *ngIf="video.isOnVideoTab">On Video Tab</span>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>
