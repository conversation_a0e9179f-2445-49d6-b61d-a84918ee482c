<div class="p-3">
  <h2>View Name: {{viewName}}</h2>
  <div class="d-flex justify-content-end" style="margin-top: -50px;">
    <button class="btn btn-primary d-flex" (click)="mapWithAi()" style="background-color: var(--bg-color);">
      <span>{{ isMapping ? 'Mapping ' : 'Map With AI' }}</span>
      <span class="d-flex">
        <i *ngIf="isMapping" class="pi pi-spin pi-spinner" style="font-size: 1.2rem"></i>
      </span>
    </button>
  </div>
  <div class="d-flex align-items-center justify-content-between"
    style="font-size: 18px; margin: 10px 0; padding: 0 5px;" *ngIf="mappingData">
    <div>FieldName</div>
    <div>MapTo</div>
  </div>
  <div class="d-flex gap-2 flex-column">
    <div *ngFor="let mapping of mappingData; let i = index" class="mapping-section d-flex gap-2">
      <div class="mapping-cell" style="width: 100%;">
        <p-dropdown [options]="fieldName" [(ngModel)]="mapping.source" [placeholder]="'Select Source Column'"
          name="source{{ i }}" required></p-dropdown>
      </div>

      <div class="mapping-cell" style="width: 100%;">
        <p-dropdown [options]="mapTo" [(ngModel)]="mapping.destination" [placeholder]="'Select Destination Column'"
          name="destination{{ i }}" required></p-dropdown>
      </div>
      <i (click)="remove(i)" style="color: red; margin-top: 8px; font-size: 1rem;" class="fa-solid fa-delete-left"></i>
    </div>
  </div>
  <div class="mt-3 d-flex align-items-center justify-content-end">
    <button class="custom-dropdown-toggle d-flex align-items-center"
      style="background-color: var(--bg-color); color: white; border-radius: 5px;" (click)="onSave()">
      Generate View
    </button>
  </div>
</div>
