<div class="card">
  <button class="backBtn btn" (click)="goBack()"><i class="fa-solid fa-arrow-left"></i> </button>
  <p-tabView [(activeIndex)]="activeIndex">

    <p-tabPanel header="Business Central">
      <div class="flex justify-content-center mt-3">
        <p style="font-weight: bold;">Select Source </p>
        <p-dropdown [options]="bcConnections" optionLabel="connectionName" (onChange)="onSourceSelected($event.value)"
          [style]="{'width':'85%'}" required [loading]="dropdownLoading.connection">
        </p-dropdown>
      </div>
      <br>
      <div class="d-flex w-100 gap-4 w-100" *ngIf="this.sourceGuid">
        <div class="flex justify-content-center mb-3" style="width: 30%;">
          <div class="d-flex justify-content-between align-items-center">

            <p style="font-weight: bold;">Select Companies</p>
            <button style="border: none; background: none; color: #085e81;padding: 0;" (click)="refreshCompany()"
              *ngIf="!compnayLoading" pTooltip="Refresh Company" tooltipPosition="top" placeholder="top">
              <i class="fa-solid fa-arrow-rotate-right fs-5"></i>
            </button>

            <span class="loader" *ngIf="compnayLoading"></span>
          </div>
          <p-listbox [options]="companyNames" [(ngModel)]="selectedCompanyName" [style]="{'width':'100%'}"
            [metaKeySelection]="false" [multiple]="true" [listStyle]="{'max-height': '320px', 'padding':0}"
            #companyName="ngModel"  required  />

          <!-- Display error message if the field is required and empty -->
          <div *ngIf="companyName?.errors?.['required']" class="error-message">
            Source connection is required.
          </div>

        </div>

        <div class="flex justify-content-center " style="width: 70%;">
          <div class="d-flex gap-2 align-items-center justify-content-between">
            <p style="font-weight: bold;">Select Entities</p>
            <div class="d-flex  " style="gap: 8px;">
              <form class="form-inline my-2">
                <input class="form-control mr-sm-2" (input)="searchEntities($event)" type="text"
                  placeholder="Search Entities" aria-label="Search">
              </form>
              <div class="d-flex align-items-center">
                <button style="border: none; background: none; color: #085e81;padding: 0;" (click)="refreshEntities()"
                  *ngIf="!loading" pTooltip="Refresh Entities" tooltipPosition="top" placeholder="top">
                  <i class="fa-solid fa-arrow-rotate-right fs-5"></i>
                </button>
                <span class="loader" *ngIf="loading"></span>
              </div>
            </div>
          </div>
          <p-listbox *ngIf="!isSearchedResult" [options]="searchedEntities" [(ngModel)]="selectedEntityNames"
            [style]="{'width':'100%'}" [multiple]="true" [metaKeySelection]="false"
            [listStyle]="{'max-height': '320px', 'padding': 0}" #entityList="ngModel" required >
          </p-listbox>

          <div *ngIf="!selectedEntityNames.length" class="error-message">
            At least one entity must be selected.
          </div>

          <!-- Display error message if the listbox is required and no item is selected -->

          <div *ngIf="isSearchedResult">no related result found</div>

        </div>
      </div>

    </p-tabPanel>
    <p-tabPanel header="Destination" >

      <div class="form-section">
        <p style="font-weight: bold;">Select Destination Connection</p>
        <p-dropdown [options]="sqlConnections" optionLabel="connectionName" [style]="{'width':'100%'}"
          (onChange)="onSourceConnectionSelected($event.value)" placeholder="Select Source Connection"
          required [loading]="dropdownLoading.source"></p-dropdown>

        <!-- Error message for source connection -->


        <div class="flex justify-content-center mt-3">
          <p style="font-weight: bold;">Select Destination Database</p>

          <p-dropdown [options]="destinationDatabases" [(ngModel)]="destinationDatabase" [style]="{'width':'100%'}"
            filter="true" filterPlaceholder="Search..." [showClear]="true" required #destinationDb="ngModel" [loading]="dropdownLoading.database">
          </p-dropdown>

          <!-- Error message for destination database -->
          <div *ngIf="destinationDatabase=='' &&destinationDb?.errors?.['required'] " class="error-message">
            Destination database is required.
          </div>
        </div>
      </div>

    </p-tabPanel>
    <div *ngIf="!(existingEntities.length==0)" class="my-2 text-danger">
      @for (existingEntitie of existingEntities; track $index) {
      <p>Companies = {{existingEntitie.split("_")[0]}}, Entities = {{existingEntitie.split("_")[1]}}</p>
      }
      <p>is already exist</p>
    </div>

    <div class="w-100 d-flex justify-content-end mt-4" *ngIf="activeIndex == 1">
      <div class="custom-dropdown-container">
        <button class="custom-dropdown-toggle d-flex align-items-center gap-2" (click)="toggleDropdown()" style="
            background-color: var(--bg-color);
            color: white;
            border-radius: 5px;
          ">
          Save & execute <i class="pi pi-angle-down"></i>
        </button>
        <ul class="custom-dropdown-menu d-flex flex-column gap-1" *ngIf="isDropdownOpen">
          <li class="custom-dropdown-item" (click)="save(true)" style="background-color: var(--bg-color); color: white">
            Save & execute
          </li>
          <li class="custom-dropdown-item" (click)="save(false)"
            style="background-color: var(--bg-color); color: white">
            Save
            <span *ngIf="isSaving" class="loader"><i class="pi pi-spin pi-spinner" style="font-size: 1.2rem"></i></span>
          </li>
        </ul>
      </div>

    </div>
  </p-tabView>
</div>
