﻿using AdminPortalBackend.Core.Models;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

public static class HttpClientExtensions
{
    public static void SetAuthorizationHeader(this HttpClient httpClient, string token)
    {
        if (token.Contains("CustomBasicAuth"))
        {
            var basicAuthDetails = JsonSerializer.Deserialize<BasicAuthDetails>(token);
            var byteArray = Encoding.ASCII.GetBytes($"{basicAuthDetails.UserName}:{basicAuthDetails.Password}");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
        }
        else
        {
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }
    }
}