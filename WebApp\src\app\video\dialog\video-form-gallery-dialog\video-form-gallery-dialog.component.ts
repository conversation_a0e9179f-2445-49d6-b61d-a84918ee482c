import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-video-form-dialog',
  standalone: true,
  imports: [MatFormFieldModule, MatInputModule, FormsModule, MatButtonModule],
  templateUrl: './video-form-gallery-dialog.component.html',
  styleUrls: ['./video-form-gallery-dialog.component.css'],
})
export class VideoFormGalleryDialogComponent {
  video = {
    itemNo: '',
    videoLink: '',
    title: '',
    isThumbnail: false,
    isOnVideoTab: false,
  };

  constructor(private dialogRef: MatDialogRef<VideoFormGalleryDialogComponent>) {}

  onSubmit(form: any) {
    if (form.valid) {
      // Pass the video data back to the parent component
      console.log(this.video);
      this.dialogRef.close(this.video);
    }
  }

  onClose() {
    this.dialogRef.close();
  }
}
