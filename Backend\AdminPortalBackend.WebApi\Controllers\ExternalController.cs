﻿using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Infrastructure.DataStore;
using Dapper;
using Elastic.Clients.Elasticsearch;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver.Core.Connections;
using System.Data;
using System.Reflection;
using System.Text.Json;

namespace AdminPortalBackend.WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ExternalController (SyncManager syncManager, IDbConnection _dbConnection, ILogger<ExternalController> logger) : ControllerBase
{
    [HttpPost("request")]
    public async Task<ActionResult> HandleRequest([FromBody] RequestPayload payload)
    {
        // Validate the payload (e.g., check for required fields)
        if (payload == null || string.IsNullOrEmpty(payload.ConnectionId) || string.IsNullOrEmpty(payload.Company) || string.IsNullOrEmpty(payload.Action) || string.IsNullOrEmpty(payload.Entity))
        {
            return BadRequest("Missing required information.");
        }

        var query = @"
            SELECT COUNT(1)
            FROM [DbConnections]
            WHERE [Guid] = @Guid
            AND [Type] IN ('BCODataRestApiService', 'BCODataWebService')";
        int count = await _dbConnection.QuerySingleOrDefaultAsync<int>(query, new { Guid = payload.ConnectionId });


        if (count != 1)
        {
            throw new InvalidOperationException("Connectin Not Valid.");
        }

        var sql = "SELECT * FROM ConnectionIntegrations WHERE SourceConnectionGuid = @SourceConnectionGuid AND SourceDatabase = @SourceDatabase AND SourceTable = @SourceTable";
        var integration = _dbConnection.QuerySingleOrDefaultAsync<ConnectionIntegration>(sql,
                                   new { SourceConnectionGuid = payload.ConnectionId, SourceDatabase = payload.Company, SourceTable = payload.Entity }
                    ).Result;
        if (integration == null)
        {
            logger.LogWarning("Integration was not found for {ConnectionId} {Company} {Entity}", payload.ConnectionId, payload.Company, payload.Entity);
            return BadRequest("Connection integration is not found for provided entity");
        }

        // Check action type and process accordingly
        try
        {
            switch (payload.Action.ToUpper())
            {
                case "INSERT":
                case "UPDATE":
                case "DELETE":
                    var inputData = new List<Dictionary<string, object>>();
                    var response = new List<ResponsePayload>();
                    var isFieldExist = await IsFieldExist(payload.ConnectionId, payload.Entity, payload.Data);
                    if (!isFieldExist)
                    {
                        return BadRequest("Field Doesnot exist");
                    }
                    if (payload.Data is JsonElement jsonElement)
                    {
                        if (jsonElement.ValueKind == JsonValueKind.Array)
                        {
                            inputData = JsonSerializer.Deserialize<List<Dictionary<string, object>>>(jsonElement.GetRawText());
                        }
                        else if (jsonElement.ValueKind == JsonValueKind.Object)
                        {
                            inputData = [JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText())];
                        }
                        else
                        {
                            return Ok(new ResponsePayload { IsError = false, ErrorMessage="Data not supported", Data = payload.Data });
                        }
                    }
                    foreach (var item in inputData)
                    {
                        var result = await syncManager.ProcessRecord(payload.Action.ToUpper(), integration, item);
                        response.Add(new ResponsePayload { IsError = false, Data = result });
                    }

                    return Ok(response);
                case "READ":
                    var readResult = await syncManager.RetrieveData(integration, payload.Filter);
                    return Ok(new ResponsePayload { IsError = false, Data = readResult });
                default:
                    return BadRequest("Invalid action specified.");
            }
        }
        catch (Exception ex)
        {
            return Ok(new ResponsePayload { IsError = true, ErrorMessage=ex.Message });
        }
    }

    private async Task<bool> IsFieldExist(string guid, string entity, object data)
    {
        var query = @"
        SELECT [PropertyName]
        FROM [BCEntityMetadata]
        WHERE [ConnectionId] = @Guid AND [EntityName] = @Entity";

        // Fetch fields from the database
        var fields = await _dbConnection.QueryAsync<string>(query, new { Guid = guid, Entity = entity });

        // Handle different JSON ValueKinds
        List<Dictionary<string, object>> dataDictionaries = new();

        if (data is JsonElement jsonElement)
        {
            if (jsonElement.ValueKind == JsonValueKind.Object)
            {
                // Single object
                dataDictionaries.Add(jsonElement.EnumerateObject()
                                                .ToDictionary(p => p.Name, p => (object)p.Value));
            }
            else if (jsonElement.ValueKind == JsonValueKind.Array)
            {
                // Array of objects
                foreach (var item in jsonElement.EnumerateArray())
                {
                    if (item.ValueKind == JsonValueKind.Object)
                    {
                        dataDictionaries.Add(item.EnumerateObject()
                                                 .ToDictionary(p => p.Name, p => (object)p.Value));
                    }
                    else
                    {
                        throw new ArgumentException("Array elements must be JSON objects.");
                    }
                }
            }
            else
            {
                throw new ArgumentException("Data must be a JSON object or an array of JSON objects.");
            }
        }
        else
        {
            throw new ArgumentException("Data must be a valid JSON structure.");
        }

        // Check if all objects contain keys matching the fields
        foreach (var dataDictionary in dataDictionaries)
        {
            foreach (var key in dataDictionary.Keys)
            {
                if (!fields.Contains(key))
                {
                    return false;
                }
            }
        }

        return true; // Return true if all keys are found
    }

    private string BuildODataFilter(List<FilterCondition> filter)
    {
        // Convert the filter conditions into an OData query string (e.g., `?$filter=name eq 'John Doe' and status eq 'Active'`)
        var filterStrings = filter.Select(f => $"{f.Field} {f.Operator} '{f.Value}'");
        return string.Join(" and ", filterStrings);
    }
}

public class RequestPayload
{
    public string ConnectionId { get; set; } 
    public string Company { get; set; } 
    public string Action { get; set; } // CRUD operation: "create", "read", "update", "delete"
    public string Entity { get; set; } // Entity type: "Customer", "Order", etc.
    public List<FilterCondition> Filter { get; set; }
    public object Data { get; set; } // Dynamic data for the entity
}

public class ResponsePayload
{
    public bool IsError { get; set; }
    public string ErrorMessage { get; set; }
    public object Data { get; set; } 
}


