/* app.component.css */
.app-container {
  width: 100%;
  height: calc(100vh - 58px);
  overflow-y: auto;

}
.cards{
  height: calc(100vh - 58px);
  overflow-y: hidden;
}

.router-outlet {
  width: 100%;
  transition: margin-right 0.3s ease; /* Smooth transition for margin change */
}


.flex1{
  flex: 1;
}

.chat-section {
  height: calc(100vh - 67px);
  overflow-y: auto;
  width: 0; /* Start collapsed */
  transition: width 0.3s ease; /* Smooth transition for width change */
  overflow: hidden; /* Hide overflow when collapsed */
  background-color: #e9ecef; /* Background color for chat section */
  flex-shrink: 0; /* Prevent flex shrinking */
}

.chat-section.open {
  width:100%; /* Set width of the chat section when opened */
  margin: 5px;
}

/* When chat is open, compress the router outlet */
.app-container.open .router-outlet {
  margin-right: 350px; /* Push the router outlet to the left when chat is open */
}

.chat-header {
  display: flex;
  justify-content: space-between; /* Align items to space them */
  align-items: center; /* Center items vertically */
  padding: 10px; /* Padding for header */
  background-color: #007bff; /* Example header color */
  color: white; /* Text color */
}
.chatHeader {
  position: sticky;
  top: 0;
  background-color: #03354c;
  color: white;
  text-align: center;
  padding: 10px;
  z-index: 10; /* Make sure the header stays above the chat content */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chatHeader h4 {
  margin: 0;
  font-size: 18px;
}

.chatHeader p {
  margin: 0;
  font-size: 14px;
  color: #cccccc;
}

.chat-content {
  padding: 20px; /* Padding for chat content */
}

::ng-deep .p-splitter-gutter {
  background-color: var(--bg-color);
  border: 1px solid #d3d3d3;
  transition: background-color 0.3s ease;
}

::ng-deep .p-splitter-gutter:hover {
  background-color: var(--hover-color);
}

::ng-deep .p-splitter-gutter-handle {
  background-color: rgb(255, 255, 255);
  border-radius: 2px;
  height: 50px;
  transition: background-color 0.3s ease;
}

::ng-deep .p-splitter-gutter-handle:hover {
  background-color: #f3ecec;
}

::ng-deep .p-splitter-gutter-handle:active,
::ng-deep .p-splitter-gutter-handle:focus {
  background-color: #ffffff;
  outline: none;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}
