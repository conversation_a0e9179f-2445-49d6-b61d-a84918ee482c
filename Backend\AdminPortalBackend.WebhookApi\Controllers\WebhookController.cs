﻿using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.OData;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient; // or System.Data.SqlClient
using System.Collections.Generic;
using System.Data;
using System.Text.Json;

namespace AdminPortalBackend.WebApi.Controllers;

[ApiController]
[Route("api/webhooks")]
public class WebhookController : ControllerBase
{
    private readonly string _connectionString;

    public WebhookController(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("AdminConnection");
    }

    [HttpGet]
    [Route("ping")]
    public IActionResult Ping()
    {
        return Ok("Pong");
    }

    [HttpPost]
    [Route("bc-notifications")]
    public async Task<IActionResult> ReceiveWebhook([FromQuery] string validationToken, [FromBody] WebhookRequest request)
    {
        if (!string.IsNullOrWhiteSpace(validationToken))
        {
            return Ok(validationToken); // Respond with the validation token first registration
        }

        if (request == null || request.Value == null || request.Value.Count == 0)
        {
            return BadRequest("Invalid webhook request.");
        }

        foreach (var notification in request.Value)
        {
            await SaveNotification(notification);
        }

        return Ok();
    }

    private async Task SaveNotification(WebhookNotification notification)
    {
        try
        {
            using (IDbConnection dbConnection = new SqlConnection(_connectionString))
            {
                const string sql = @"
                    INSERT INTO BCWebhookNotifications (SubscriptionId, ClientState, ExpirationDateTime, Resource, ChangeType, LastModifiedDateTime, Status)
                    VALUES (@SubscriptionId, @ClientState, @ExpirationDateTime, @Resource, @ChangeType, @LastModifiedDateTime, @Status)";

                var parameters = new
                {
                    notification.SubscriptionId,
                    notification.ClientState,
                    notification.ExpirationDateTime,
                    notification.Resource,
                    notification.ChangeType,
                    notification.LastModifiedDateTime,
                    Status="New"
                };

                await dbConnection.ExecuteAsync(sql, parameters);
            }
        }
        catch (Exception ex)
        {
            // Log the exception (you can use a logging framework of your choice)
            Console.WriteLine($"Error processing notification: {ex.Message}");
        }
    }
}
