﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Repositiories;
using AutoMapper;
using Azure.Core;
using Dapper;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Core.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text.Json;
using System.Threading.Tasks;
using static Hangfire.Storage.JobStorageFeatures;

namespace AdminPortalBackend.Infrastructure.Repositories
{
    public class DbConnectionRepository : IDbConnectionRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly IMapper _mapper;
        private readonly ILogger<DbConnectionRepository> _logger;

        public DbConnectionRepository(IDbConnection dbConnection, IMapper mapper, ILogger<DbConnectionRepository> logger)
        {
            _dbConnection = dbConnection;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<List<DbConnectionDto>> GetAllConnections()
        {
            var connections = await _dbConnection.QueryAsync<DbConnection>("SELECT * FROM DbConnections");
            return _mapper.Map<List<DbConnectionDto>>(connections.AsList());
        }

        public async Task<DbConnectionDto> GetConnection(Guid guid)
        {
            var connection = await _dbConnection.QueryFirstOrDefaultAsync<DbConnection>(
                "SELECT * FROM DbConnections WHERE Guid = @Guid",
                new { Guid = guid }
            );

            if (connection == null)
            {
                var ex = new Exception("Connection not found.");
                _logger.LogError(ex,"Connection not found");
                throw ex;
            }

            var dbConnection = _mapper.Map<DbConnectionDto>(connection);
            if (dbConnection.Type == "Ms SQL Server")
            {
                var jsonExtract = JsonSerializer.Deserialize<MssqlServerCredDto>(dbConnection.ConnectionCredJson);
                jsonExtract.Password = EncryptionHelper.Decrypt(jsonExtract.Password);
                dbConnection.ConnectionCredJson = JsonSerializer.Serialize(jsonExtract);
            }
            else if (dbConnection.Type == "BCODataWebService" || dbConnection.Type == "BCODataRestApiService")
            {
                var jsonExtract = JsonSerializer.Deserialize<ODataCredDto>(dbConnection.ConnectionCredJson);
                jsonExtract.ClientSecret = EncryptionHelper.Decrypt(jsonExtract.ClientSecret);
                dbConnection.ConnectionCredJson = JsonSerializer.Serialize(jsonExtract);
            }

            return dbConnection;

        }

        public async Task<ODataConnectionDetailDto> GetODataConnectionDetailAsync(Guid guid)
        {
            var connection = await _dbConnection.QueryFirstOrDefaultAsync<DbConnection>(
                "SELECT * FROM DbConnections WHERE Guid = @Guid",
                new { Guid = guid }
            );

            if (connection == null)
            {
                var ex = new Exception("Connection not found.");
                _logger.LogError("Connection not found");
                throw ex;
            }

            return _mapper.Map<ODataConnectionDetailDto>(connection);
        }

        public async Task<DbConnectionDto> CreateOrEdit(DbConnectionDto request)
        {
            using (_logger.BeginScope(new Dictionary<string, object> { { "ConnectionId", request.Guid } }))
            {
                _logger.LogInformation("Adding connection");
                if (request.Guid == Guid.Empty)
                {
                    // Create
                    var newConnection = _mapper.Map<DbConnection>(request);
                    newConnection.Guid = Guid.NewGuid(); // Generate new Guid
                    newConnection.CreatedDate = DateTime.Now; // Set CreatedDate

                    if (request.Type == "Ms SQL Server")
                    {
                        var jsonExtract = JsonSerializer.Deserialize<MssqlServerCredDto>(newConnection.ConnectionCredJson);
                        jsonExtract.Password = EncryptionHelper.Encrypt(jsonExtract.Password);
                        newConnection.ConnectionCredJson = JsonSerializer.Serialize(jsonExtract);
                    }
                    else if (request.Type == "BCODataWebService" || request.Type == "BCODataRestApiService")
                    {
                        var jsonExtract = JsonSerializer.Deserialize<ODataCredDto>(newConnection.ConnectionCredJson);
                        jsonExtract.ClientSecret = EncryptionHelper.Encrypt(jsonExtract.ClientSecret);
                        newConnection.ConnectionCredJson = JsonSerializer.Serialize(jsonExtract);
                    }

                    var insertQuery = @"
                INSERT INTO DbConnections (Guid, ConnectionName, Description, Type, ConnectionCredJson, CreatedDate) 
                OUTPUT INSERTED.Guid 
                VALUES (@Guid, @ConnectionName, @Description, @Type, @ConnectionCredJson, @CreatedDate)";

                    newConnection.Guid = await _dbConnection.ExecuteScalarAsync<Guid>(insertQuery, newConnection);

                    // Map back to DTO to return
                    return _mapper.Map<DbConnectionDto>(newConnection);
                }
                else
                {
                    // Edit
                    var existingConnection = await GetConnection(request.Guid);

                    // Map updated properties from DTO to entity
                    var updatedConnection = _mapper.Map<DbConnection>(request);
                    updatedConnection.Guid = existingConnection.Guid; // Ensure Guid is not changed

                    // Deserialize and update ConnectionCredJson based on type
                    if (request.Type == "Ms SQL Server")
                    {
                        var jsonExtract = JsonSerializer.Deserialize<MssqlServerCredDto>(request.ConnectionCredJson);
                        jsonExtract.Password = EncryptionHelper.Encrypt(jsonExtract.Password);
                        updatedConnection.ConnectionCredJson = JsonSerializer.Serialize(jsonExtract);
                    }
                    else if (request.Type == "BCODataWebService" || request.Type == "BCODataRestApiService")
                    {
                        var jsonExtract = JsonSerializer.Deserialize<ODataCredDto>(request.ConnectionCredJson);
                        jsonExtract.ClientSecret = EncryptionHelper.Encrypt(jsonExtract.ClientSecret);
                        updatedConnection.ConnectionCredJson = JsonSerializer.Serialize(jsonExtract);
                    }

                    // Set the LastModifiedDate for the update
                    updatedConnection.LastModifiedDate = DateTime.Now;

                    var updateQuery = @"
                        UPDATE DbConnections 
                        SET ConnectionName = @ConnectionName, 
                            Description = @Description, 
                            Type = @Type, 
                            ConnectionCredJson = @ConnectionCredJson,
                            LastModifiedDate = @LastModifiedDate
                        WHERE Guid = @Guid";

                    await _dbConnection.ExecuteAsync(updateQuery, new
                    {
                        updatedConnection.ConnectionName,
                        updatedConnection.Description,
                        updatedConnection.Type,
                        updatedConnection.ConnectionCredJson,
                        updatedConnection.LastModifiedDate,
                        Guid = updatedConnection.Guid
                    });

                    _logger.LogInformation("Added Connection");
                    // Map back to DTO to return
                    return _mapper.Map<DbConnectionDto>(existingConnection);
                }
            }
        }

        public async Task<ResponseMessage> Delete(Guid guid)
        {
            _logger.LogInformation("Deleting Connection");

            // Define a list for integration names
            var sourceDatabase = new List<string>();

            // SQL query to get integration names for the given sourceConnectionId
            var sql = @"
                SELECT SourceDatabase
                FROM ConnectionIntegrations
                WHERE sourceConnectionGuid = @SourceConnectionGuid";

            // Execute the query and get the integration names
            var result = await _dbConnection.QueryAsync<string>(sql, new { SourceConnectionGuid = guid });

            // Convert the result to a list
            sourceDatabase = result.ToList();

            if (sourceDatabase.Count == 0)
            {
                // If no integrations exist, delete the DbConnection
                var deleteQuery = "DELETE FROM DbConnections WHERE Guid = @Guid";
                await _dbConnection.ExecuteAsync(deleteQuery, new { Guid = guid });

                _logger.LogInformation("Deleted Connection");
                return new ResponseMessage { IsError = false, Message = "Deleted Successfully" };
            }
            else
            {
                string message = "There is already Integration with Company " + string.Join(", ", sourceDatabase);
                _logger.LogWarning(message);
                // Return error message with the list of integration names
                return new ResponseMessage
                {
                    IsError = true,
                    Message = "Delete Failed: " + message
                };
            }
        }

        public async Task<List<JobLogEntry>> GetTop10JobLogs(string integrationId)
        {
            // SQL query to get the top 10 rows sorted by StartTime (latest first)
            string query = @"SELECT TOP 10 * FROM JobLogEntries Where ProcessId = @ProcessId ORDER BY [StartTime] DESC";
            var jobLogEntries = await _dbConnection.QueryAsync<JobLogEntry>(query, new {ProcessId = integrationId});
            return jobLogEntries.ToList();

        }

        public Dictionary<string, string> GetLastModifiedFilter(string integrationId)
        {
            var logSql = "SELECT TOP 1 StartTime FROM JobLogEntries WHERE ProcessId = @ProcessId AND Status = 'Completed' ORDER BY LogId DESC;";
            var startTime = _dbConnection.QuerySingleOrDefault<DateTime?>(logSql, new { ProcessId = integrationId });

            if (startTime == null)
            {
                return new Dictionary<string, string>() { };
            }

            return new Dictionary<string, string>() { { "lastModifiedDateTime", startTime.ToString() } };
        }

        public long GetLastModifiedTimestamp(string integrationId)
        {
            var logSql = "SELECT TOP 1 SystemRowVersion FROM JobLogEntries WHERE ProcessId = @ProcessId AND Status = 'Completed' ORDER BY LogId DESC;";
            var systemRowVersion = _dbConnection.QuerySingleOrDefault<long>(logSql, new { ProcessId = integrationId });

            return systemRowVersion;
        }

        //public Dictionary<string, string> GetLastModifiedTimestampFilter(string integrationId)
        //{
        //    var systemRowVersion = GetLastModifiedTimestamp(integrationId);
         
        //    return new Dictionary<string, string>() { { "SystemRowVersion", systemRowVersion.ToString() } };
        //}

        public int InsertJobLog(string processId)
        {
            try
            {
                var insertSql = @"INSERT INTO JobLogEntries (ProcessId, StartTime, Status)
                    OUTPUT INSERTED.LogId
                    VALUES (@ProcessId, @DateTime, 'Processing');";

                return _dbConnection.ExecuteScalar<int>(insertSql, new { ProcessId = processId, DateTime = DateTime.Now });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting JobLogEntries");
                return -1;
            }
        }

        public void UpdateJobLog(int logId, int recordCount, long maxTimestamp, string status)
        {
            if(logId < 0) { return; }

            try
            {
                var updateSql = @"
                    UPDATE JobLogEntries
                    SET Status = @Status, EndTime = @DateTime, RecordCount = @RecordCount, SystemRowVersion = @SystemRowVersion
                    WHERE LogId = @LogId;
                ";

                _dbConnection.Execute(updateSql, new { LogId = logId, RecordCount = recordCount, SystemRowVersion = maxTimestamp, Status=status, DateTime = DateTime.Now });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting JobLogEntries");
            }
        }

    }
}
