/* General styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 10px 20px;
  border: none;
  /* color: rgb(90, 96, 127); */
  color: var(--bg-color);
  box-shadow: rgba(21, 34, 50, 0.08) 0px 1px 4px 0px;
  position: sticky;
  /* Make navbar sticky */
  top: 0;
  /* Stick to the top */
  z-index: 1000;
  position: relative;
  /* Ensure it's above other content */
  font-family: "Open Sans", sans-serif;

}

/* Other styles remain unchanged */
.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  margin-left: 37px;
  /* Parent needs to be relative for absolute positioning of child */
}

.app-menu {
  position: absolute;
  top: 50px;
  left: 5px;
  z-index: 656565;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 20px;
  background-color: white;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  width: 295px;
  border-top: 1.5px solid var(--bg-color);
  border-radius: 2px 2px 8px 8px;
}

.app-launcher {
  width: 450px;
  margin: auto;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}



.app-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: var(--bg-color);
  cursor: pointer;
  padding: 10px;
  text-align: center;
  transition: color 0.3s ease, background-color 0.3s ease;
  border-radius: 8px;
  font-weight: 700;


}

.app-item:hover {
  color: white;
  background-color: var(--hover-color);
}

.app-item.active {
  background-color: var(--bg-color);
  color: white;
}

.app-item i {
  font-size: 24px;
  margin-bottom: 5px;
}

.app-item i:hover {
  color: #0056b3;
}


.lower-grid {
  margin-top: 30px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  border-top: 1px solid #ccc;
  padding-top: 20px;
}

.doc-item,
.create-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #333;
  cursor: pointer;
}

.doc-item i,
.create-item i {
  font-size: 32px;
  margin-bottom: 10px;
}


.logo img {
  height: 60px;
  margin-right: 10px;
}

.logo span {
  font-size: 1.5rem;
  line-height: 1.5;
  font-weight: 700;

}

.toogleBtn:hover {
  background-color: var(--hover-color);
  color: white !important;
}

.toogleBtn {
  background: none;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.toogleBtn:hover svg path {
  stroke: white;
}

.isMenuOpen,
svg path {
  background-color: var(--bg-color);
}

.isMenuOpen svg path {
  stroke: white;
}


::ng-deep ul.p-autocomplete-multiple-container.p-component.p-inputtext.ng-star-inserted {
  margin-bottom: 0px !important;
}

::ng-deep.p-autocomplete .p-autocomplete-multiple-container {
  padding: 0px;
  padding-left: 10px;
}

.custom-dialog-container .mat-dialog-container {
  border-radius: 0;
  /* Remove any border radius */
  padding: 0;
  box-shadow: none;
  /* Remove box shadow if desired */
}


.nav-icons {
  display: flex;
  align-items: center;
}

.flag-icon {
  height: 20px;
  margin-right: 15px;
}

.notifications {
  position: relative;
  margin-right: 20px;
}

.notifications i {
  font-size: 25px;
  color: var(--bg-color);
}

.notifications i:hover {
  color: var(--hover-color);
}

.notification-count {
  position: absolute;
  top: -15px;
  right: -10px;
  background-color: var(--hover-color);
  color: white;
  font-size: 15px;
  padding: 2px 6px;
  width: 20px;
  border-radius: 50%;
}

.userLogo {
  font-size: 32px;
  margin-right: 6px;
}

.user-profile {
  display: flex;
  align-items: center;
  position: relative;
}

.user-profile img {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.user-profile span {
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 700;
  font-family: "Open Sans", sans-serif;
}


.profileMenu {
  position: absolute;
  top: 50px;
  right: 20px;
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  color: rgb(90, 96, 127);
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.02) 0px -2.46px 3.86px 0px, rgba(0, 0, 0, 0.02) 0px 2.258px 4.692px 0px, rgba(0, 0, 0, 0.03) 0px 6.147px 9.475px 0px, rgba(0, 0, 0, 0.04) 4px 18px 18px 0px;
  font-size: 17px;
  border-top: 1.5px solid var(--bg-color);
  border-radius: 2px 2px 8px 8px;

}

.profileMenu>li {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1vw;
  list-style: none;
  padding: 06px 1vw;
}

.profileMenu>li:hover {
  text-decoration: none;
  background-color: var(--hover-color);
  color: white;
}

.profileMenu>div:last-child {
  display: flex;
  align-items: center;
  justify-content: center;
}

.profileMenu button {
  color: var(--bg-color);
  outline: none;
  font-size: 0.875rem;
  line-height: 1.4286;
  font-weight: 400;
  font-family: Inter, "Open Sans", sans-serif;
  width: 80%;
  margin: 12px 0px 0px;
  transition: box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1), border-color 250ms cubic-bezier(0.4, 0, 0.2, 1), color 250ms cubic-bezier(0.4, 0, 0.2, 1);

  border-radius: 4px;
  border: 1px solid rgb(215, 219, 236);
  background: white;
  padding: 4px 20px;
}

.profileMenu button:hover {
  text-decoration: none;
  background-color: var(--hover-color);
  border: 1px solid var(--hover-color);
  color: white;
}

/* Mobile styles */
@media (max-width: 753px) {

  .search-bar input,
  .logo span,
  .nav-icons .flag-icon,
  .user-profile span {
    display: none;
  }

  .logo img {
    height: 40px;
  }

  .user-profile img {
    height: 30px;
    width: 30px;
  }

  .notifications {
    margin-right: 10px;
  }

  .navbar {
    padding: 10px 15px;
  }

  .profileMenu {

    font-size: 14px;
  }
}


@media (max-width: 600px) {
  .app-item {
    font-size: 10px;
    padding: 8px;
  }

  .app-item i {
    font-size: 20px;
  }
}
