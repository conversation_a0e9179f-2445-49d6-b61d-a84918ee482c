<div class="card p-2">
  <button class="backBtn btn" (click)="goBack()"><i class="fa-solid fa-arrow-left"></i> </button>
  <p-stepper [linear]="true">
    <p-stepperPanel header="Source">
      <ng-template pTemplate="content" let-nextCallback="nextCallback" let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
            <div class="form-section">
              <div class="form-section">
                <p style="font-weight: bold;">Select Source Connection</p>
                <p-dropdown [options]="connections" optionLabel="connectionName" [(ngModel)]="sourceConnection"
                  [style]="{'width':'100%'}" (onChange)="onSourceConnectionSelected($event.value)"
                  placeholder="Select Source Connection" required #sourceConnectionRef="ngModel" [loading]="dropdownLoading.conections">
                </p-dropdown>

                <!-- Error message shown if sourceConnection is invalid and touched -->
                <div *ngIf="sourceConnectionRef?.errors?.['required']" class="error-message">
                  Source connection is required.
                </div>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Source Database</p>
                <p-dropdown [options]="databases" [(ngModel)]="sourceDatase" [style]="{'width':'100%'}"
                  (onChange)="onSourceDatabaseSelected($event.value)" placeholder="Select Source Database" filter="true"
                  filterPlaceholder="Search..." required #sourceDatabaseRef="ngModel" [loading]="dropdownLoading.database">
                </p-dropdown>

                <!-- Error message shown if sourceDatase is invalid and touched -->
                <div *ngIf="sourceDatabaseRef?.errors?.['required']" class="error-message">
                  Source database is required.
                </div>

              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Source Tables</p>

                <!-- PrimeNG MultiSelect -->
                <p-multiSelect [options]="tables" [(ngModel)]="sourceSelectedTables"
                  (onChange)="addTables($event.value)" placeholder="Select Source Tables" class="w-100" required
                  #sourceTablesRef="ngModel" name="sourceTable" [loading]="dropdownLoading.tables" >
                </p-multiSelect>

                <!-- Error message shown if sourceSelectedTables is invalid and touched -->
                <div *ngIf="sourceTablesRef?.errors?.['required'] " class="error-message">
                  At least one table must be selected.
                </div>
                <!-- PrimeNG Chips to display selected tables -->
                <!-- <p-chips [(ngModel)]="sourcesTableLists" [removable]="true" (onRemove)="remove($event)"
                  [style]="{'margin-top': '10px'}" name="sourceTable">
                </p-chips> -->
              </div>

            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-end">
          <button class="btn btn-primary" (click)="nextCallback.emit()" style="background-color: var(--bg-color);"
            [disabled]="!sourceConnection || !sourceDatase || !sourceSelectedTables?.length">
            Next
            <i class="pi pi-arrow-right"></i>
          </button>
        </div>
      </ng-template>
    </p-stepperPanel>

    <p-stepperPanel header="Destination">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-nextCallback="nextCallback"
        let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
            <div class="form-section">

              <div class="form-section">
                <p style="font-weight: bold;">Select Destination Connection</p>
                <p-dropdown [options]="connections" optionLabel="connectionName" [(ngModel)]="destinationConnection"
                  (onChange)="onDestinationConnectionSelected($event.value)" placeholder="Select Connection"
                  [style]="{'width':'100%'}" required #destinationConnectionRef="ngModel" >
                </p-dropdown>

                <!-- Error message if destinationConnection is invalid and touched -->
                <div *ngIf="destinationConnectionRef?.errors?.['required'] " class="error-message">
                  Destination connection is required.
                </div>
              </div>

              <div class="form-section">
                <p style="font-weight: bold;">Select Database</p>
                <p-dropdown [options]="destinationDatabases" [(ngModel)]="destinationDatase"
                  (onChange)="onDestinationDatabaseSelected($event.value)" placeholder="Select Database"
                  [style]="{'width':'100%'}" filter="true" filterPlaceholder="Search..." required
                  #destinationDatabaseRef="ngModel" [loading]="dropdownLoading.destinationDatabase">
                </p-dropdown>

                <!-- Error message shown if destinationDatase is invalid and touched -->
                <div *ngIf="destinationDatabaseRef?.errors?.['required'] " class="error-message">
                  Destination database is required.
                </div>

              </div>


              <p-table class="mapping-table" [value]="sourceMappingsTable" tableStyleClass="table table-bordered">
                <ng-template pTemplate="header">
                  <tr>
                    <th>Source Table</th>
                    <th>Destination Table</th>
                    <th>Actions</th>
                  </tr>
                </ng-template>

                <ng-template pTemplate="body" let-mapping let-i="rowIndex">
                  <tr>
                    <td>
                      <p-dropdown [options]="sourcesTableLists" [(ngModel)]="mapping.sourceTable"
                        placeholder="Select Source Column">
                      </p-dropdown>
                    </td>
                    <td>
                      <p-dropdown [options]="destinationTables" [(ngModel)]="mapping.destinationTable"
                        placeholder="Select Destination Column" filter="true" filterPlaceholder="Search..."
                        [showClear]="true">
                      </p-dropdown>
                    </td>
                    <td>
                      <button type="button" class="btn action-btn map-btn" (click)="mappingTable(mapping, i)"
                        title="Map">
                        <i class="fa-solid fa-map-pin"></i>
                      </button>
                      <button type="button" class="btn action-btn delete-btn mx-2" (click)="removetable(i)"
                        title="Delete">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </td>
                  </tr>
                </ng-template>
              </p-table>



            </div>
          </div>
        </div>
        <div class="d-flex pt-4 justify-content-between">
          <button class="btn btn-secondary" (click)="prevCallback.emit()"> <i
              class="pi pi-arrow-left mx-1"></i>Back</button>

          <button class="btn btn-primary" (click)="nextCallback.emit()" style="background-color: var(--bg-color);"
            [disabled]="!destinationDatabaseRef || !destinationConnectionRef ">
            Next
            <i class="pi pi-arrow-right"></i>
          </button>
        </div>
      </ng-template>
    </p-stepperPanel>

    <p-stepperPanel header="Schedule Job">
      <ng-template pTemplate="content" let-prevCallback="prevCallback" let-index="index">
        <div class="flex flex-column h-12rem">
          <div
            class="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">

            <div class="form-section" style="width: 400px;">
              <p style="font-weight: bold;">Job Frequency</p>
              <p-dropdown [options]="jobTypes" [(ngModel)]="connectionData.jobFrequency"
                placeholder="Select Job Frequency" [style]="{'width':'100%'}">
              </p-dropdown>
            </div>

            <div class="form-group mt-2 w-100 d-flex justify-content-between">
              <button class="btn btn-secondary" (click)="prevCallback.emit()"> <i class="pi pi-arrow-left mx-1"></i>
                Back</button>
              <button (click)="save()"
                class="btn btn-primary ms-auto d-flex justify-content-center align-items-center gap-2 "
                style="background-color: var(--bg-color);" [disabled]="isSaving">Save
                <span *ngIf="isSaving" class="loader"><i class="pi pi-spin pi-spinner"
                    style="font-size: 1.2rem"></i></span></button>

            </div>
          </div>
        </div>
      </ng-template>
    </p-stepperPanel>
  </p-stepper>

  <p-toast position="bottom-right" key="br" />
</div>
