{"ConnectionStrings": {"AdminConnection": "Server=NC1-GaneshT540P; Database=AdminDB; User ID=tms-local; Password=password@7894!;TrustServerCertificate=True"}, "Logging": {"LogLevel": {"Default": "Error", "Microsoft.AspNetCore": "Error"}}, "Serilog": {"Using": ["Serilog.Sinks.MSSqlServer"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Hangfire": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "MSSqlServer", "Args": {"connectionString": "AdminConnection", "tableName": "AppLogs", "autoCreateSqlTable": true, "columnOptionsSection": {"addStandardColumns": ["LogEvent", "TraceId", "SpanId"], "removeStandardColumns": ["MessageTemplate", "Properties"]}}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithExceptionDetails"]}, "CorsOrigins": "http://localhost:4200,http://************:9595", "AllowedHosts": "*", "OpenAI": {"ApiKey": "***************************************************", "ChatModelId": "gpt-4o-mini", "EmbeddingModelId": "text-embedding-3-small"}, "DataProcessing": {"DeleteStagingTableAfterLoad": "Yes"}}