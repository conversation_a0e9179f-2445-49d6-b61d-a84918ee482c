<div class="page-header">
  <div class="d-flex gap-2 align-items-center  ">

    <h2>SQL Integration</h2>
    <input [(ngModel)]="searchBySourceTable" (input)="updateSearchFilter($event)" class="form-control mr-sm-2"
      placeholder="Search by Source Table" style="width: 221px;padding: 9px .75rem;" />

    <button *ngIf="isFilterAdded"
      style="display: flex; align-items: center; justify-content: center; border: none; outline: none;"
      (click)="clearAllFilter()">
      <i class="pi pi-times"></i>
    </button>
  </div>
  <button class="Btn" (click)="navigateToIntegration()">
    <i class="fa-solid fa-plus" style="margin-right: 5px;"></i>Add Integration
  </button>

</div>
<div class="card compact-table">
  @if (isDataLoaded) {
  @if (groupedTableData.length) {

  <!-- <p-table [value]="groupedTableData" [rows]="10" [paginator]="tableData.length > 10" [showCurrentPageReport]="false"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [rowsPerPageOptions]="[10, 25]"> -->
  <p-table [value]="groupedTableData">
    <!-- Header Section -->
    <ng-template pTemplate="header">
      <tr>
        <th>Last Run</th>
        <th>Source DB</th>
        <th>Source Table</th>
        <th>Destination Database</th>
        <th>Destination Table</th>
        <th>Action</th>
      </tr>
    </ng-template>

    <!-- Body Section -->
    <ng-template pTemplate="body" let-group>
      <!-- Group Row -->
      <tr>
        <td colspan="6" class="font-weight-bold py-0">
          <h3 class="mb-0 d-flex gap-3 align-items-center" (click)="toggleGroup(group)"
            style="cursor: pointer; padding: 8px 0; font-weight: 500;">
            <i class="fas" [ngClass]="{'fa-chevron-down': !group.isExpanded, 'fa-chevron-right': group.isExpanded}"></i>
            <span>Destination: {{ group.destinationDatabase }}</span>
          </h3>
        </td>
      </tr>

      <!-- Group Items -->
      <ng-container *ngIf="!group.isExpanded">
        <ng-container *ngFor="let item of group.items">
          <tr [ngClass]="{'executing-row': executingJobs.has(item.guid)}">
            <td>{{item.lastExecutionDate}}</td>
            <td>{{ item.sourceDatabase }}</td>
            <td>{{ item.sourceTable }}</td>
            <td>{{ item.destinationDatabase }}</td>
            <td>{{ item.destinationTable }}</td>

            <td>
              <div class="d-flex justify-content-center">
                <div class="d-flex align-items-center justify-content-center gap-1 dataflow " style="cursor: pointer;"
                  (click)="showJobLogsDialog(item)">
                  <span><img [src]="'../../assets/img/sql-logo.png' " style="width: 20px;" /></span>
                  <i [class]="'pi pi-arrow-right'"></i>
                  <span><img src="../../assets/img/sql-logo.png" style="width: 20px;" /></span>
                </div>
                <button class="activeBtn" (click)="activeJobOrInactive(item)"
                  [title]="item.isActive ? 'Active' : 'Inactive'"
                  [ngStyle]="{'color': item.isActive ? 'green' : 'DimGray'}">

                  <i class="fa-solid" style="font-size: 16px;"
                    [ngClass]="{'fa-toggle-on': item.isActive, 'fa-toggle-off': !item.isActive}"></i>
                </button>

                <button class="action-button edit-button" (click)="editItem(item.guid)" title="Edit">
                  <i class="fas fa-edit" style="font-size: 16px;"></i>
                </button>
                <button class="action-button delete-button" (click)="deleteClicked(item.guid)" title="Delete"
                  style="margin-left: 5px; font-size:16px;">
                  <i class="fas fa-trash-alt" style="font-size: 16px;"></i>
                </button>
                <button class="action-button btn" [title]="executingJobs.has(item.guid) ? 'Stop' : 'Execute'"
                  [style.color]="executingJobs.has(item.guid) ? 'red' : 'var(--bg-color)'"
                  (click)="executingJobs.has(item.guid) ? stopJob(item.guid) : executeJob(item.guid)">
                  <i class="fas" [class.fa-play]="!executingJobs.has(item.guid)"
                    [class.fa-stop]="executingJobs.has(item.guid)"></i>
                </button>
              </div>
            </td>
          </tr>
        </ng-container>
      </ng-container>
    </ng-template>
  </p-table>
  }@else {
  <div class="d-flex align-items-center justify-content-center " style="height: 50vh;">

    <span style="font-size: 1.5rem;font-weight: 500;">No relevant search results found...</span>
  </div>
  }}@else {
  <div style="    height: 45vh;
  display: flex
  ;
  align-items: center;
  justify-content: center;">

    <span class="loader"><i class="pi pi-spin pi-spinner" style="font-size: 5rem"></i></span>
  </div>
  }

</div>
