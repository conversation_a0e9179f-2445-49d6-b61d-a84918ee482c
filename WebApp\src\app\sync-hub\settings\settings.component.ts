import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AppSettingServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { MessageService } from 'primeng/api';
import { RippleModule } from 'primeng/ripple';
import { ToastModule } from 'primeng/toast';
@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [ButtonModule, InputTextModule, FormsModule, CommonModule, ToastModule, ButtonModule, RippleModule],
  providers: [MessageService],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.css'
})
export class SettingsComponent {
  constructor(private appSettingService: AppSettingServiceProxy, private messageService: MessageService) {

  }
  url: string = '';
  isInputShown = false

  async ngOnInit() {
    var res = await this.appSettingService.getWebhookNotificationUrl().toPromise();
    console.log(res);

    if (res.isError == true) {
      this.url = '';
    } else {
      this.url = res.message;
    }
  }

  addWebHook() {
    this.isInputShown = true
    this.url = ''
  }

  async updateUrl(event: Event) {
    event.stopPropagation()

    try {
      var res = await this.appSettingService.addOrEditWebHookNotification(this.url).toPromise();

      if (res.isError == true) {
        this.messageService.clear();
        this.messageService.add({ severity: 'error', detail: res.message, key: 'br', life: 3000 });

      } else {
        this.messageService.clear();
        this.messageService.add({ severity: 'success', detail: res.message, key: 'br', life: 3000 });
      }
    } catch (error) {
      this.messageService.clear();
      this.messageService.add({ severity: 'error', detail: "Failed to update webhook", key: 'br', life: 3000 });
    }
  }
}
