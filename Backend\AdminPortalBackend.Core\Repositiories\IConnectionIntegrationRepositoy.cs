﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Repositiories
{
    public interface IConnectionIntegrationRepository
    {
        Task<ResponseMessageList> GetDatabase(Guid guid);
        Task<ResponseMessageList> GetTables(Guid guid, string databaseName);
        Task<ResponseMessageList> GetColumns(Guid guid, string databaseName, string tableName);

        Task<ResponseMessage> Create(CreateConnectionIntegrationDto integration);
        Task<ConnectionIntegration> Edit(ConnectionIntegration integration);
        Task<List<ConnectionIntegration>> GetSqlSqlAllAsync();
        Task<List<ConnectionIntegration>> GetBcSqlAllAsync();
        Task<ConnectionIntegration> GetByIdAsync(Guid guid);
        Task<ConnectionIntegration> DeleteAsync(Guid guid);
        Task<ResponseMessage> ScheduleJob(JobRequest request);
        Task<ResponseMessage> DisableJob(Guid jobId);
        Task<ResponseMessage> DeleteJob(Guid jobId);
        Task<ResponseMessage> ExecuteJob(Guid integrationGuid);
        Task<ResponseMessageList> GetEntityNames(Guid guid);
        Task<ResponseMessageList> GetPropertiesForEntity(string entityName, Guid guid);
        Task<ResponseMessageList> GetCompanyNames(Guid guid);
        Task<ResponseMessage> CreateTableForODataWebService(CreateConnectionIntegrationDto integration, bool isExecute);
        Task<ResponseMessage> CreateTrigger(Guid integrationGuid, Guid destinationGuid, string databaseName, string tableName, Guid SourceGuid);
        Task<ResponseMessage> DeleteTrigger(Guid destinationGuid, string databaseName, string tableName);
        Task<ResponseMessage> EditTableForODataWebService(ConnectionIntegration integration);
        Task<ResponseMessageList> CheckTableExist(List<string> table, Guid destinationGuid, string destinationDatabase);
        Task<ResponseMessage> GetCompanyId(Guid connectionGuid, string companyName);
        Task<ResponseMessage> IsSystemRowVersionAvailable(string entityName, Guid guid);
        Task<ResponseMessage> CreateView(CreateViewDto request);
        Task<ResponseMessage> CheckIfViewExists(IsViewDto request);
        Task<ResponseMessageList> GetFieldNames(string tableName);
        Task<ResponseMessageList> GetTableNames();
        Task<ServiceField> GetFieldFromService(string serviceName);
        Task<ResponseMessage> ProcessSqlDelete();
        Task<ResponseMessage> GetDestinationPrimaryKey(string sourceGuid);
    }
}
