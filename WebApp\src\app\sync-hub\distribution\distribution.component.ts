import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { PanelModule } from 'primeng/panel';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'app-distribution',
  standalone: true,
  imports: [
    TableModule,
    CheckboxModule,
    ButtonModule,
    PanelModule, CommonModule, CheckboxModule, FormsModule],
  templateUrl: './distribution.component.html',
  styleUrl: './distribution.component.css'
})
export class DistributionComponent {
  data = [
    { Heading: "Personal Information", FieldName: "firstName", description: "User's first name", isChecked: true },
    { Heading: "Contact Details", FieldName: "phone", description: "User's phone number", isChecked: true },
    { Heading: "Personal Information", FieldName: "lastName", description: "User's last name", isChecked: false },
    { Heading: "Social Media Links", FieldName: "twitter", description: "Twitter handle", isChecked: false },
    { Heading: "Contact Details", FieldName: "email", description: "User's email address", isChecked: true },
    { Heading: "Contact Details", FieldName: "city", description: "User's city of residence", isChecked: false },
    { Heading: "Educational Details", FieldName: "graduationYear", description: "Graduation year", isChecked: true },
    { Heading: "Contact Details", FieldName: "postalCode", description: "User's postal code", isChecked: true },
    { Heading: "Personal Information", FieldName: "dateOfBirth", description: "User's date of birth", isChecked: true },
    { Heading: "Educational Details", FieldName: "certifications", description: "User's certifications", isChecked: false },
    { Heading: "Account Settings", FieldName: "password", description: "User's account password", isChecked: false },
    { Heading: "Educational Details", FieldName: "institutionName", description: "Name of institution", isChecked: false },
    { Heading: "Preferences", FieldName: "notificationSettings", description: "User's notification preferences", isChecked: true },
    { Heading: "Employment Information", FieldName: "jobTitle", description: "User's job title", isChecked: true },
    { Heading: "Preferences", FieldName: "language", description: "Preferred language", isChecked: true },
    { Heading: "Account Settings", FieldName: "username", description: "User's username", isChecked: true },
    { Heading: "Preferences", FieldName: "theme", description: "UI theme preference", isChecked: false },
    { Heading: "Educational Details", FieldName: "highestQualification", description: "Highest qualification", isChecked: true },
    { Heading: "Social Media Links", FieldName: "linkedin", description: "LinkedIn profile URL", isChecked: true },
    { Heading: "Emergency Contact", FieldName: "contactName", description: "Emergency contact's name", isChecked: true },
  ];

  distributionGroups = [];

  ngOnInit() {
    // data value came from api call
    this.distributeData();
    this.distributeData();
  }

  distributeData() {
    const groupedData = this.data.reduce((acc, curr) => {
      const group = acc.find(item => item.heading === curr.Heading);
      if (group) {
        group.items.push({ label: curr.FieldName, description: curr.description, isChecked: curr.isChecked });
      } else {
        acc.push({ heading: curr.Heading, items: [{ label: curr.FieldName, description: curr.description, isChecked: curr.isChecked }] });
      }
      return acc;
    }, []);

    this.distributionGroups = groupedData;
  }

  save() {
    // Loop through the distributionGroups and find the updated isChecked values
    var updatedData = [];
    this.distributionGroups.forEach(group => {
      group.items.forEach(item => {
        const originalItem = this.data.find(dataItem => dataItem.FieldName === item.label && dataItem.Heading === group.heading);

        if (originalItem && originalItem.isChecked !== item.isChecked) {
          originalItem.isChecked = item.isChecked;

          updatedData.push(originalItem)
        }
      });
    });

    //console.log('all Data', this.data);
    //console.log('updated Data', updatedData);

  }
}
