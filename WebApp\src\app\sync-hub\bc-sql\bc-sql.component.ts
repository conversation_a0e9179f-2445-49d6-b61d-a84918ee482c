import { Component, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { FormsModule } from '@angular/forms';
import { ListboxModule } from 'primeng/listbox';
import { ATestServiceProxy, ConnectionIntegrationServiceProxy, CreateConnectionIntegrationDto, DbConnectionDto, DbConnectionServiceProxy, JobRequest } from '../../../shared/service-proxies/service-proxies';
import { DropdownModule } from 'primeng/dropdown';
import { CommonModule } from '@angular/common';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TooltipModule } from 'primeng/tooltip';
import { InputTextModule } from 'primeng/inputtext';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { ToastModule } from 'primeng/toast';
import { ToasterService } from '../../toaster.service';
import { AddViewDialogComponent } from '../dialog/add-view-dialog/add-view-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { IViewRefrence } from '../../app.interface';
import { AutoCompleteModule } from 'primeng/autocomplete';


interface AutoCompleteCompleteEvent {
  originalEvent: Event;
  query: string;
}

@Component({
  selector: 'app-bc-sql',
  standalone: true,
  imports: [ButtonModule, TabViewModule, FormsModule, ListboxModule, DropdownModule, CommonModule, ProgressSpinnerModule, TooltipModule, ToastModule, AutoCompleteModule],
  templateUrl: './bc-sql.component.html',
  styleUrl: './bc-sql.component.css'
})
export class BcSqlComponent implements OnInit {
  loading: boolean = false;
  compnayLoading: boolean = false;
  isSaving = false
  constructor(private integrationService: ConnectionIntegrationServiceProxy, private _dbconnectionServices: DbConnectionServiceProxy, private _aTestService: ATestServiceProxy, private toasterService: ToasterService, private location: Location, private router: Router,
    private dialog: MatDialog) {

  }

  sourceName: string = "";
  sourceGuid: string;
  companyNames: string[] = []
  selectedCompanyName: string[] = [];
  entityNames: string[] = []
  selectedEntityNames: string[] = [];
  bcConnections: DbConnectionDto[] = [];
  sqlConnections: DbConnectionDto[] = [];
  connection: DbConnectionDto
  destinationConnectionName: string = '';
  destinationGuid: string | null = null;
  destinationDatabases: string[] = [];
  destinationDatabase: string = '';
  activeIndex: number = 0;

  searchedEntities = []
  isSearchedResult = false

  selectedEntitiesTable: any[] = []
  existingEntities = []
  dropdownLoading = {
    source: false,
    company: false,
    entities: false,
    connection: false,
    database: false
  }

  ngOnInit() {

    //console.log(this.existingEntities.length == 0);


    // this.integrationService.getCompanyNames().subscribe(res => {
    //   if (res.isError == false) {
    //     this.companyNames = res.message;
    //     //console.log(this.companyNames);

    //   }
    // })
    // load all Entit Data
    //this.LoadAllEntities()
    this.dropdownLoading.source=true
    this.dropdownLoading.connection=true
    this._dbconnectionServices.getAll().subscribe(res => {
      this.bcConnections = res.filter(r => r.type.startsWith('BC'));
      this.sqlConnections = res.filter(r => !r.type.startsWith('BC'));
      this.dropdownLoading.connection=false
      this.dropdownLoading.source=false
      ////console.log(this.connections)
    });


  }

  LoadAllEntities() {
    this.integrationService.getEntityNames(this.sourceGuid).subscribe(res => {
      if (res.isError == false) {
        this.entityNames = res.message;
        this.searchedEntities = [...this.entityNames]
        //console.log(this.entityNames);
      }
    })
  }
  goBack() {
    this.location.back()
  }

  onSourceSelected(sourceSelected: DbConnectionDto): void {
    //console.log(sourceSelected)
    if (sourceSelected && sourceSelected.guid) {
      this.sourceName = sourceSelected.connectionName
      this.sourceGuid = sourceSelected.guid
      this.loadCompanies();
      this.LoadAllEntities();
    } else {
      //console.error('No valid source selected for source.');
    }
  }

  onSourceConnectionSelected(selectedConnection: DbConnectionDto): void {
    //console.log(selectedConnection)
    if (selectedConnection && selectedConnection.guid) {
      this.destinationConnectionName = selectedConnection.connectionName;
      this.destinationGuid = selectedConnection.guid;
      this.loadDatabases();
    } else {
      //console.error('No valid connection selected for source.');
    }
  }

  loadCompanies(): void {
    this.integrationService.getCompanyNames(this.sourceGuid).subscribe(res => {
      if (res.isError == false) {
        this.companyNames = res.message;
        //console.log(this.companyNames);

      }
    })
  }

  loadDatabases(): void {
    if (this.destinationGuid) {
      this.dropdownLoading.database=true
      this.integrationService.getDatabase(this.destinationGuid).subscribe(
        (response) => {
          this.destinationDatabases = response.message;
          this.dropdownLoading.database=false

        },
        (error) => {
          //console.error('Error fetching source databases:', error);
        }
      );
    }
  }

  //Search Funtionlity
  searchEntities(event: Event) {
    let query = (event.target as HTMLInputElement).value.toLowerCase();

    this.searchedEntities = this.entityNames.filter(item =>
      item.toLowerCase().startsWith(query.toLowerCase())
    );
    //console.log(this.searchedEntities.length);

    if (!this.searchedEntities.length) {
      this.isSearchedResult = true
    } else {
      this.isSearchedResult = false
    }
  }
  // Refresh Entities
  async refreshEntities() {
    this.loading = true;
    await this._aTestService.entities(this.sourceGuid).toPromise();
    await this._aTestService.metadata(this.sourceGuid).toPromise();
    this.LoadAllEntities();
    this.loading = false;
    this.selectedEntityNames = []
  }
  // Refresh Companies
  async refreshCompany() {
    this.compnayLoading = true;
    await this._aTestService.companies(this.sourceGuid).toPromise();
    this.LoadAllEntities();
    this.compnayLoading = false;
    this.selectedCompanyName = []
  }

  async save(isExecute: boolean) {
    try {
      this.isSaving = true
      var connectionIntegrationDto = new CreateConnectionIntegrationDto({
        sourceConnectionGuid: this.sourceGuid,
        sourceConnectionName: this.sourceName,
        sourceDatabase: this.selectedCompanyName.join('@#'),
        sourceTable: this.selectedEntityNames,
        destinationConnectionGuid: this.destinationGuid,
        destinationConnectionName: this.destinationConnectionName,
        destinationDatabase: this.destinationDatabase,
        destinationTable: [],
        sourcePrimaryKey: [],
        destinationPrimaryKey: [],
        jobFrequency: "On Demand",
        mappedColumns: [],
        settings: JSON.stringify({ BcToSql: 'Clean Load', SqlToBc: false })
      })
      this.existingEntities = [];
      this.selectedEntitiesTable = [];
      let sourceDatabase = connectionIntegrationDto.sourceDatabase.split("@#")
      let sourceTable = connectionIntegrationDto.sourceTable
      for (let i = 0; i < sourceDatabase.length; i++) {
        let element = sourceDatabase[i];
        for (let j = 0; j < sourceTable.length; j++) {
          const el = element + "_" + sourceTable[j];
          this.selectedEntitiesTable.push(el)
        }
      }
      // get all company_entityName
      // api call
      //console.log(connectionIntegrationDto);

      let res = await this.integrationService.checkTableExist(this.destinationGuid, this.destinationDatabase, this.selectedEntitiesTable).toPromise()
      //console.log(res);
      if (res.message.length) {
        //console.log(res.message);
        this.existingEntities = res.message
        this.isSaving = false
      } else {
        this.existingEntities = []
        this.integrationService.createTableForODataWebService(isExecute, connectionIntegrationDto).subscribe((res) => {
          this.router.navigateByUrl('/sync-hub/bc-sql-list')
          this.isSaving = false
          this.toasterService.showToaster('success', "Data is added")
        })
      }
    }

    catch (error) {
      this.isSaving = false

      this.toasterService.showToaster('error', error.message)
    }

  }

  isDropdownOpen: boolean = false;

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

}
