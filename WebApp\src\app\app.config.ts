import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';
import { API_BASE_URL } from '../shared/service-proxies/service-proxies';
import { provideToastr } from 'ngx-toastr';
import { MarkdownModule } from 'ngx-markdown';
import { MessageService } from 'primeng/api';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';


export function getRemoteServiceBaseUrl(): string {
  let url = (window as any).location.host;
  if (url.indexOf("localhost") >= 0)
    return 'https://localhost:7136';
  else
    return `${window.location.protocol}//${url.split(":")[0]}:7575`;
}


export const appConfig: ApplicationConfig = {
  providers: [provideRouter(routes), provideClientHydration(), provideAnimations(), provideHttpClient(withFetch()),
  { provide: API_BASE_URL, useFactory: getRemoteServiceBaseUrl },
  provideToastr({
    timeOut: 3000,
    positionClass: 'toast-bottom-right',
    preventDuplicates: true,
    closeButton: true,
    progressBar: true

  }),{
    provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
    useValue: {
      subscriptSizing: 'dynamic'
    }
  }, MessageService,
  importProvidersFrom(
    MarkdownModule.forRoot()  // Correctly use forRoot() with importProvidersFrom
  )
  ]
};
