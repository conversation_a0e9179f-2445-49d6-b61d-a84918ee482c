{"version": 3, "sources": ["../../../../../node_modules/subsink/dist/subsink.js", "../../../../../node_modules/subsink/dist/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar isFunction = function (fn) { return typeof fn === 'function'; };\n/**\n * Subscription sink that holds Observable subscriptions\n * until you call unsubscribe on it in ngOnDestroy.\n */\nvar SubSink = /** @class */ (function () {\n    /**\n     * Subscription sink that holds Observable subscriptions\n     * until you call unsubscribe on it in ngOnDestroy.\n     *\n     * @example\n     * In Angular:\n     * ```\n     *   private subs = new SubSink();\n     *   ...\n     *   this.subs.sink = observable$.subscribe(...)\n     *   this.subs.add(observable$.subscribe(...));\n     *   ...\n     *   ngOnDestroy() {\n     *     this.subs.unsubscribe();\n     *   }\n     * ```\n     */\n    function SubSink() {\n        this._subs = [];\n    }\n    /**\n     * Add subscriptions to the tracked subscriptions\n     * @example\n     *  this.subs.add(observable$.subscribe(...));\n     */\n    SubSink.prototype.add = function () {\n        var subscriptions = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            subscriptions[_i] = arguments[_i];\n        }\n        this._subs = this._subs.concat(subscriptions);\n    };\n    Object.defineProperty(SubSink.prototype, \"sink\", {\n        /**\n         * Assign subscription to this sink to add it to the tracked subscriptions\n         * @example\n         *  this.subs.sink = observable$.subscribe(...);\n         */\n        set: function (subscription) {\n            this._subs.push(subscription);\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * Unsubscribe to all subscriptions in ngOnDestroy()\n     * @example\n     *   ngOnDestroy() {\n     *     this.subs.unsubscribe();\n     *   }\n     */\n    SubSink.prototype.unsubscribe = function () {\n        this._subs.forEach(function (sub) { return sub && isFunction(sub.unsubscribe) && sub.unsubscribe(); });\n        this._subs = [];\n    };\n    return SubSink;\n}());\nexports.SubSink = SubSink;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar subsink_1 = require(\"./subsink\");\nexports.SubSink = subsink_1.SubSink;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,aAAa,SAAU,IAAI;AAAE,aAAO,OAAO,OAAO;AAAA,IAAY;AAKlE,QAAI;AAAA;AAAA,MAAyB,WAAY;AAkBrC,iBAASA,WAAU;AACf,eAAK,QAAQ,CAAC;AAAA,QAClB;AAMA,QAAAA,SAAQ,UAAU,MAAM,WAAY;AAChC,cAAI,gBAAgB,CAAC;AACrB,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,0BAAc,EAAE,IAAI,UAAU,EAAE;AAAA,UACpC;AACA,eAAK,QAAQ,KAAK,MAAM,OAAO,aAAa;AAAA,QAChD;AACA,eAAO,eAAeA,SAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAM7C,KAAK,SAAU,cAAc;AACzB,iBAAK,MAAM,KAAK,YAAY;AAAA,UAChC;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AAQD,QAAAA,SAAQ,UAAU,cAAc,WAAY;AACxC,eAAK,MAAM,QAAQ,SAAU,KAAK;AAAE,mBAAO,OAAO,WAAW,IAAI,WAAW,KAAK,IAAI,YAAY;AAAA,UAAG,CAAC;AACrG,eAAK,QAAQ,CAAC;AAAA,QAClB;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,UAAU;AAAA;AAAA;;;ACjElB;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,YAAY;AAChB,YAAQ,UAAU,UAAU;AAAA;AAAA;", "names": ["SubSink"]}