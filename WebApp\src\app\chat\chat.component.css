.middleCont {
  color: black;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

.messageBox {
  overflow-y: auto;
  scroll-behavior: smooth;
}

.messageBox::-webkit-scrollbar {
  width: 10px;
}

.messageBox::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}

.messageBox::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.messageBox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}




.defaultSms {
  margin-top: 10px;
  background: #03354c;
  padding-right: 10px;
  text-align: start;
  align-items: flex-end;
  padding: 5px 12px;
  border-radius: 8px;
  line-height: 1.3rem;
  color: white;
}


.messageCont {
  display: flex;
  justify-content: start;
  align-items: start;
  flex-direction: column;
  width: 100%;
  margin: 0 auto;
  padding: 4px 0;
}

.messageCont .top {
  display: flex;
  /* justify-content: center; */
  align-items: baseline;
}

.messageCont .top .logo {
  width: 36px;
  margin-right: 5px;
  border-radius: 50%;
}

.messageCont.incommingMessage .top .logo {
  background-color: #0c0c0c;
  padding: 6px;
}

.messageCont .top .logo h5 {
  color: white;
  text-align: center;
  padding: 10px;
  width: 100%;
  overflow: hidden;
  background: red;
  border-radius: 50%;
  width: 39px;
  font-size: large;
  background: #03354c;
}

.messageCont.incommingMessage .top .logo img {
  filter: invert(100%);
}

.messageCont .top .name {
  font-weight: 700;
  font-size: 16px;
}

.messageCont .message {
  margin: 5px;
  font-size: 15px;
  /* margin: 10px 15px; */
  font-size: 15px;
  padding: 12px 18px;
  border-radius: 18px;
  max-width: 90%;
  word-wrap: break-word;
  font-family: 'Arial', sans-serif;
  box-shadow: 0px 3px 7px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
  text-align: left;

}

.outgoingMessage {
  padding-right: 10px;
  text-align: end;
  align-items: flex-end;
}

.outgoingMessage .message {
  display: inline;
  background: #195b8f;
  padding-right: 10px;
  text-align: start;
  align-items: flex-end;
  margin-left: 50px;
  padding: 8px 12px;
  border-radius: 8px;
  line-height: 1.75rem;
  color: white;
}

.incommingMessage {
  text-align: start;
  align-items: flex-start;
}

.incommingMessage .message {
  display: inline-block;
  text-align: left;
  padding: 8px 12px;
  border-radius: 8px;
  background-color: #f7f8ff;
  line-height: 1.75rem;
}

svg {
  background-color: black;
  color: white;
  padding: 12px;
  border-radius: 50%;
  margin-right: 8px;
}



.preMessageCont {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.preMessageCont .preMessage {
  padding: 2px 10px;
  margin: 4px;
  margin-right: 6px;
  position: relative;
  cursor: pointer;
  background-color: #03354c;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
  display: inline-block;
  transition: background-color 0.3s;
}


.preMessage:hover {
  /* background-color: #0f0909; */
  opacity: 0.8;
  color: white !important;
}

.preMessage .title {
  font-weight: bold;
}

.preMessage .text {
  color: #666;
}

.sendPreIcon {
  color: #999;
  margin-left: 5px;
}

.preMessageCont .preMessage .sendPreIcon {
  background-color: white;
  color: black;
  padding: 8px;
  border-radius: 8px;
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translate(-10px, -50%);
  display: none;
}



.preMessage:hover .text {
  color: #ffffff !important;
}

.preMessageCont .preMessage:hover .sendPreIcon {
  display: block;
}

.preMessageCont .preMessage .title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.preMessageCont .preMessage .text {
  color: #acacbe;
}

.list {
  bottom: 108px;
  right: 4px;
  max-height: 300px;
  overflow-y: auto;
  position: absolute;
  z-index: 9;
  margin-bottom: 8px;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 8px;
  /* width: 80%; */
  min-width: 170px;
}

.list h5 {
  color: #03354c !important;
  padding-bottom: 5px;
  border-bottom: 1px solid #062d3f;
  font-size: 1.2rem !important;
}

.list span {
  color: #03354c;
  margin-bottom: .3rem;
  font-weight: 500;
  line-height: 1.2;
  font-size: 0.8rem;

}

.list span:hover {
  cursor: pointer;
  opacity: 0.8;
}

.list .preMessage {
  width: 200px !important;
}

.list::-webkit-scrollbar {
  width: 10px;
}

.list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

.list::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.list .preMessage {
  border: 1.5px solid #717171;
  border-radius: 16px;
  padding: 10px 20px;

}





.bottomCont {
  position: relative;
  border: 1px solid #062d3f;
  border-radius: 16px;
  width: 95%;
  margin: 0 auto;
  padding: 0px 5px;
}



.bottomCont textarea {
  width: 100%;
  resize: vertical;
  height: 58px;
  padding: calc(53px / 2 - 0.5em) 15px;
  font-size: 16px;
  background: transparent;
  resize: none;
  color: black;
  flex: 1;
  border-radius: 20px;
  border: none;
}

textarea:focus {
  outline: none;
}

.bottomCont .btn {
  /* position: absolute; */
  /* top: 11px;
  right: 16px; */
  background-color: #03354c;
  color: white;
}

.bottomCont .btn.active {
  background: #03354c;
  border-color: black;
}

.bottomCont .btn:hover {
  background: white !important;
  border-color: white;
}

.bottomCont .btn:hover i {
  color: black;
}



.bottomText {
  color: #acacbe;
  text-align: center;
  margin-bottom: 0;
  margin-top: 4px;
}


.bottomCont {
  position: relative;
  width: 95%;
  margin: 0 auto;
  padding: 0px 5px;
  box-shadow: 0px 1px 5px #9b9b9c;
  border: none; /* Remove extra border */
}


.attachment-icon {
  cursor: pointer;
  margin-right: 10px;
  font-size: 18px; /* Icon size */
  color: gray;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 16px;
  resize: none; /* Disable manual resizing */
  max-height: 200px; /* Set maximum height */
  overflow-y: auto; /* Add scrolling when content exceeds height */
  color: black;
}

.send-button {
  background-color: #03354c;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50px;
  width: 36px;
}

.send-button i {
  color: white;
}

.send-button:disabled {
  background-color: #b3b3b3;
  cursor: not-allowed;
}

.send-button:disabled i {
  color: #8c8c8c;
}

.send-button.active {
  background-color: #03354c;
}

.send-button:hover {
  background: white;
  border-color: white;
}

.send-button:hover i {
  color: black;
}



/* .card-img,
.card-img-bottom,
.card-img-top {
  width: 100%;
  height: 180px;
} */

.card {
  transition: all 1s;
}

.card .picCont {
  height: 180px;
  overflow: hidden;
}

.card .picCont img {
  height: 100%;
  object-fit: cover;
  transition: all 0.5s linear;
}

.card:hover img {
  transform: rotate(25deg) scale(1.5);
}

.card .card-title {
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}

.card.disabled {
  opacity: 0.6;
  transform: scale(0.9);
}

.card .qunty {
  /* padding: 8px; */
  position: absolute;
  top: 4px;
  right: 4px;
  background: whtie;
  color: black;
  opacity: 1;
  display: flex;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  z-index: 2;
}

.cart {
  position: absolute;
  width: 25px;
  height: 25px;
  top: 20px;
  right: 45px;
  color: black;
  font-size: 30px;
}

.cartQnt {
  position: absolute;
  width: 25px;
  height: 25px;
  top: -12px;
  right: -18px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-weight: bold;
  font-size: 12px !important;
}

.cartTable {
  width: 80%;
  background-color: black;
  color: white;
}

.tableBottom {
  padding: 8px 24px;
}

.tableBottom .shipping {
  display: flex;
  justify-content: space-between;
}

.tableBottom .shipping .address p {
  margin-bottom: 4px;
}

.tableBottom .shipping .address p strong {
  color: #101010;
}

table {
  border-collapse: collapse;
  margin-bottom: 0;
}

table th:first-child {
  width: 40%;
}

th,
td {
  text-align: left;
  padding: 8px;
}

th {
  background-color: #04AA6D;
  color: white;
}

table i {
  font-size: 18px;
  cursor: pointer;
}

table tr.disabled {
  --bs-table-color: #acacbe;
}



/* for
product card */



/* product cards */
#productCardCont {
  display: flex;
  flex-wrap: wrap;
  /* justify-content: space-around; */
}

#productCardCont .productCard {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  padding: 20px;
  width: 30%;
  min-width: 206px;
  margin: 20px 10px;
  border: 1px solid #5c7a8a;
  border-radius: 15px;
}

#productCardCont .productCard img {
  display: block;
  margin: 0 auto;
  width: 175px;
  height: 180px;
  -o-object-fit: contain;
  object-fit: contain;
  -o-object-position: center;
  object-position: center;
}

#productCardCont .productCard .price {
  color: #5c7a8a;
  font-weight: 500;
  margin: 30px 0 10px 0;
}

#productCardCont .productCard .bottomCont {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}

#productCardCont .productCard button {
  padding: 8px 20px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 20px;
  margin: 10px 0px;
}

#productCardCont .productCard button:nth-child(1) {
  background-color: #03354c;
  color: white;
}

#productCardCont .productCard button:nth-child(2) {
  border: 1px solid #03354c;
  background: white;
  color: #03354c;

}

#productCardCont .productCard .stickLeft {
  background-color: #03354c;
  color: white;
  padding: 4px 15px;
  font-size: 11px;
  border-radius: 0 8px 8px 0;
  position: absolute;
  top: 10px;
  left: 0px;
}

#productCardCont .productCard .stickRight {
  position: absolute;
  top: 10px;
  right: 10px;
}

#productCardCont .productCard .stickRight i {
  color: #f5b03d !important;
  display: block;
  font-size: 10px;
  margin: 4px 0;
}


/* chat buttons style */
.chatBtn {
  background-color: #03354c;
  color: white;
}


@media (max-width:992px) {
  .bottomCont .preMessageCont .preMessage {
    width: 40%;
  }
}

@media (max-width:768px) {
  .middleCont {
    height: 60vh;
  }

  .messageBox::-webkit-scrollbar {
    display: none;
  }
}

/*
@media (max-width:800px) {
  .bottomCont {
    margin: 0 20px;
  }
}



@media (max-width:500px) {
  .bottomCont .preMessageCont .preMessage {
    width: 45%;
  }

  .middleCont {
    color: black;
    overflow: hidden;
    height: calc(100vh - 95px);
  }
}

@media (max-width:300px) {
  .bottomCont .preMessageCont .preMessage {
    width: 50%;
  }
} */

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 8px 12px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: #90909090;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.thinking {
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 4px 8px;
  min-width: 60px;
}
