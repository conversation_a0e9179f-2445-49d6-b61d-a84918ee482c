/* You can add global styles to this file, and also import other style files */

@import "primeicons/primeicons.css";

:root {
  --bg-color: #085e81;
  --hover-color: #2c7b99;
  --active-color: #3684ac;
  --focus-color: #2c6474;
}
html,
body {
  height: 100%;
  background-color: rgb(245, 246, 250);
  font-family: Inter, "Open Sans", sans-serif !important;
  overflow-x: hidden;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  color: rgb(19, 21, 35);
}

app-video.ng-star-inserted {
  width: 100% !important;
}

app-project.ng-star-inserted {
  width: 100% !important;
}

app-sync-hub.ng-star-inserted {
  width: 100% !important;
}
app-sync-hub,app-video ,app-project,app-login,app-page-not-found {
  width: 100% !important;

}
.v-cen{
  display: flex;
  align-items: center;
}

.Btn {
  color: white;
  outline: none;
  font-size: 0.875rem;
  /* line-height: 1.4286; */
  font-weight: 400;
  border-radius: 4px;
  background: var(--bg-color);
  padding: 4px 20px;
  border: none;
}

.Btn:hover {
  text-decoration: none;
  background-color: var(--hover-color);
}

.Btn:active {
  border: 1px solid var(--active-color);

  /* Even darker green on click */
}

.add-button:focus {
  outline: none;
  box-shadow: 0 0 5px var(--focus-color);
}

ul{
  margin: 0!important;
  padding-left: 0!important;
}
a {
  text-decoration: none!important;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{
  border-color: #085e81;
  color: #085e81;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link{
  color: #6b7280;
}
.backBtn{
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 10px;
  margin: 10px;
  color: #0d65ab;
  outline: none;
  border: none;
  font-size: 20px;
}

.pi-spin
{
animation: loader 1s  linear infinite ;
}
@keyframes loader {
0%{
  rotate: 0deg;
}
100%{
  rotate: 360deg;
}
}

.compact-table .p-datatable .p-datatable-tbody > tr > td {
  padding: 0rem 1rem;
  font-size: 0.9rem;
}

.p-datatable .p-datatable-tbody > tr > td {
  border-bottom: 1px solid rgb(239, 237, 237);
}
