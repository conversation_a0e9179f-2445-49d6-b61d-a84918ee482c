﻿using AdminPortalBackend.Core.Contracts;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Extensions;
using AdminPortalBackend.Infrastructure.SignalRHub;
using Amazon.Runtime.Internal.Util;
using Dapper;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;
using System.Text.Json;

namespace AdminPortalBackend.Infrastructure.DataStore;

//public class SqlServerDataStore(SyncManager syncManager, IDbConnection _dbConnection, IDbConnectionRepository _dbConnectionRepo) : IDataStore
public class SqlServerDataStore(string connectionString,
                                ILogger<DataStoreFactory> _logger,
                                IHubContext<MessageHub, IMessageClient> messageHub,
                                string deleteStatingAfterLoad,
                                IntegrationInfo integrationInfo) : IDataStore
{

    private Dictionary<string, object> storeConfig = [];

    public async Task<List<Dictionary<string, object>>> RetrieveDataAsync(string tableName)
    {
        int offset = 0;
        var result = new List<Dictionary<string, object>>();
        var batchSize = 20000;

        string nextLink = storeConfig.GetValueOrDefault("NextLink") as string;
        if (!string.IsNullOrEmpty(nextLink))
        {
            offset = int.Parse(nextLink);
        }

        var tableNamePart = string.IsNullOrEmpty(tableName) ? [" "] : tableName.Split('_');
        string tablePart = tableNamePart.Length == 2 ? tableNamePart[1] : tableNamePart[0];

        var message = $"{integrationInfo.IntegrationId}~({tablePart}) Retriving Connection Data From table {tableName}";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                await connection.OpenAsync();

                // Create the query with OFFSET and FETCH for pagination
                string selectQuery = $"SELECT * FROM [{tableName}] ORDER BY (SELECT NULL) OFFSET {offset} ROWS FETCH NEXT {batchSize} ROWS ONLY";

                // Use Dapper to execute the query and retrieve the data as an IEnumerable of dynamic objects
                var rows = await connection.QueryAsync(selectQuery);

                if (rows == null || !rows.Any())
                {
                    message = $"{integrationInfo.IntegrationId}~({tablePart}) Retrieved Total {offset} Data from Table {tableName}";
                    await messageHub.Clients.All.SendMessage(message);
                    _logger.LogInformation(message);
                    this.SetConfig("NextLink", null);
                }
                else
                {
                    // Convert each row to a dictionary (column name -> value)
                    foreach (var row in rows)
                    {
                        var rowDict = new Dictionary<string, object>();
                        foreach (var property in (IDictionary<string, object>)row)
                        {
                            rowDict[property.Key] = property.Value;
                        }
                        result.Add(rowDict);
                    }

                    string totalSize = (offset + rows.Count()).ToString();
                    this.SetConfig("NextLink", totalSize);
                    message = $"{integrationInfo.IntegrationId}~({tablePart}) Retrieved {rows.Count()} data. Total {(offset + rows.Count())}";
                    await messageHub.Clients.All.SendMessage(message);
                    _logger.LogInformation(message);
                }
            }
            catch (Exception ex)
            {
                message = $"{integrationInfo.IntegrationId}~({tablePart}) Error: Retriving Connection Data";
                await messageHub.Clients.All.SendMessage(message);
                _logger.LogError(ex, message);
                throw;
            }
        }

        return result;
    }

    public async Task<List<Dictionary<string, object>>> SaveDataAsync(List<Dictionary<string, object>> data, string destinationTable, string sourceTable, string primaryKeys)
    {
        var tableNamePart = string.IsNullOrEmpty(sourceTable) ? [" "] : sourceTable.Split('_');
        string tablePart = tableNamePart.Length == 2 ? tableNamePart[1] : tableNamePart[0];

        _logger.LogInformation("Saving Data to Sql Server");

        try
        {
            bool batchLoadEnabled = storeConfig.ContainsKey("BatchNumber") && storeConfig.ContainsKey("HasMoreData");

            if (batchLoadEnabled)
            {
                string strBatchNumber = storeConfig.GetValueOrDefault("BatchNumber") as string;
                int.TryParse(strBatchNumber, out int batchNumber);

                if (batchNumber == 1)
                {
                    // Ensure the staging table exists
                    await EnsureStagingTableExists(connectionString, destinationTable, sourceTable, primaryKeys);

                    // Clear staging table before transferring data
                    await ClearStagingTable(connectionString, destinationTable, sourceTable);
                }

                var builder = new SqlConnectionStringBuilder(connectionString);
                var colList = await GetColumns(connectionString, builder.InitialCatalog, $"{destinationTable}_Staging");
                var dataTable = data.ToDataTable(colList);
                await TransferDataToStaging(connectionString, $"{destinationTable}_Staging", sourceTable, dataTable);

                if (storeConfig.TryGetValue("HasMoreData", out var hasMoreData2) && string.Equals(hasMoreData2, "No"))
                {
                    // Merge data from staging to destination table
                    await MergeStagingToDestination(connectionString, destinationTable, sourceTable, primaryKeys);
                }
            }
            else
            {
                // Ensure the staging table exists
                await EnsureStagingTableExists(connectionString, destinationTable, sourceTable, primaryKeys);

                // Clear staging table before transferring data
                await ClearStagingTable(connectionString, destinationTable, sourceTable);

                var builder = new SqlConnectionStringBuilder(connectionString);
                var colList = await GetColumns(connectionString, builder.InitialCatalog, $"{destinationTable}_Staging");
                var dataTable = data.ToDataTable(colList);
                await TransferDataToStaging(connectionString, $"{destinationTable}_Staging", sourceTable, dataTable);

                // Merge data from staging to destination table
                await MergeStagingToDestination(connectionString, destinationTable, sourceTable, primaryKeys);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error saving data to table {destinationTable}: {ex.Message}", ex);
            throw;
        }

        return new List<Dictionary<string, object>>(); //just return empty
    }

    private async Task EnsureStagingTableExists(string destinationConnectionString, string destinationTable, string sourceTable, string destinationPrimaryKey)
    {
        var tableNamePart = string.IsNullOrEmpty(sourceTable) ? [" "] : sourceTable.Split('_');
        string tablePart = tableNamePart.Length == 2 ? tableNamePart[1] : tableNamePart[0];

        var message = $"{integrationInfo.IntegrationId}~({tablePart}) Ensuring Staging table Exists for {destinationTable} if not Creating new";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        string stagingTable = $"{destinationTable}_Staging";
        using (SqlConnection connection = new SqlConnection(destinationConnectionString))
        {
            try
            {
                await connection.OpenAsync(); // Ensure the connection is open


                // Get the data type of the primary key
                //    string columnTypeSql = $@"
                //SELECT DATA_TYPE 
                //FROM INFORMATION_SCHEMA.COLUMNS 
                //WHERE TABLE_NAME = '{destinationTable}' AND COLUMN_NAME = '{destinationPrimaryKey}'";

                //    var columnType = await connection.ExecuteScalarAsync<string>(columnTypeSql);

                try
                {
                    // Check if staging table exists and create if it doesn't
                    string createTableSql = $@"
                        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{stagingTable}')
                        BEGIN
                            SELECT * INTO [{stagingTable}] FROM [{destinationTable}] WHERE 1 = 0;
                        END";

                    await connection.ExecuteAsync(createTableSql);
                }
                catch (Exception ex)
                {
                    await messageHub.Clients.All.SendMessage($"{integrationInfo.IntegrationId}~({tablePart}) Error: Error occurred while creating Staging table " + stagingTable);
                    _logger.LogError(ex, $"Error checking or creating staging table {stagingTable}: {ex.Message}", ex);
                    throw;
                }
            }
            catch (Exception ex)
            {
                await messageHub.Clients.All.SendMessage($"{integrationInfo.IntegrationId}~({tablePart}) Error: Error occurred while creating Staging table " + stagingTable);
                _logger.LogError(ex, $"Error ensuring staging table exists for {destinationTable}: {ex.Message}", ex);
                throw;
            }
            await messageHub.Clients.All.SendMessage($"{integrationInfo.IntegrationId}~({tablePart}) Successfully checked and created if not exist " + stagingTable);
        }
    }

    private async Task ClearStagingTable(string destinationConnectionString, string destinationTable, string sourceTable)
    {
        var tableNamePart = string.IsNullOrEmpty(sourceTable) ? [" "] : sourceTable.Split('_');
        string tablePart = tableNamePart.Length == 2 ? tableNamePart[1] : tableNamePart[0];

        string stagingTable = $"{destinationTable}_Staging";

        var message = $"{integrationInfo.IntegrationId}~({tablePart}) Clearing Staging Table {stagingTable}";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            using (SqlConnection connection = new SqlConnection(destinationConnectionString))
            {
                string truncateQuery = $"TRUNCATE TABLE [{stagingTable}];";
                await connection.ExecuteAsync(truncateQuery);
            }
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Cleared Staging Table {stagingTable}";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogInformation(message);
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Error: Cannot clear staging table {stagingTable}";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
            throw;
        }
    }

    private async Task TransferDataToStaging(string connectionString, string stagingTable, string sourceTable, DataTable data)
    {
        var tableNamePart = string.IsNullOrEmpty(sourceTable) ? [" "] : sourceTable.Split('_');
        string tablePart = tableNamePart.Length == 2 ? tableNamePart[1] : tableNamePart[0];

        var message = $"{integrationInfo.IntegrationId}~({tablePart}) Transferring Data to staging table. {stagingTable}";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            using (SqlConnection destinationConnection = new SqlConnection(connectionString))
            {
                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(destinationConnection))
                {
                    // Set the destination table name
                    bulkCopy.BatchSize = 50000;
                    bulkCopy.DestinationTableName = $"[{stagingTable}]";
                    foreach (DataColumn column in data.Columns)
                    {
                        bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
                    }

                    // Open the connection
                    destinationConnection.Open();

                    // Transfer the data from the DataTable to the staging table
                    bulkCopy.WriteToServer(data); // Transfer batch data to staging table
                }
            }
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Data transferred to staging table. {stagingTable}";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogInformation(message);
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Error: Cannot transferred data to staging table. {stagingTable}";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
            throw;
        }
    }

    private async Task MergeStagingToDestination(string destinationConnectionString, string destinationTable, string sourceTable, string destinationPrimaryKey)
    {
        var tableNamePart = string.IsNullOrEmpty(sourceTable) ? [" "] : sourceTable.Split('_');
        string tablePart = tableNamePart.Length == 2 ? tableNamePart[1] : tableNamePart[0];

        string stagingTable = $"{destinationTable}_Staging";
        var message = $"{integrationInfo.IntegrationId}~({tablePart}) Merging staging table {stagingTable} to destination table {destinationTable}";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            using (SqlConnection connection = new SqlConnection(destinationConnectionString))
            {
                await connection.OpenAsync(); // Open the connection


                // Retrieve column names from the destination and staging tables
                var destinationColumns = await GetColumnNamesAsync(destinationConnectionString, destinationTable);
                var stagingColumns = await GetColumnNamesAsync(destinationConnectionString, stagingTable);

                // Ensure both tables have the same columns
                var commonColumns = destinationColumns.Intersect(stagingColumns).ToList();

                // Split the destination primary key into multiple keys
                var primaryKeys = destinationPrimaryKey.Split("@#");

                // Build the UPDATE SET clause dynamically, excluding primary keys
                var updateSet = string.Join(", ", commonColumns.Where(col => !primaryKeys.Contains(col))
                    .Select(col => $"Target.[{col}] = Source.[{col}]"));

                // Build the INSERT clause
                var insertColumns = string.Join(", ", commonColumns.Select(col => $"[{col}]"));
                var insertValues = string.Join(", ", commonColumns.Select(col => $"Source.[{col}]"));

                // Check if the destination table has an identity column
                bool hasIdentity = await HasIdentityColumnAsync(connection, destinationTable);

                // Construct the ON clause with all primary keys
                var onClause = string.Join(" AND ", primaryKeys.Select(pk => $"Target.[{pk}] = Source.[{pk}]"));

                var updateClause = !String.IsNullOrWhiteSpace(updateSet) ? @$"WHEN MATCHED THEN
                        UPDATE SET {updateSet}" : String.Empty;

                if (storeConfig.TryGetValue("DataLoadType", out var bcToSqlType) && string.Equals(bcToSqlType, "Clean Load"))
                {
                    //get staging table count
                    var stagingCountSql = $"SELECT COUNT(*) FROM [{stagingTable}]";
                    var stagingCount = connection.ExecuteScalar<long>(stagingCountSql);
                    if (stagingCount > 500000)
                    {
                        string truncateSql = $"TRUNCATE TABLE [{destinationTable}]";
                        await connection.ExecuteAsync(truncateSql);
                    }
                    else
                    {
                        // Construct the WHERE clause for the DELETE statement
                        var whereClause = string.Join(" AND ", primaryKeys.Select(pk => $"dst.[{pk}] IS NULL"));

                        //Construct the DELETE query
                        string deleteQuery = $@"
                        DELETE dst
                        FROM [{destinationTable}] AS dst
                        LEFT JOIN [{stagingTable}] AS src
                        ON {string.Join(" AND ", primaryKeys.Select(pk => $"dst.[{pk}] = src.[{pk}]"))}
                        WHERE src.[{primaryKeys[0]}] IS NULL;";  // Using the first primary key to check for NULLs

                        await connection.ExecuteAsync(deleteQuery, commandTimeout: 300);
                    }
                }

                // Construct the merge query
                string mergeQuery = $@"
                    -- Set SkipChangeTracking to 1 before executing the merge
                    EXEC sp_set_session_context @key = N'SkipChangeTracking', @value = N'1';

                    {(hasIdentity ? "SET IDENTITY_INSERT [" + destinationTable + "] ON;" : "")}
                    MERGE INTO [{destinationTable}] AS Target
                    USING (
                        SELECT * FROM [{stagingTable}]
                    ) AS Source
                    ON {onClause}
                    {updateClause}
                    WHEN NOT MATCHED THEN
                        INSERT ({insertColumns}) VALUES ({insertValues});
                    {(hasIdentity ? "SET IDENTITY_INSERT [" + destinationTable + "] OFF;" : "")}

                    -- Reset SkipChangeTracking to 0 after executing the merge
                    EXEC sp_set_session_context @key = N'SkipChangeTracking', @value = N'0';
                ";

                // Execute the merge query
                await connection.ExecuteAsync(mergeQuery, commandTimeout: 300);

                //drop staging table
                if (deleteStatingAfterLoad == "Yes")
                {
                    string dropStagingTableSql = $@"DROP TABLE IF EXISTS [{stagingTable}];";
                    await connection.ExecuteAsync(dropStagingTableSql);
                }
            }
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Success: Merging Completed Successfully.";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogInformation(message);
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Error: Merging failed";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
            throw;
        }
    }

    private async Task<bool> HasIdentityColumnAsync(SqlConnection connection, string tableName)
    {
        var message = $"{integrationInfo.IntegrationId}~({tableName}) Checking for Identity Column";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            var query = @"
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @TableName AND COLUMNPROPERTY(OBJECT_ID(TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1;";

            using (var command = new SqlCommand(query, connection))
            {
                command.Parameters.AddWithValue("@TableName", tableName);
                var count = (int)await command.ExecuteScalarAsync();
                return count > 0;
            }
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({tableName}) Error checking identity column";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);
            throw;
        }
    }

    private async Task<IEnumerable<string>> GetColumnNamesAsync(string connectionString, string tableName)
    {
        using (var connection = new SqlConnection(connectionString))
        {
            try
            {
                var sql = @"
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = @TableName";

                return await connection.QueryAsync<string>(sql, new { TableName = tableName });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving column names for table {tableName}: {ex.Message}", ex);
                throw;
            }
        }
    }

    public Task<Dictionary<string, object>> UpdateDataAsync(Dictionary<string, object> data, string tableName, Dictionary<string, object> primaryKeyValues)
    {
        throw new NotImplementedException();
    }

    private async Task<List<ColumnInfo>> GetColumns(string connectionString, string databaseName, string tableName)
    {
        var message = $"{integrationInfo.IntegrationId}~({tableName}) Fetching SQL Columns";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);

        try
        {
            var columns = new List<ColumnInfo>();

            using (var sqlConnection = new SqlConnection(connectionString))
            {
                await sqlConnection.OpenAsync();

                // Define the query to fetch both column name and data type
                var query = @"
                SELECT COLUMN_NAME AS ColumnName, DATA_TYPE as DataType
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = @TableName";

                // Use Dapper to execute the query and map the result to ColumnInfo
                columns = (await sqlConnection.QueryAsync<ColumnInfo>(query, new { TableName = tableName })).ToList();
            }

            message = $"{integrationInfo.IntegrationId}~({tableName}) Successfully fetched SQL Columns";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogInformation(message);

            return columns;
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({tableName}) Error: Failed to fetch SQL Columns";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message);

            return new List<ColumnInfo>();
        }
    }


    public void SetConfig(string key, object value)
    {
        storeConfig[key] = value;
    }

    public object GetConfig(string key)
    {
        return storeConfig.GetValueOrDefault(key);
    }




    //public async Task SaveData(Guid integrationGuid)
    //{
    //    var sql = "SELECT * FROM ConnectionIntegrations WHERE Guid = @Guid;";
    //    var integration = await _dbConnection.QuerySingleOrDefaultAsync<ConnectionIntegration>(sql, new { Guid = integrationGuid });
    //    if (integration == null)
    //    {
    //        return;
    //    }

    //    var sourceConnectionString = await GetConnectionString(integration.SourceConnectionGuid, integration.SourceDatabase);
    //    var destinationConnectionString = await GetConnectionString(integration.DestinationConnectionGuid, integration.DestinationDatabase);

    //    if (sourceConnectionString == null || destinationConnectionString == null)
    //    {
    //        return;
    //    }

    //    var mappedColumns = JsonSerializer.Deserialize<Dictionary<string, string>>(integration.MappedColumns);

    //    await syncManager.TransferSqlSql("sql-sql", sourceConnectionString, destinationConnectionString, integration.DestinationTable, integration.SourceTable, integration.SourcePrimaryKey, integration.DestinationPrimaryKey, mappedColumns);
    //}

    //public async Task<string> GetConnectionString(Guid guid, string databaseName)
    //{
    //    var dbConnection = await _dbConnectionRepo.GetConnection(guid);
    //    if (dbConnection != null)
    //    {
    //        var connectionCreds = JsonSerializer.Deserialize<MssqlServerCredDto>(dbConnection.ConnectionCredJson);
    //        if (connectionCreds != null)
    //        {
    //            return $"Server={connectionCreds.ServerName};" +
    //                   (databaseName != null ? $"Database={databaseName};" : "") +
    //                   $"User Id={connectionCreds.UserId};" +
    //                   $"Password={connectionCreds.Password};" +
    //                   $"TrustServerCertificate={(connectionCreds.TrustedCertificate ? "yes" : "no")};";
    //        }
    //    }
    //    return null;
    //}
}


