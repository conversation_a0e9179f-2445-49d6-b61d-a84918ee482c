<form (ngSubmit)="saveMappings()" #mappingForm="ngForm" class="integration-form">
  <div class="d-flex justify-content-end">
    <i (click)="onClose()" class="fa fa-xmark text-danger mb-3" style="font-size: 32px; cursor: pointer;"></i>
  </div>
  <div class="d-flex align-items-center justify-content-between mb-3">
    <h2 class="m-0">Map Columns</h2>

    <button (click)="mapWithAi($event)" class="btn btn-primary" style="
        background-color: var(--bg-color);
        display: flex;
        gap: 5px;
        align-items: center;
        transform: unset;
      ">
      <span *ngIf="!isMappingWithAi"> Map With AI </span>
      <div *ngIf="isMappingWithAi" style="display: flex; gap: 5px; align-items: center">
        <span>Mapping</span>
        <span class="d-flex"><i class="pi pi-spin pi-spinner" style="font-size: 1.2rem"></i></span>
      </div>
    </button>
  </div>
  <div class="d-flex" style="    justify-content: space-between;
    gap: 10px;     width: 96%;">

    <div class="mapping-section flex-column w-100">
      <label class="mb-1 w-100" style=" font-weight: 600; font-size: 16px;">Select Source Primary Key</label>
      <div class="mapping-cell same-width">
        <!-- PrimeNG MultiSelect -->
        <p-multiSelect [options]="sourceColumns" [(ngModel)]="sourcePrimaryKey" placeholder="Select Source Tables"
          name="sourcePrimaryKey" class="w-100" (onChange)="addSourcePrimaryKey($event.value)">
        </p-multiSelect>
        <div *ngIf="isSourceKeyEmpty" class="text-danger px-1" style="font-size: 16px">
          Source Primary Key is required.
        </div>
        <!-- PrimeNG Chips to display selected tables -->
        <p-chips [(ngModel)]="selectedSourcePrimaryKey" [removable]="true" (onRemove)="removeSourcePrimaryKey($event)"
          [style]="{ 'margin-top': '10px' }" name="selectedSourcePrimaryKey">
        </p-chips>
      </div>
    </div>
    <div class="mapping-section flex-column w-100">
      <label class="mb-1 w-100" style="  font-weight: 600; font-size: 16px;">Select Destination Primary Key</label>
      <div class="mapping-cell same-width">
        <!-- PrimeNG MultiSelect -->
        <p-multiSelect [options]="destinationColumns" [(ngModel)]="selectedDestinationPrimaryKey"
          placeholder="Select Source Tables" class="w-100" (onChange)="addDestinationPrimaryKey($event.value)"
          name="destinationPrimaryKey">
        </p-multiSelect>
        <div *ngIf="isDestinationKeyEmpty" class="text-danger px-1" style="font-size: 16px">
          Destination Primary Key is required.
        </div>
        <!-- PrimeNG Chips to display selected tables -->
        <p-chips [(ngModel)]="selectedDestinationPrimaryKey" [removable]="true" name="selectedDestinationPrimaryKey"
          (onRemove)="removeDestinationPrimaryKey($event)" [style]="{ 'margin-top': '10px', background: 'red' }">
        </p-chips>
      </div>
    </div>

  </div>

  <div class="d-flex align-items-center" style="gap: 14.5rem">
    <h3 style="font-weight: 500; margin: 0">Source Column</h3>
    <h3 style="font-weight: 500; margin: 0">Destination Column</h3>
  </div>
  <div *ngFor="let mapping of columnMappings; let i = index" class="mapping-section">
    <div class="mapping-cell same-width">
      <!-- <p-dropdown [options]="sourceColumns" [(ngModel)]="mapping.source" [placeholder]="'Select Source Column'"
        [showClear]="true" name="source{{ i }}" required>
      </p-dropdown> -->
      <p-dropdown [options]="filteredSourceColumns(i)" [(ngModel)]="mapping.source"
        (onFocus)="columnFocus(mapping.source)" (ngModelChange)="onSourceChange($event, i)"
        [placeholder]="'Select Source Column'"name="source{{ i }}" required>
      </p-dropdown>
    </div>

    <div class="mapping-cell same-width">
      <p-dropdown [options]="filteredDestinationColumns(i)" [(ngModel)]="mapping.destination"
        (onFocus)="columnFocus(mapping.destination)" (ngModelChange)="onDestinationChange($event, i)"
        [placeholder]="'Select Destination Column'" name="destination{{ i }}" required>
      </p-dropdown>
    </div>
    <i (click)="removetable(i)" style="color: red; margin-top: 8px; font-size: 1rem;"
      class="fa-solid fa-delete-left"></i>
  </div>
  <h3 (click)="addMapping()">
    Add More
    <i class="fa fa-add"></i>
  </h3>
  <div class="d-flex justify-content-center">
    <button type="submit" class="btn save-btn">Save Mappings</button>
  </div>
</form>
