.integration-form {
  max-width: 650px;
  margin: 20px auto;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.p-stepper {
  flex-basis: 40rem;
}


.mapping-section {
  display: flex;
  gap: 1px;
  align-items: center;
  border-radius: 8px;
  padding: 8px;
}

.table {
  width: 100%;
}
::ng-deep mat-form-field {
  width: 100%;
  margin-bottom: 15px;
}

.btn.action-btn {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
  font-size: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  width: 35px;
}

.map-btn {
  color: var(--bg-color);
  background-color: rgba(40, 167, 69, 0.1);
}

.map-btn:hover {
  background-color: var(--hover-color);
  color: white;
  box-shadow: 0 4px 8px rgba(0, 167, 69, 0.2);
}

.delete-btn {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.delete-btn:hover {
  background-color: #dc3545;
  color: white;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

.btn i {
  transition: transform 0.2s ease;
}

.btn:hover i {
  transform: scale(1.1);
}


.form-section {
  margin-bottom: 30px;
}

.custom-dropdown-container {
  position: relative;
  display: inline-block;
}

.custom-dropdown-toggle {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.custom-dropdown-menu {   position: absolute;
  background-color: transparent;
  margin-top: .25rem !important;
  list-style: none;
  padding: 0;
  z-index: 10;
  width: 160px;
}

.custom-dropdown-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #212529;
}

.custom-dropdown-item:hover {
  background-color: #e9ecef;
}

.delete-button {
  color: #f44336;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

mat-stepper {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px;
}

mat-step {
  margin-bottom: 20px;
}

/* Updated Styles for mat-option */
::ng-deep mat-option {
  padding: 12px; /* Increase padding for better touch targets */
  border-radius: 4px; /* Rounded corners for options */
}

::ng-deep mat-option:hover {
  background-color: rgba(0, 123, 255, 0.1); /* Hover effect */
  color: #007bff; /* Text color on hover */
}

::ng-deep mat-select::after {
  color: #007bff; /* Arrow color */
}

::ng-deep .mat-select {
  background-color: #ffffff; /* Background color for the select box */
  border: 1px solid #ced4da; /* Border color */
  border-radius: 4px; /* Rounded corners for the select box */
  padding: 8px 12px; /* Inner padding */
  transition: border-color 0.3s; /* Smooth transition for border */
}

::ng-deep .mat-select:focus {
  border-color: #007bff; /* Border color on focus */
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* Shadow effect on focus */
}
::ng-deep .p-stepper  {
  flex-basis: 20rem !important;
}

::ng-deep .form-section .p-dropdown,input {
  width: 85% !important;
}
::ng-deep #mapping .form-section .p-dropdown,input {
  width: 100% !important;
}
::ng-deep #mapping .p-inputtext{
  width: 100% !important;
  padding: 0.25rem 0.25rem;
  font-size: 0.9rem;
}
::ng-deep #mapping .p-multiselect .p-multiselect-label {
  padding: 0;
}
::ng-deep #mapping .p-multiselect {
  width: 100%;
  padding: 0.25rem 0.25rem;
  font-size: 0.9rem;
}

.mapping-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
}

.mapping-table th,
.mapping-table td {
  padding: 5px;
  border: 1px solid #e0e0e0;
  text-align: left;
}

.mapping-table th {
  background-color: #f7f7f7;
  font-weight: bold;
}

.mapping-table td {
  background-color: #ffffff;
}

.full-width {
  width: 100%;
}
::ng-deep .mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) {
  background-color: none !important;
}


::ng-deep .p-multiselect {
  width: 100% !important;
}

::ng-deep .p-dropdown {
  width: 100% !important;
}
::ng-deep .p-dropdown-items-wrapper {
  min-width: 100%;
}
