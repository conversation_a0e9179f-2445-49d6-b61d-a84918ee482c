<div class="app-container">
  <!-- Hamburger icon to toggle the sidebar -->
  <div class="hamburger" (click)="toggleSidebar()">
    <i class="fas fa-bars"></i>
  </div>

  <!-- Sidebar, using ngClass to apply 'open' or 'closed' class based on isSidebarOpen -->
  <div class="sidebar" [ngClass]="{'open': isSidebarOpen, 'closed': !isSidebarOpen}" *ngIf="isSidebarOpen">
    <nav>
      <ul>
        <li class="nav-item" routerLink="./video-gallery" routerLinkActive="active">
          <i class="fa-solid fa-photo-film"></i>
          <span>Video Gallery</span>
        </li>
        <li class="nav-item" routerLink="./video-list" routerLinkActive="active">
          <i class="fa-solid fa-film"></i>
          <span>Video List</span>
        </li>
      </ul>
    </nav>
  </div>

  <!-- Main content section -->
  <div class="main-content" [class.sidebar-open]="isSidebarOpen">
    <router-outlet></router-outlet>
  </div>
</div>
