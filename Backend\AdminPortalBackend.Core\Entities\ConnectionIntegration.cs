﻿using System;

namespace AdminPortalBackend.Core.Entities
{
    public class ConnectionIntegration
    {
        public Guid Guid { get; set; }
        public string IntegrationName { get; set; }
        public Guid SourceConnectionGuid { get; set; }
        public string SourceConnectionName { get; set; }
        public string SourceDatabase { get; set; }
        public string SourceTable { get; set; }
        public Guid DestinationConnectionGuid { get; set; }
        public string DestinationConnectionName { get; set; }
        public string DestinationDatabase { get; set; }
        public string DestinationTable { get; set; }
        public string SourcePrimaryKey { get; set; }
        public string DestinationPrimaryKey { get; set; }
        public string? JobFrequency { get; set; }
        public bool IsActive { get; set; }
        public string MappedColumns { get; set; } // JSON string
        public string? Settings { get; set; } // Json string
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public string LastExecutionDate { get; set; }
    }

    public class IntegrationInfo
    {
        public Guid IntegrationId { get; set; }
        public Guid ConnectionGuid { get; set; }
        public string ConnectionName { get; set; }
        public string Database { get; set; }
        public string Table { get; set; }
        public string PrimaryKey { get; set; }
        public string? Settings { get; set; } // Json string
    }

}
