.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.page-heading {
  font-size: 25px;
  font-weight: 500;
  color: #333;
}

.add-button {
  color: rgb(30, 94, 255);
  outline: none;
  font-size: 0.875rem;
  line-height: 1.4286;
  font-weight: 400;
  font-family: Inter, "Open Sans", sans-serif;
  margin: 12px 0px 0px;
  transition: box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1), border-color 250ms cubic-bezier(0.4, 0, 0.2, 1), color 250ms cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 4px;
  border: 1px solid rgb(215, 219, 236);
  background: white;
  padding: 4px 20px;
}

.add-button:hover {
  text-decoration: none;
  background-color: rgba(30, 94, 255, 0.04);
  border: 1px solid rgb(30, 94, 255);
}

.add-button:active {
  border: 1px solid rgb(18, 85, 253);

  /* Even darker green on click */
}

.add-button:focus {
  outline: none;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
  /* Blue focus outline */
}

.card {
  overflow-x: auto;
  background: #ffffff !important;
}
