﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Repositiories;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AppSettingController(IAppSettingRepository _settingRepository) : ControllerBase
    {
        [HttpPost("AddOrEditWebHookNotification")]
        public async Task<ActionResult<ResponseMessage>> AddOrEditWebHookNotification(string notificationUrl)
        {
            var res = await _settingRepository.CreateOrUpdateWebhookNotificationUrl(notificationUrl);
            return Ok(res);
        } 
        
        [HttpPost("GetWebhookNotificationUrl")]
        public async Task<ActionResult<ResponseMessage>> GetWebhookNotificationUrl()
        {
            var res = await _settingRepository.GetWebhookNotificationUrl();
            return Ok(res);
        } 
    }
}
