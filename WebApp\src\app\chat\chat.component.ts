import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GenAIServiceProxy } from '../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../shared/service-proxies/service-proxy.module';
import { MarkdownModule } from 'ngx-markdown';
import { SignalRService } from '../signalr.service';
import { MessageType } from '../message-types.enum';

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule, MarkdownModule],
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.css'] // Note: use styleUrls, not styleUrl
})
export class ChatComponent implements OnInit {
  @Input() initialMessage: string = ''; // Message sent from AppComponent
  newMessage: string = ''; // Chat input box message
  @ViewChild('middle') middleElement: ElementRef;
  @ViewChild('messageInputs') messageInputRef: ElementRef; // Reference to the textarea

  panelVisible: boolean = true;
  isMobileView: boolean = false;

  chats: { question: string, answer: { text: string, buttons: string[] } }[] = [];
  preButtons = ["Clear Cart", "View Cart", "About", "Services", "FaQs"];
  moreButtons = ["Check Out", "Get all Orders"];
  messageInput: string = "";
  isAIThinking: boolean = false;

  constructor(
    private genAiService: GenAIServiceProxy,
    private signalRService: SignalRService
  ) {
    // Subscribe to SignalR messages
    this.signalRService.message$.subscribe(message => {
      if (message.type === MessageType.ReceiveAIChunk) {
        this.isAIThinking = false;
        const lastChat = this.chats[this.chats.length - 1];
        lastChat.answer.text += message.message; // Append the new chunk
        this.scrollToBottom();
      }
    });
  }

  ngOnInit(): void {
    this.chats.push({
      question: "What services do you offer?",
      answer: {
        text: "We offer a range of services including AI Messaging, Smart Widget, Product Recommendations, AI Assistant, Smart Search etc.",
        buttons: ["Clear Cart", "View Cart", "About"]
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['initialMessage'] && changes['initialMessage'].currentValue) {
      this.messageInput = changes['initialMessage'].currentValue;
      this.sendChat();
    }
  }

  resetHeight(): void {
    const textarea = this.messageInputRef.nativeElement; // Reference to textarea
    textarea.style.height = 'auto'; // Reset the height
  }

  handleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendChat();
    }
  }

  adjustHeight(): void {
    const textarea = this.messageInputRef.nativeElement; // Reference to textarea
    textarea.style.height = 'auto'; // Reset height to allow shrinking
    textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px'; // Set to scrollHeight, max 200px
  }

  sendChat(event: any = null) {
    this.showList = false;

    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.messageInput.trim() === '') {
      return;
    }

    // Adding the user's message to chats
    this.chats.push({
      question: this.messageInput,
      answer: {
        text: "", // Start with empty text
        buttons: []
      }
    });

    this.isAIThinking = true; // Set thinking state to true
    this.signalRService.startAIConversation(this.messageInput);

    this.messageInput = "";
    this.resetHeight();
    this.scrollToBottom();
  }

  scrollToBottom() {
    setTimeout(() => {
      const element = this.middleElement?.nativeElement;
      if (element) {
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  sendPreChat(title: string) {
    this.messageInput = title;
    this.sendChat();
  }

  showList: boolean = false;

  toggleList(): void {
    this.showList = !this.showList;
  }

  togglePanel() {
    this.panelVisible = !this.panelVisible;
  }
}
