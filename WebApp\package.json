{"name": "admin-hub", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/cdk": "^17.3.10", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@microsoft/signalr": "^8.0.7", "bootstrap": "^5.3.3", "luxon": "^3.5.0", "ngx-markdown": "^17.0.0", "ngx-toastr": "^19.0.0", "nswag": "^14.1.0", "primeicons": "^7.0.0", "primeng": "^17.18.10", "rxjs": "~7.8.0", "subsink": "^1.0.2", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.10", "@angular/cli": "^17.0.10", "@angular/compiler-cli": "^17.0.0", "@types/jasmine": "~5.1.0", "@types/luxon": "^3.4.2", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.2"}}