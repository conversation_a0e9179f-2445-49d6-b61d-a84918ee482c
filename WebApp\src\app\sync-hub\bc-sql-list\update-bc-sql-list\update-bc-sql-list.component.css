.p-stepper {
  flex-basis: 50rem;
}

.p-dropdown {
  width: 100% !important;
}

.dropdown>div {
  width: 100%;

}

h2 {
  margin: 0;
  font-size: 16px;
}

.active {
  background: #0d6efd;
}
::ng-deep.p-stepper .p-stepper-header.p-highlight .p-stepper-number {
  color: white;
  background-color: var(--active-color);
}
::ng-deep.p-stepper .p-stepper-header:has(~ .p-highlight) .p-stepper-separator {
  background-color: var(--bg-color);
}
::ng-deep.p-inputswitch .p-inputswitch-slider:before {
  width: 1rem;
  height: 1rem;
  left: 0.25rem;
  margin-top: -8px;
  border-radius: 50%;
  transition-duration: 0.2s;
}
::ng-deep.p-inputswitch .p-inputswitch-slider {
  height: 20px;
}
::ng-deep .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
  background: var(--bg-color);
  height: 20px;
}
.p-radiobutton .p-radiobutton-box.p-highlight {
  border-color: var(--bg-color);
  background: var(--bg-color);
}
::ng-deep.p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
  border-color: var( --hover-color);
  background: var( --hover-color);
  color: #ffffff;
}

::ng-deep.p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled) {
  border-color: var( --hover-color);
  background: var( --hover-color);
  color: #ffffff;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

::ng-deep .p-stepper .p-stepper-header {
  padding: 0.5rem 0;
}

.custom-dropdown-container {
  position: relative;
  display: inline-block;
}

.custom-dropdown-toggle {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.custom-dropdown-menu {    position: absolute;
  background-color: transparent;
  margin-top: .25rem !important;
  list-style: none;
  padding: 0;
  z-index: 10;
  width: 160px;
}

.custom-dropdown-item {
  padding: 5px ;
  cursor: pointer;
  font-size: 14px;
  color: #212529;
  border-radius: 5px;
}

.custom-dropdown-item:hover {
  background-color: #e9ecef;
}

.delete-button {
  color: #f44336;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

::ng-deep .form-section .p-dropdown, input  {
  width: 85% !important;
}
::ng-deep .form-section .p-multiselect {
  width: 85% !important;
}

.map-col ::ng-deep .p-inputtext{
  width: 100%;
  padding: 0.25rem 0.25rem;
  font-size: 0.9rem;
}
