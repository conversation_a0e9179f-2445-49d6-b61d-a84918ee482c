import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { HttpClientModule } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { ConnectionTypeDialogComponent } from '../dialog/connection-type-dialog/connection-type-dialog.component';
import { DbConnectionDto, DbConnectionServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { CustomConfirmDialogComponent } from '../../dialog/custom-confirm-dialog/custom-confirm-dialog.component';
import { SqlConnectionDialogComponent } from '../dialog/sql-connection-dialog/sql-connection-dialog.component';
import { ODataConnectionDialogComponent } from '../dialog/o-data-connection-dialog/o-data-connection-dialog.component';
import { PanelModule } from 'primeng/panel';
import { FieldsetModule } from 'primeng/fieldset';
import { TooltipModule } from 'primeng/tooltip';

@Component({
  selector: 'app-connections',
  standalone: true,
  imports: [TableModule, CommonModule, ButtonModule, HttpClientModule, PanelModule, FieldsetModule, TooltipModule],

  templateUrl: './connections.component.html',
  styleUrl: './connections.component.css'
})
export class ConnectionsComponent {
  tableData: DbConnectionDto[] = [];
  first: number = 0;
  isDataLoaded = false
  constructor(private dialog: MatDialog, private _dbConnectionServices: DbConnectionServiceProxy) { }

  ngOnInit(): void {
    this.LoadAllData()
  }

  LoadAllData() {
    this._dbConnectionServices.getAll().subscribe((res) => {
      //console.log(res)
      this.tableData = res
      if (res) {
        this.isDataLoaded = true
      }
    })
  }

  AddDialog(videoData?: any): void {
    const dialogRef = this.dialog.open(ConnectionTypeDialogComponent, {
      width: '400px',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.LoadAllData()
      }
    });
  }

  // Edit video method
  editItem(item: any): void {
    if (item.type == 'Ms SQL Server') {
      //console.log(item.guid)
      const dialogRef = this.dialog.open(SqlConnectionDialogComponent, {
        width: '500px',
        height: 'auto',
        data: item.guid,
        disableClose: true
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.LoadAllData()
        }
      });

    } else {
      const dialogRef = this.dialog.open(ODataConnectionDialogComponent, {
        width: '900px',
        height: 'auto',
        data: item,
        disableClose: true,
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.LoadAllData()
        }
      });

    }

  }




  // delete methid
  deleteClicked(guid: string) {

    const dialogRef = this.dialog.open(CustomConfirmDialogComponent, {
      width: '300px',
      data: {
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this item?'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this._dbConnectionServices.delete(guid).subscribe((res) => {
          if (res) {
            this.LoadAllData();
          }
        })
      }
    });
  }


  chooseDataBase(type: string) {
    //console.log(type)
    if (type == 'sql') {
      this.SqlConnection()
    } else {
      this.odataConnection(type)
    }
  }


  onClose(): void {
    //this.dialogRef.close();
  }
  // create the conenction to the sql
  SqlConnection(videoData?: any): void {
    const dialogRef = this.dialog.open(SqlConnectionDialogComponent, {
      width: '500px',
      height: 'auto',
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        //console.log(result);
        //this.dialogRef.close(result);
        this.LoadAllData()
      }
    });
  }

  // create the conenction to the oData
  odataConnection(type: any): void {
    const dialogRef = this.dialog.open(ODataConnectionDialogComponent, {
      width: '800px',
      height: 'auto',
      disableClose: true,
      data: { type: type },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        //console.log(result);
        //this.dialogRef.close(result);
        this.LoadAllData()
      }
    });
  }

  isNav(connectionJson: any): boolean {
    var connection = JSON.parse(connectionJson)
    if ('scope' in connection) {
      return connection['scope'] == 'CustomBasicAuth';
    }
    return false;
  }


  // Pagination methods
  next() {
    this.first += 5;
  }

  prev() {
    this.first -= 5;
  }

  reset() {
    this.first = 0;
  }

  isFirstPage(): boolean {
    return this.first === 0;
  }

  isLastPage(): boolean {
    return this.first === (this.tableData.length - 5);
  }

  pageChange(event: any) {
    this.first = event.first;
  }

  fetchType(type: string) {
    if (type == 'BCODataWebService') {
      return 'Web Service'
    } else if (type == 'Ms SQL Server') {
      return 'MS SQL Server'
    } else if (type == 'BCODataRestApiService') {
      return 'Rest API'
    }
    return type
  }
}
