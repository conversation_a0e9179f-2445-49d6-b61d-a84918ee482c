{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-fieldset.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * Fieldset is a grouping component with the optional content toggle feature.\n * @group Components\n */\nconst _c0 = [\"*\", [[\"p-header\"]]];\nconst _c1 = [\"*\", \"p-header\"];\nconst _c2 = (a0, a1) => ({\n  \"p-fieldset p-component\": true,\n  \"p-fieldset-toggleable\": a0,\n  \"p-fieldset-expanded\": a1\n});\nconst _c3 = a0 => ({\n  transitionParams: a0,\n  height: \"0\"\n});\nconst _c4 = a0 => ({\n  value: \"hidden\",\n  params: a0\n});\nconst _c5 = a0 => ({\n  transitionParams: a0,\n  height: \"*\"\n});\nconst _c6 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction Fieldset_ng_container_2_ng_container_2_PlusIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"PlusIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-fieldset-toggler\");\n    i0.ɵɵattribute(\"data-pc-section\", \"togglericon\");\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_2_span_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, Fieldset_ng_container_2_ng_container_2_span_2_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"togglericon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.expandIconTemplate);\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Fieldset_ng_container_2_ng_container_2_PlusIcon_1_Template, 1, 2, \"PlusIcon\", 9)(2, Fieldset_ng_container_2_ng_container_2_span_2_Template, 2, 2, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.expandIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.expandIconTemplate);\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_3_MinusIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"MinusIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-fieldset-toggler\");\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"togglericon\");\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_3_span_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, Fieldset_ng_container_2_ng_container_3_span_2_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"togglericon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.collapseIconTemplate);\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Fieldset_ng_container_2_ng_container_3_MinusIcon_1_Template, 1, 3, \"MinusIcon\", 9)(2, Fieldset_ng_container_2_ng_container_3_span_2_Template, 2, 2, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.collapseIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.collapseIconTemplate);\n  }\n}\nfunction Fieldset_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Fieldset_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function Fieldset_ng_container_2_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggle($event));\n    })(\"keydown\", function Fieldset_ng_container_2_Template_a_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    });\n    i0.ɵɵtemplate(2, Fieldset_ng_container_2_ng_container_2_Template, 3, 2, \"ng-container\", 8)(3, Fieldset_ng_container_2_ng_container_3_Template, 3, 2, \"ng-container\", 8)(4, Fieldset_ng_container_2_ng_container_4_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const legendContent_r4 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r2.id + \"_header\")(\"aria-controls\", ctx_r2.id + \"_content\")(\"aria-expanded\", !ctx_r2.collapsed)(\"aria-label\", ctx_r2.buttonAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.collapsed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.collapsed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", legendContent_r4);\n  }\n}\nfunction Fieldset_ng_template_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Fieldset_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵtemplate(3, Fieldset_ng_template_3_ng_container_3_Template, 1, 0, \"ng-container\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"legendtitle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.legend);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.headerTemplate);\n  }\n}\nfunction Fieldset_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nclass Fieldset {\n  el;\n  /**\n   * Header text of the fieldset.\n   * @group Props\n   */\n  legend;\n  /**\n   * When specified, content can toggled by clicking the legend.\n   * @group Props\n   * @defaultValue false\n   */\n  toggleable;\n  /**\n   * Defines the default visibility state of the content.\n   * * @group Props\n   */\n  collapsed = false;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Transition options of the panel animation.\n   * @group Props\n   */\n  transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n  /**\n   * Emits when the collapsed state changes.\n   * @param {boolean} value - New value.\n   * @group Emits\n   */\n  collapsedChange = new EventEmitter();\n  /**\n   * Callback to invoke before panel toggle.\n   * @param {PanelBeforeToggleEvent} event - Custom toggle event\n   * @group Emits\n   */\n  onBeforeToggle = new EventEmitter();\n  /**\n   * Callback to invoke after panel toggle.\n   * @param {PanelAfterToggleEvent} event - Custom toggle event\n   * @group Emits\n   */\n  onAfterToggle = new EventEmitter();\n  templates;\n  get id() {\n    return UniqueComponentId();\n  }\n  get buttonAriaLabel() {\n    return this.legend;\n  }\n  animating;\n  headerTemplate;\n  contentTemplate;\n  collapseIconTemplate;\n  expandIconTemplate;\n  constructor(el) {\n    this.el = el;\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'expandicon':\n          this.expandIconTemplate = item.template;\n          break;\n        case 'collapseicon':\n          this.collapseIconTemplate = item.template;\n          break;\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  toggle(event) {\n    if (this.animating) {\n      return false;\n    }\n    this.animating = true;\n    this.onBeforeToggle.emit({\n      originalEvent: event,\n      collapsed: this.collapsed\n    });\n    if (this.collapsed) this.expand();else this.collapse();\n    this.onAfterToggle.emit({\n      originalEvent: event,\n      collapsed: this.collapsed\n    });\n    event.preventDefault();\n  }\n  onKeyDown(event) {\n    if (event.code === 'Enter' || event.code === 'Space') {\n      this.toggle(event);\n      event.preventDefault();\n    }\n  }\n  expand() {\n    this.collapsed = false;\n    this.collapsedChange.emit(this.collapsed);\n  }\n  collapse() {\n    this.collapsed = true;\n    this.collapsedChange.emit(this.collapsed);\n  }\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n  onToggleDone() {\n    this.animating = false;\n  }\n  static ɵfac = function Fieldset_Factory(t) {\n    return new (t || Fieldset)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Fieldset,\n    selectors: [[\"p-fieldset\"]],\n    contentQueries: function Fieldset_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      legend: \"legend\",\n      toggleable: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"toggleable\", \"toggleable\", booleanAttribute],\n      collapsed: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"collapsed\", \"collapsed\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      transitionOptions: \"transitionOptions\"\n    },\n    outputs: {\n      collapsedChange: \"collapsedChange\",\n      onBeforeToggle: \"onBeforeToggle\",\n      onAfterToggle: \"onAfterToggle\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c1,\n    decls: 9,\n    vars: 28,\n    consts: [[\"legendContent\", \"\"], [3, \"ngClass\", \"ngStyle\"], [1, \"p-fieldset-legend\"], [4, \"ngIf\", \"ngIfElse\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-fieldset-content\"], [4, \"ngTemplateOutlet\"], [\"pRipple\", \"\", \"tabindex\", \"0\", \"role\", \"button\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-fieldset-toggler\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-fieldset-toggler\"], [1, \"p-fieldset-legend-text\"]],\n    template: function Fieldset_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"fieldset\", 1)(1, \"legend\", 2);\n        i0.ɵɵtemplate(2, Fieldset_ng_container_2_Template, 5, 7, \"ng-container\", 3)(3, Fieldset_ng_template_3_Template, 4, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4);\n        i0.ɵɵlistener(\"@fieldsetContent.done\", function Fieldset_Template_div_animation_fieldsetContent_done_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onToggleDone());\n        });\n        i0.ɵɵelementStart(6, \"div\", 5);\n        i0.ɵɵprojection(7);\n        i0.ɵɵtemplate(8, Fieldset_ng_container_8_Template, 1, 0, \"ng-container\", 6);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const legendContent_r4 = i0.ɵɵreference(4);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(17, _c2, ctx.toggleable, !ctx.collapsed && ctx.toggleable))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"id\", ctx.id)(\"data-pc-name\", \"fieldset\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"data-pc-section\", \"legend\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.toggleable)(\"ngIfElse\", legendContent_r4);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"@fieldsetContent\", ctx.collapsed ? i0.ɵɵpureFunction1(22, _c4, i0.ɵɵpureFunction1(20, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(26, _c6, i0.ɵɵpureFunction1(24, _c5, ctx.animating ? ctx.transitionOptions : \"0ms\")));\n        i0.ɵɵattribute(\"id\", ctx.id + \"_content\")(\"aria-labelledby\", ctx.id + \"_header\")(\"aria-hidden\", ctx.collapsed)(\"data-pc-section\", \"toggleablecontent\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"data-pc-section\", \"content\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple, MinusIcon, PlusIcon],\n    styles: [\"@layer primeng{.p-fieldset{min-width:initial}.p-fieldset-legend>a,.p-fieldset-legend>span{display:flex;align-items:center;justify-content:center}.p-fieldset-toggleable .p-fieldset-legend a{cursor:pointer;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-fieldset-legend-text{line-height:1}.p-fieldset-toggleable.p-fieldset-expanded>.p-toggleable-content:not(.ng-animating){overflow:visible}.p-fieldset-toggleable .p-toggleable-content{overflow:hidden}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('fieldsetContent', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Fieldset, [{\n    type: Component,\n    args: [{\n      selector: 'p-fieldset',\n      template: `\n        <fieldset\n            [attr.id]=\"id\"\n            [ngClass]=\"{ 'p-fieldset p-component': true, 'p-fieldset-toggleable': toggleable, 'p-fieldset-expanded': !collapsed && toggleable }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'fieldset'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <legend class=\"p-fieldset-legend\" [attr.data-pc-section]=\"'legend'\">\n                <ng-container *ngIf=\"toggleable; else legendContent\">\n                    <a [attr.id]=\"id + '_header'\" pRipple tabindex=\"0\" role=\"button\" [attr.aria-controls]=\"id + '_content'\" [attr.aria-expanded]=\"!collapsed\" [attr.aria-label]=\"buttonAriaLabel\" (click)=\"toggle($event)\" (keydown)=\"onKeyDown($event)\">\n                        <ng-container *ngIf=\"collapsed\">\n                            <PlusIcon *ngIf=\"!expandIconTemplate\" [styleClass]=\"'p-fieldset-toggler'\" [attr.data-pc-section]=\"'togglericon'\" />\n                            <span *ngIf=\"expandIconTemplate\" class=\"p-fieldset-toggler\" [attr.data-pc-section]=\"'togglericon'\">\n                                <ng-container *ngTemplateOutlet=\"expandIconTemplate\"></ng-container>\n                            </span>\n                        </ng-container>\n                        <ng-container *ngIf=\"!collapsed\">\n                            <MinusIcon *ngIf=\"!collapseIconTemplate\" [styleClass]=\"'p-fieldset-toggler'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'togglericon'\" />\n                            <span *ngIf=\"collapseIconTemplate\" class=\"p-fieldset-toggler\" [attr.data-pc-section]=\"'togglericon'\">\n                                <ng-container *ngTemplateOutlet=\"collapseIconTemplate\"></ng-container>\n                            </span>\n                        </ng-container>\n                        <ng-container *ngTemplateOutlet=\"legendContent\"></ng-container>\n                    </a>\n                </ng-container>\n                <ng-template #legendContent>\n                    <span class=\"p-fieldset-legend-text\" [attr.data-pc-section]=\"'legendtitle'\">{{ legend }}</span>\n                    <ng-content select=\"p-header\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                </ng-template>\n            </legend>\n            <div\n                [attr.id]=\"id + '_content'\"\n                role=\"region\"\n                class=\"p-toggleable-content\"\n                [@fieldsetContent]=\"collapsed ? { value: 'hidden', params: { transitionParams: transitionOptions, height: '0' } } : { value: 'visible', params: { transitionParams: animating ? transitionOptions : '0ms', height: '*' } }\"\n                [attr.aria-labelledby]=\"id + '_header'\"\n                [attr.aria-hidden]=\"collapsed\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n                (@fieldsetContent.done)=\"onToggleDone()\"\n            >\n                <div class=\"p-fieldset-content\" [attr.data-pc-section]=\"'content'\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n        </fieldset>\n    `,\n      animations: [trigger('fieldsetContent', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-fieldset{min-width:initial}.p-fieldset-legend>a,.p-fieldset-legend>span{display:flex;align-items:center;justify-content:center}.p-fieldset-toggleable .p-fieldset-legend a{cursor:pointer;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-fieldset-legend-text{line-height:1}.p-fieldset-toggleable.p-fieldset-expanded>.p-toggleable-content:not(.ng-animating){overflow:visible}.p-fieldset-toggleable .p-toggleable-content{overflow:hidden}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    legend: [{\n      type: Input\n    }],\n    toggleable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    collapsed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    collapsedChange: [{\n      type: Output\n    }],\n    onBeforeToggle: [{\n      type: Output\n    }],\n    onAfterToggle: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass FieldsetModule {\n  static ɵfac = function FieldsetModule_Factory(t) {\n    return new (t || FieldsetModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FieldsetModule,\n    declarations: [Fieldset],\n    imports: [CommonModule, RippleModule, MinusIcon, PlusIcon],\n    exports: [Fieldset, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RippleModule, MinusIcon, PlusIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldsetModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RippleModule, MinusIcon, PlusIcon],\n      exports: [Fieldset, SharedModule],\n      declarations: [Fieldset]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Fieldset, FieldsetModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;AAChC,IAAM,MAAM,CAAC,KAAK,UAAU;AAC5B,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,kBAAkB;AAAA,EAClB,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,kBAAkB;AAAA,EAClB,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,oBAAoB;AAChD,IAAG,YAAY,mBAAmB,aAAa;AAAA,EACjD;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,gBAAgB,CAAC;AAC/G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,aAAa;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,kBAAkB;AAAA,EAC7D;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,wDAAwD,GAAG,GAAG,QAAQ,EAAE;AAC7K,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,kBAAkB;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,kBAAkB;AAAA,EACjD;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,oBAAoB;AAChD,IAAG,YAAY,eAAe,IAAI,EAAE,mBAAmB,aAAa;AAAA,EACtE;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,gBAAgB,CAAC;AAC/G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,mBAAmB,aAAa;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,oBAAoB;AAAA,EAC/D;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,wDAAwD,GAAG,GAAG,QAAQ,EAAE;AAC/K,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,oBAAoB;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,oBAAoB;AAAA,EACnD;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,SAAS,SAAS,oDAAoD,QAAQ;AAC1F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,MAAM,CAAC;AAAA,IAC7C,CAAC,EAAE,WAAW,SAAS,sDAAsD,QAAQ;AACnF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AACnP,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,UAAU;AACb,IAAG,YAAY,MAAM,OAAO,KAAK,SAAS,EAAE,iBAAiB,OAAO,KAAK,UAAU,EAAE,iBAAiB,CAAC,OAAO,SAAS,EAAE,cAAc,OAAO,eAAe;AAC7J,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC1F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,mBAAmB,aAAa;AAC/C,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAClC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,cAAc;AAAA,EACzD;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,kBAAkB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,gBAAgB,IAAI,aAAa;AAAA,EACjC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,kBAAkB;AAAA,EAC3B;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,qBAAqB,KAAK;AAC/B;AAAA,QACF,KAAK;AACH,eAAK,uBAAuB,KAAK;AACjC;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,SAAK,YAAY;AACjB,SAAK,eAAe,KAAK;AAAA,MACvB,eAAe;AAAA,MACf,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,QAAI,KAAK;AAAW,WAAK,OAAO;AAAA;AAAO,WAAK,SAAS;AACrD,SAAK,cAAc,KAAK;AAAA,MACtB,eAAe;AAAA,MACf,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,UAAU,OAAO;AACf,QAAI,MAAM,SAAS,WAAW,MAAM,SAAS,SAAS;AACpD,WAAK,OAAO,KAAK;AACjB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,WAAW;AACT,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,GAAG,cAAc,SAAS,CAAC;AAAA,EACzC;AAAA,EACA,eAAe;AACb,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,GAAG;AACzC,WAAO,KAAK,KAAK,WAAa,kBAAqB,UAAU,CAAC;AAAA,EAChE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,gBAAgB;AAAA,MACrG,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,gBAAgB;AAAA,MAClG,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,mBAAmB;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,QAAQ,UAAU,GAAG,sBAAsB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,WAAW,IAAI,YAAY,KAAK,QAAQ,UAAU,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,IAC9b,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC;AAClD,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,iCAAiC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACvK,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,yBAAyB,SAAS,kEAAkE;AAChH,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,CAAC;AAAA,QAC1C,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC;AAC1E,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,mBAAsB,YAAY,CAAC;AACzC,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,IAAI,YAAY,CAAC,IAAI,aAAa,IAAI,UAAU,CAAC,EAAE,WAAW,IAAI,KAAK;AAC5H,QAAG,YAAY,MAAM,IAAI,EAAE,EAAE,gBAAgB,UAAU,EAAE,mBAAmB,MAAM;AAClF,QAAG,UAAU;AACb,QAAG,YAAY,mBAAmB,QAAQ;AAC1C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,UAAU,EAAE,YAAY,gBAAgB;AAClE,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,IAAI,YAAe,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,IAAI,iBAAiB,CAAC,IAAO,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,IAAI,YAAY,IAAI,oBAAoB,KAAK,CAAC,CAAC;AAC3O,QAAG,YAAY,MAAM,IAAI,KAAK,UAAU,EAAE,mBAAmB,IAAI,KAAK,SAAS,EAAE,eAAe,IAAI,SAAS,EAAE,mBAAmB,mBAAmB;AACrJ,QAAG,UAAU;AACb,QAAG,YAAY,mBAAmB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,IAAI,eAAe;AAAA,MACvD;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,MAAS,kBAAqB,SAAY,QAAQ,WAAW,QAAQ;AAAA,IACzG,QAAQ,CAAC,qeAAqe;AAAA,IAC9e,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,mBAAmB,CAAC,MAAM,UAAU,MAAM;AAAA,QAC5D,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACjH;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkDV,YAAY,CAAC,QAAQ,mBAAmB,CAAC,MAAM,UAAU,MAAM;AAAA,QAC7D,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MAC/G,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,qeAAqe;AAAA,IAChf,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,GAAG;AAC/C,WAAO,KAAK,KAAK,iBAAgB;AAAA,EACnC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,QAAQ;AAAA,IACvB,SAAS,CAAC,cAAc,cAAc,WAAW,QAAQ;AAAA,IACzD,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,WAAW,UAAU,YAAY;AAAA,EACzE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,WAAW,QAAQ;AAAA,MACzD,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,cAAc,CAAC,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}