﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Entities
{
    public class DbConnection
    {
        public DbConnection()
        {
            Guid = Guid.NewGuid(); // Automatically assign a new GUID
        }

        public Guid Guid { get; set; }
        public string ConnectionName { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string ConnectionCredJson { get; set; }
        public string AccessToken { get; set; } 
        public DateTimeOffset? ExpiresOn { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
    }
}
