import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { HttpClientModule } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { ConnectionIntegration, ConnectionIntegrationServiceProxy, JobRequest } from '../../../shared/service-proxies/service-proxies';
import { CustomConfirmDialogComponent } from '../../dialog/custom-confirm-dialog/custom-confirm-dialog.component';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { JoblogsViewDialogComponent } from '../dialog/joblogs-view-dialog/joblogs-view-dialog.component';
import { SignalRService } from '../../signalr.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-connection-integration',
  standalone: true,
  imports: [TableModule, CommonModule, ButtonModule, HttpClientModule, FormsModule],
  templateUrl: './connection-integration.component.html',
  styleUrls: ['./connection-integration.component.css']
})
export class ConnectionIntegrationComponent {
  guid: string = '00000000000000';
  tableData: ConnectionIntegration[] = [];
  groupedTableData: any[] = [];
  filteredTableData: ConnectionIntegration[] = []; // Holds filtered data
  first: number = 0;
  searchBySourceTable: string = ''; // Property to hold the search term for sourceTable
  isDataLoaded = false
  isFilterAdded = false
  executingJobs: Set<string> = new Set();

  constructor(
    private dialog: MatDialog,
    private _connectionIntegrationService: ConnectionIntegrationServiceProxy,
    private router: Router,
    private signalRService: SignalRService,
    private toastr: ToastrService
  ) {
    this.signalRService.message$.subscribe(message => {
      if (message.message) {
        const [integrationId] = message.message.split('~');
        if (message.message.includes('Merging Completed Successfully')) {
          this.executingJobs.delete(integrationId);
          this.toastr.success('Job Completed Successfully');
        }
        else if (message.message.includes('Failed')) {
          this.executingJobs.delete(integrationId);
          this.toastr.error('Job Failed');
        }
        else if (message.message.includes('Cancelled')) {
          this.executingJobs.delete(integrationId);
          this.toastr.success('Job Cancelled Successfully');
        }
        else {
          this.executingJobs.add(integrationId);
        }
      }
    });
  }

  ngOnInit(): void {
    // Load the saved search term from localStorage (if it exists)
    const savedSearchTerm = localStorage.getItem('searchBySourceTable');
    if (savedSearchTerm) {
      this.searchBySourceTable = savedSearchTerm;
    }

    // Load all data and apply the filter
    this.LoadAllData();
    this.restoreExpandedState();
  }

  // Load data from the API and apply filtering
  LoadAllData() {
    this._connectionIntegrationService.getSqlSqlAll().subscribe((res) => {
      this.tableData = res;

      // Apply the filter to the data based on the search term
      this.filteredTableData = this.applySourceTableFilter(res);

      // Group filtered data by destinationDatabase
      this.groupedTableData = this.groupByDestinationDatabase(this.filteredTableData);

      // Restore the expanded state of the groups
      this.restoreExpandedState();
      if (res) {
        this.isDataLoaded = true
      }
    });
  }

  // Apply the source table filter
  applySourceTableFilter(data: ConnectionIntegration[]): ConnectionIntegration[] {
    const searchValue = this.searchBySourceTable.toLowerCase();
    if (searchValue) {

      this.isFilterAdded = true
    }
    return data.filter((item) => item.sourceTable.toLowerCase().includes(searchValue));
  }

  // Group data by destination database
  groupByDestinationDatabase(data: any[]): any[] {
    const groupedData = [];
    const groups = data.reduce((acc, item) => {
      const group = item.destinationDatabase || 'Unknown'; // Handle undefined databases
      if (!acc[group]) {
        acc[group] = { id: group, destinationDatabase: group, isExpanded: false, items: [] }; // Add 'id' and 'isExpanded'
        groupedData.push(acc[group]);
      }
      acc[group].items.push(item);
      return acc;
    }, {});
    return groupedData;
  }

  // Toggle group expansion
  toggleGroup(group: any) {
    group.isExpanded = !group.isExpanded;
    this.saveExpandedState(); // Save state after toggling
  }

  // Save the expanded state of groups to localStorage
  saveExpandedState() {
    const expandedGroupIds = this.groupedTableData
      .filter(group => group.isExpanded)
      .map(group => group.id);
    localStorage.setItem('expandedSqlToSqlGroups', JSON.stringify(expandedGroupIds));
  }

  // Restore the expanded state from localStorage
  restoreExpandedState() {
    const storedData = localStorage.getItem('expandedSqlToSqlGroups');
    if (storedData) {
      const expandedGroupIds = JSON.parse(storedData);
      this.groupedTableData.forEach(group => {
        group.isExpanded = expandedGroupIds.includes(group.id);
      });
    }
  }

  // Update search filter and save to localStorage
  updateSearchFilter(event: Event): void {
    this.searchBySourceTable = (event.target as HTMLInputElement).value;

    // Save the search term in localStorage
    localStorage.setItem('searchBySourceTable', this.searchBySourceTable);

    // Reload data with the new filter
    this.LoadAllData();
  }

  // Navigate to the add integration page
  navigateToIntegration(): void {
    this.router.navigate(['/sync-hub/add-connection-integration']);
  }

  // Edit an existing item
  editItem(item: any): void {
    this.router.navigate(['./sync-hub/connection-integration', item]);
  }

  // Activate or deactivate a job
  activeJobOrInactive(item: any) {
    if (item.isActive) {
      this._connectionIntegrationService.disableJob(item.guid).subscribe(() => {
        this.LoadAllData();
      });
    } else {
      const jobData = new JobRequest();
      jobData.frequency = item.jobFrequency;
      jobData.reqeustGuid = item.guid;
      this._connectionIntegrationService.scheduleJob(jobData).subscribe(() => {
        this.LoadAllData();
      });
    }
  }

  // Delete an integration
  deleteClicked(guid: string) {
    const dialogRef = this.dialog.open(CustomConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Deletion',
        message: 'Are you sure you want to delete this item?'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this._connectionIntegrationService.delete(guid).subscribe(() => {
          this.LoadAllData();
        });
      }
    });

  }
  showJobLogsDialog(item: any) {
    this.dialog.open(JoblogsViewDialogComponent, {
      width: '750px',
      height: 'auto',
      minHeight: '350px',
      data: {
        intergrationId: item.guid,
        settings: JSON.parse(item.settings)
      },
    });
  }
  clearAllFilter() {
    this.isFilterAdded = false;
    this.searchBySourceTable = ''
    localStorage.setItem('searchBySourceTable', '');
    this.groupedTableData = this.groupByDestinationDatabase(this.tableData);

    // Restore the expanded state of the groups
    this.restoreExpandedState();
  }

  // Pagination methods
  // next() {
  //   this.first += 5;
  // }

  // prev() {
  //   this.first -= 5;
  // }

  // reset() {
  //   this.first = 0;
  // }

  // isFirstPage(): boolean {
  //   return this.first === 0;
  // }

  // isLastPage(): boolean {
  //   return this.first === (this.tableData.length - 5);
  // }

  // pageChange(event: any) {
  //   this.first = event.first;
  // }

  async executeJob(guid: string) {
    try {
      await this._connectionIntegrationService.executeJob(guid).toPromise();
      this.executingJobs.add(guid);
      this.toastr.info('Job Started Executing');
    } catch (error) {
      console.error('Error executing job:', error);
    }
  }

  async stopJob(guid: string) {
    try {
      this.toastr.info('Cancelling Job');
      await this._connectionIntegrationService.deleteJob(guid).toPromise();
    } catch (error) {
      console.error('Error stopping job:', error);
    }
  }
}
