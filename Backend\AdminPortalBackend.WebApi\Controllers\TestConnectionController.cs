﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.DataStore;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestConnectionController(ITestConnectionRepository _testConnectionRepo, SyncManager syncManager) : ControllerBase
    {
        [HttpPost("CheckSqlConnection")]
        public async Task<ActionResult<ResponseMessage>> CheckSqlConnection(string username, string password, string serverName)
        {
            var res = await _testConnectionRepo.CheckSqlConnection(username, password, serverName);
            return Ok(res);
        }
        
        [HttpPost("CheckBcConnection")]
        public async Task<ActionResult<ResponseMessage>> CheckBcConnection(string clientSecret, string clientId, string tokenEndpoint, string scope)
        {
            var res = await _testConnectionRepo.CheckBcConnection(clientSecret, clientId, tokenEndpoint, scope);
            return Ok(res);
        }

        [HttpPost("realtime")]
        public async Task<ActionResult<ResponseMessage>> TestRealtime()
        {
            await syncManager.ProcessBCToSqlUpdate();
            return Ok();
        }

    }
}
