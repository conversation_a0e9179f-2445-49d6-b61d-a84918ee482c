ul{
  margin: 0!important;
  padding-left: 0!important;
}
.error-message {
  display: flex;
  align-items: center;
  color: #c91023;
  
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 5px;
  margin-bottom: 10px;
  line-height: 1.5;
  width: 100%;
  box-sizing: border-box;
}

.loader {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
  border-top: 4px solid #000000;
  border-right: 4px solid transparent;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
.loader::after {
  content: '';
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border-bottom: 4px solid var(--bg-color);
  border-left: 4px solid transparent;
}
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


/* card spinner  */
.card<PERSON>oader {
  position: relative;
  margin: auto;
  box-sizing: border-box;
  width: 1000px;
  height: 1000px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.1);
  transform-origin: 50% 50%;
  transform: perspective(200px) rotateX(66deg);
  animation: spinner-wiggle 1.2s infinite;
}
.cardLoader:before,
.cardLoader:after {
  content: "";
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  box-sizing: border-box;
  border: 4px solid #0000;
  animation: spinner-spin 1.2s cubic-bezier(0.6, 0.2, 0, 0.8) infinite,
    spinner-fade 1.2s linear infinite;
}
.cardLoader:before {
  border-top-color: black;
}
.cardLoader:after {
  border-top-color: var(--bg-color);
  animation-delay: 0.4s;
}

@keyframes spinner-spin {
  100% { transform: rotate(360deg)}
}
@keyframes spinner-fade {
  25%, 75% { opacity: 0.1}
  50% { opacity: 1 }
}

.card
p{
  display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
}

.custom-dropdown-container {
  position: relative;
  display: inline-block;
}

.custom-dropdown-toggle {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.custom-dropdown-menu {    position: absolute;
  background-color: transparent;
  margin-top: .25rem !important;
  list-style: none;
  padding: 0;
  z-index: 10;
  width: 160px;
}

.custom-dropdown-item {
  padding: 5px ;
  cursor: pointer;
  font-size: 14px;
  color: #212529;
  border-radius: 5px;
}

.custom-dropdown-item:hover {
  background-color: #e9ecef;
}

::ng-deep .form-section .p-dropdown, input  {
  width: 85% !important;
}
::ng-deep .form-section .p-multiselect {
  width: 85% !important;
}
