import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from './header/header.component';
import { ChatComponent } from './chat/chat.component';
import { ChatService } from './chat.service';
import { SplitterModule } from 'primeng/splitter';
import { SignalRService } from './signalr.service';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet,ChatComponent, HeaderComponent, SplitterModule, ToastModule, ButtonModule, RippleModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'AdminHub';
  chatMessages: string[] = [];
  isChatOpen = false;
  messageToChat: string = '';  // Holds the latest message sent

  constructor(private chatService: ChatService, private signalRService: SignalRService, private messageSerive: MessageService) {}

  ngOnInit() {
    // Subscribe to the chat toggle observable
    this.chatService.chatToggle$.subscribe(isOpen => {
      this.isChatOpen = isOpen;
    });
    this.isChatOpen= false

    // Subscribe to the chat message observable
    this.chatService.chatMessage$.subscribe(message => {
      this.messageToChat = message;
    });
  }

  toggleChat() {
    this.isChatOpen = !this.isChatOpen; // Toggle the chat section
  }

  onMessageSent(message: string) {

    this.chatMessages.push(message); // Add the message to the chatMessages array
    console.log('Message received from SearchingComponent:', message);
  }
}
