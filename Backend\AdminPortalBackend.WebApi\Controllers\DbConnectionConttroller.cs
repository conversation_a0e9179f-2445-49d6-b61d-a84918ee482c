﻿using AdminPortalBackend.Core.Contracts.Features;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.OData;
using Microsoft.AspNetCore.Mvc;

namespace AdminPortalBackend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DbConnectionController : ControllerBase
    {
        private readonly IDbConnectionRepository _repository;
        private readonly ODataService oDataService;

        public DbConnectionController(IDbConnectionRepository repository, ODataService oDataService)
        {
            _repository = repository;
            this.oDataService = oDataService;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<List<DbConnectionDto>>> GetAll()
        {
            var connections = await _repository.GetAllConnections();
            return Ok(connections);
        }

        [HttpGet("GetSingle")]
        public async Task<ActionResult<DbConnectionDto>> GetSingle(Guid guid)
        {
            var connection = await _repository.GetConnection(guid);
            return Ok(connection);
        }

        [HttpPost("CreateOrEdit")]
        public async Task<ActionResult<DbConnectionDto>> CreateOrEdit(DbConnectionDto request)
        {
            var connection = await _repository.CreateOrEdit(request);
            if (request.Type == "BCODataWebService" || request.Type == "BCODataRestApiService")
            {
                try
                {
                    await oDataService.TransferCompanyData(connection.Guid);
                    await oDataService.TransferEntities(connection.Guid);
                    await oDataService.RefreshMetadata(connection.Guid);
                }
                catch { }
                
            }
            return Ok(connection);
        }

        [HttpDelete("Delete")]
        public async Task<ActionResult<ResponseMessage>> Delete(Guid guid)
        {
            var connection = await _repository.Delete(guid);
            return Ok(connection);
        }
        
        
        [HttpGet("GetTop10JobLogs")]
        public async Task<ActionResult<List<JobLogEntry>>> GetTop10JobLogs(string integrationId)
        {
            var jobLogEntry = await _repository.GetTop10JobLogs(integrationId);
            return Ok(jobLogEntry);
        }
    }
}
