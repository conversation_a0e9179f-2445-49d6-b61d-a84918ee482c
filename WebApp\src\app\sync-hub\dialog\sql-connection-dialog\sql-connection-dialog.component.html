<h2 mat-dialog-title class="dialog-title">{{data ? 'Update':'Create'}} SQL Server Connection</h2>
<form #sqlServerForm="ngForm" (ngSubmit)="onSubmit(sqlServerForm)" class="sql-server-form">
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Connection Name</mat-label>
    <input matInput placeholder="Enter Server Name" name="connectionName" [(ngModel)]="sqlServer.connectionName"
      required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Server Name</mat-label>
    <input matInput placeholder="Enter Server Name" name="serverName" [(ngModel)]="sqlServer.ServerName" required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width" style="display: none;">
    <mat-label>Type</mat-label>
    <input matInput value="Ms SQL Server" name="type" [(ngModel)]="sqlServer.type" disabled />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Description</mat-label>
    <input matInput placeholder="Enter Description" name="description" [(ngModel)]="sqlServer.description" required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>User ID</mat-label>
    <input matInput placeholder="Enter User ID" name="userId" [(ngModel)]="sqlServer.UserId" required />
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Password</mat-label>
    <input matInput type="password" placeholder="Enter Password" name="password" [(ngModel)]="sqlServer.Password"
      required />
  </mat-form-field>

  <mat-checkbox [(ngModel)]="sqlServer.TrustedCertificate" name="TrustedCertificate">
    Use Trusted Server Certificate
  </mat-checkbox>


  <div class="dialog-actions mt-0 ">
    <div class="d-flex gap-2">
      <button [disabled]="!isError" mat-flat-button style="background-color: var(--bg-color); color: white;"
        [ngClass]="{ 'disabled-button': !isError }" type="submit" [disabled]="!sqlServerForm.valid"
        [disabled]="isSaving">
        {{data
        ?'Save':'Save'}} <span *ngIf="isSaving" class="loader"><i class="pi pi-spin pi-spinner"
            style="font-size: 1.2rem"></i></span> </button>
      <button mat-button type="button" style="background-color: var(--bg-color);color: white;" [disabled]="isError"
        (click)="testConnection()">
        <span *ngIf="!isLoading">Test</span>
        <span *ngIf="isLoading" class="loader"><i class="pi pi-spin pi-spinner" style="font-size: 1.2rem"></i></span>
      </button>
    </div>
    <button mat-button type="button" (click)="onClose()">Cancel</button>
  </div> 
  <!-- <p-button
        (onClick)="showToast1()"
        label="Show Success" /> -->
</form>
