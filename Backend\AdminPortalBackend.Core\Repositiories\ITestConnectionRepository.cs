﻿using AdminPortalBackend.Core.Contracts.Features;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Repositiories
{
    public interface ITestConnectionRepository
    {
        Task<ResponseMessage> CheckSqlConnection(string username, string password, string serverName);
        Task<ResponseMessage> CheckBcConnection(string clientSecret, string clientId, string tokenEndpoint, string scope);
    }
}
