import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UserNotificationDialogComponent } from './user-notification-dialog.component';

describe('UserNotificationDialogComponent', () => {
  let component: UserNotificationDialogComponent;
  let fixture: ComponentFixture<UserNotificationDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UserNotificationDialogComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(UserNotificationDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
