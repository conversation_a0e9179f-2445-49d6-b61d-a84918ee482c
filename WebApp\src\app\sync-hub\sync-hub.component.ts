import { Component } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ChatComponent } from '../chat/chat.component';
import { filter } from 'rxjs';
@Component({
  selector: 'app-sync-hub',
  standalone: true,
  imports: [RouterOutlet, CommonModule, RouterLink, RouterLinkActive, ChatComponent
  ],
  templateUrl: './sync-hub.component.html',
  styleUrl: './sync-hub.component.css'
})
export class SyncHubComponent {
  constructor(private router: Router, private activatedRoute: ActivatedRoute) {

  }

  isSidebarOpen = true;

  isChatOpen: boolean = false;
  url:string = "";
  ngOnInit() {
    // Subscribe to router events to detect route changes
    this.url = this.router.url;

    this.router.events.pipe(
      filter(event => event instanceof NavigationStart || event instanceof NavigationEnd)
    ).subscribe(event => {

      if (event instanceof NavigationStart) {
        this.url = event.url
      }
      if (event instanceof NavigationEnd) {
        this.url = event.url
      }
      console.log(this.url);

    });
    console.log(this.url);
  }

  toggleChat() {
    this.isChatOpen = !this.isChatOpen;
  }
  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }
  isActive(type): boolean {
    if (type == 'connection') {
      return this.url == '/sync-hub/connection';
    }else if (type == 'sql-sql'){
      return this.url.includes('connection-integration') || this.url.includes('add-connection-integration') || this.url.includes('sync-hub/connection-integration');
    }else if (type == 'bc-sql'){
      return this.url.includes('bc-sql-list') || this.url.includes('bc-sql');
    }
    return false;
  }
}
