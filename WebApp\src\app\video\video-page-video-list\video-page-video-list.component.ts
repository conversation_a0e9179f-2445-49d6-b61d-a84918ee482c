import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { HttpClientModule } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { VideoFormListDialogComponent } from '../dialog/video-form-list-dialog/video-form-list-dialog.component';

export class VideoPageVideoListVm {
  videoLink: string;
  description: string;
  category: string;
  subCategory: string;
  thumbImageLink?: string; // Optional thumbnail image link

  constructor(data: Partial<VideoPageVideoListVm>) {
    this.videoLink = data.videoLink || '';
    this.description = data.description || '';
    this.category = data.category || '';
    this.subCategory = data.subCategory || '';
    this.thumbImageLink = this.generateThumbnailLink(this.videoLink); // Generate thumbnail link
  }

  // Method to generate YouTube thumbnail link
  private generateThumbnailLink(videoLink: string): string | undefined {
    if (!videoLink) return ''; // Return an empty string if no video link is provided
    let videoId: string | undefined;

    // Check for standard YouTube link
    const standardMatch = videoLink.match(/[?&]v=([^&#]+)/);
    if (standardMatch) {
      videoId = standardMatch[1];
    } else {
      // Check for shortened YouTube link
      const shortMatch = videoLink.match(/youtu\.be\/([^?]+)/);
      if (shortMatch) {
        videoId = shortMatch[1];
      }
    }

    return videoId ? `https://img.youtube.com/vi/${videoId}/0.jpg` : undefined;
  }
}


@Component({
  selector: 'app-video-page-video-list',
  standalone: true,
  imports: [TableModule, CommonModule, ButtonModule, HttpClientModule],

  templateUrl: './video-page-video-list.component.html',
  styleUrl: './video-page-video-list.component.css'
})
export class VideoPageVideoListComponent {

  videos: VideoPageVideoListVm[] = [];
  first: number = 0;

  constructor(private dialog: MatDialog) {
    this.loadDemoVideos();
  }

  AddVideoDialog() {
    const dialogRef = this.dialog.open(VideoFormListDialogComponent, {
      width: '600px', // Set the desired width here
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Video Data:', result);
      }
    });
  }


  loadDemoVideos(): void {
    this.videos = [
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'An interesting video about something amazing.',
        category: 'Education',
        subCategory: 'Technology',
        thumbImageLink: 'https://via.placeholder.com/100'  // Example thumbnail
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      new VideoPageVideoListVm({
        videoLink: 'https://www.youtube.com/watch?v=xyfROYN9MUQ',
        description: 'A tutorial on how to master Angular.',
        category: 'Education',
        subCategory: 'Programming',
        thumbImageLink: 'https://via.placeholder.com/100'
      }),
      // Add more videos as needed...
    ];
  }

  // Pagination methods
  next() {
    this.first += 5;
  }

  prev() {
    this.first -= 5;
  }

  reset() {
    this.first = 0;
  }

  isFirstPage(): boolean {
    return this.first === 0;
  }

  isLastPage(): boolean {
    return this.first === (this.videos.length - 5);
  }

  pageChange(event: any) {
    this.first = event.first;
  }

}
