import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';


@Injectable({
  providedIn: 'root'
})
export class ToasterService {

  constructor(private messageService: MessageService) { }

  showToaster(type: string, message: string) {
    let msg = type == 'error' ? `Error: ${message}` : message
    this.messageService.add({ severity: type, detail: msg, key: 'br', life: 3000 });
  }
}
