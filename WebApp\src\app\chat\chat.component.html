<div class="middleCont ">

  <div #middle class="messageBox ps-2">
    <p *ngIf="!chats.length" class="position-absolute defaultSms" style="top: 45%">Welcome to the chat! How can I assist
      you today?</p>
    <ng-container *ngFor="let item of chats; index as index">
      <div class="outgoingMessage messageCont">
        <div class="message">{{item.question}}</div>
      </div>
      <div class="incomingMessage messageCont">
        <div class="top">
          <div class="logo">
            <!-- <img src="../../assets/img/logo.png"> -->
            <h5>AI</h5>
          </div>
          <div class="message" [class.thinking]="isAIThinking && index === chats.length - 1">
            <ng-container *ngIf="!isAIThinking || index !== chats.length - 1">
              <div [innerHtml]="(item.answer.text | markdown) | async"></div>
            </ng-container>
            <ng-container *ngIf="isAIThinking && index === chats.length - 1">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- Predefined Messages -->
    <div class="preMessageCont">
      <div class="d-flex justify-content-end w-100 flex-wrap">
        <!-- <div class="preMessage" (click)="sendPreChat(preMessage)"
          *ngFor="let preMessage of chats[chats.length - 1]?.answer.buttons">
          {{ preMessage }}
        </div> -->
        <!-- <div class="preMessage" (click)="sendPreChat(preMessage)" *ngFor="let preMessage of moreButtons">
          {{ preMessage }}
        </div> -->
        <!-- <div class="preMessage" (click)="toggleList()">MORE ...</div> -->
      </div>
      <div *ngIf="showList" class="list">
        <h5>Suggestions</h5>
        <div (click)="sendPreChat(preMessage)" *ngFor="let preMessage of preButtons">
          <i class="fa-solid fa-circle-check text-primary mx-1"></i>
          <span> {{ preMessage }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Input Area -->
  <div class="bottomCont  mb-2">
    <div class="d-flex  align-items-center gap-2">
      <textarea #messageInputs name="messageInput" id="messageInput" placeholder="Message AI"
        [(ngModel)]="messageInput" (keydown.enter)="sendChat($event)" (input)="adjustHeight()"
        class="message-input"></textarea>

      <button (click)="sendChat($event)" type="button" id="sendMessage" class="send-button"
        [ngClass]="messageInput == '' ? '' : 'active'" [disabled]="messageInput == ''">
        <i class="fa-solid fa-arrow-up"></i>
      </button>
    </div>
  </div>


</div>
