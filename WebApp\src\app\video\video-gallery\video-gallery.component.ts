import { Component } from '@angular/core';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { HttpClientModule } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { VideoFormGalleryDialogComponent } from '../dialog/video-form-gallery-dialog/video-form-gallery-dialog.component';

interface ProductPageVideoListVm {
  itemNo: string;
  videoLink: string;
  title: string;
  isThumbnail: boolean;
  isOnVideoTab: boolean;
}

@Component({
  selector: 'app-video-gallery',
  standalone: true,
  imports: [TableModule, CommonModule, ButtonModule, HttpClientModule],
  templateUrl: './video-gallery.component.html',
  styleUrl: './video-gallery.component.css'
})
export class VideoGalleryComponent {

  videos: ProductPageVideoListVm[] = [];


  ngOnInit(): void {
    // Example data with direct video links
    this.videos = [
      {
        itemNo: '001',
        videoLink: 'https://www.w3schools.com/html/mov_bbb.mp4',
        title: 'Sample Video 1',
        isThumbnail: true,
        isOnVideoTab: true
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
      {
        itemNo: '002',
        videoLink: 'https://www.w3schools.com/html/movie.mp4',
        title: 'Sample Video 2',
        isThumbnail: false,
        isOnVideoTab: false
      },
    ];
  }

  constructor(private dialog: MatDialog) { }

  AddVideoDialog(videoData?: any): void {
    const dialogRef = this.dialog.open(VideoFormGalleryDialogComponent, {
      width: '400px',
      data: videoData || {}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Handle the form result here (e.g., save it)
        console.log(result);
      }
    });
  }

}

