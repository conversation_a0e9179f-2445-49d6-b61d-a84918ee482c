.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: 0 20px 20px 0;
}

.page-header h2 {
  margin: 0;
}

.Btn {
  font-size: 16px;
  font-weight: 400;
  padding: 6px 10px;
}

.ui-tooltip {
  background-color: #e90000;
  /* Change background color */
  color: #fff;
  /* Change text color */
  font-size: 14px;
  /* Customize font size */
  padding: 8px;
  /* Customize padding */
  border-radius: 4px;
  /* Add rounded corners */
}

::deep.custom-tooltip .ui-tooltip-text {
  color: #fff;
  font-size: 14px;
  background-color: #bb0000;
  border-radius: 5px;
}

.tool {
  white-space: nowrap;
  width: 50rem !important;
  padding: 2px;
  font-size: 10px;
}

table tr td h3 {
  border-bottom: 1px solid rgba(163, 163, 163, 0.671);
}

table tr:last-child td h3 {
  border: none;
}

.highlighted-row {
  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
  transition: background-color 0.3s ease;
  animation: highlight-fade 2s ease;
}

.executing-row {
  background-color: #ffffaa !important;
  transition: background-color 0.3s ease;
  animation: highlight-fade 2s ease;
}

@keyframes highlight-fade {
  0% {
    background-color: rgba(var(--bs-primary-rgb), 0.3);
  }

  100% {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
  }
}
