﻿using AdminPortalBackend.Core.Contracts.Features;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FileController : ControllerBase
    {
        private readonly string _uploadFolder = @"C:\AdimPortalAssests\images";
        private readonly string[] _allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".txt", ".pdf" };

        [HttpPost("Upload")]
        public async Task<ActionResult<ResponseMessage>> UploadFiles(List<IFormFile> files)
        {
            var responseMessage = new ResponseMessage();

            // Check if files are provided
            if (files == null || files.Count == 0)
            {
                responseMessage.IsError = true;
                responseMessage.Message = "No files uploaded.";
                return Ok(responseMessage);
            }

            // Validate file extensions
            if (files.Any(file => !_allowedExtensions.Contains(Path.GetExtension(file.FileName).ToLowerInvariant())))
            {
                responseMessage.IsError = true;
                responseMessage.Message = "One or more file types are not allowed. No files were uploaded.";
                return Ok(responseMessage);
            }

            var uploadedFiles = new List<string>();

            // Process each file
            foreach (var file in files)
            {
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var newFileName = $"{Guid.NewGuid()}{extension}";
                var filePath = Path.Combine(_uploadFolder, newFileName);

                try
                {
                    // Ensure the directory exists
                    Directory.CreateDirectory(_uploadFolder);

                    // Save the file to the specified folder
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    uploadedFiles.Add(newFileName); // Add the new file name to the list
                }
                catch (Exception ex)
                {
                    responseMessage.IsError = true;
                    responseMessage.Message = $"Internal server error: {ex.Message}";
                    return StatusCode(500, responseMessage);
                }
            }

            // Success: Set IsError to false and join file names
            responseMessage.IsError = false;
            responseMessage.Message = string.Join(", ", uploadedFiles);

            return Ok(responseMessage);
        }

        [HttpGet("Getfile/{fileName}")]
        public IActionResult GetFile(string fileName)
        {
            var filePath = Path.Combine(_uploadFolder, fileName);

            // Check if file exists
            if (!System.IO.File.Exists(filePath))
                return Ok(new { isError = true, message = "File not found." });

            // Determine the MIME type
            var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
            var mimeType = fileExtension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".txt" => "text/plain",
                ".pdf" => "application/pdf",
                _ => "application/octet-stream"
            };

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            return File(fileBytes, mimeType);
        }

        [HttpDelete("DeleteFile")]
        public ActionResult<ResponseMessage> DeleteFile(List<string> fileNames)
        {
            foreach (var fileName in fileNames)
            {
                string filePath = Path.Combine(_uploadFolder, fileName);

                // Check if the file exists
                if (!System.IO.File.Exists(filePath))
                {
                    return Ok(new { isError = true, message = "File not found." });
                }

                try
                {
                    // Delete the file
                    System.IO.File.Delete(filePath);
                    return Ok(new { isError = false, message = $"File '{fileName}' deleted successfully." });
                }
                catch (Exception ex)
                {
                    // Handle any exceptions that occur during the deletion process
                    return Ok(new { isError = true, message = $"Error deleting file: {ex.Message}" });
                }
            }
            return Ok(new { isError = true, message = "File not found." });
        }

    }
}
