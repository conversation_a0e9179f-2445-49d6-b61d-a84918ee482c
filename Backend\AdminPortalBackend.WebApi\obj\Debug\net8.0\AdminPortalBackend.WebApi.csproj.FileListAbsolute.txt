C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.exe
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.deps.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.runtimeconfig.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.pdb
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\FluentValidation.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\MediatR.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\MediatR.Contracts.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\SendGrid.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.MSSqlServer.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\StarkbankEcdsa.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Stripe.net.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Security.Permissions.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Core.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Infrastructure.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Core.pdb
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Infrastructure.pdb
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.AssemblyInfo.cs
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.AdminPortalBackend.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.build.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\scopedcss\bundle\AdminPortalBackend.WebApi.styles.css
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPor.E4811F96.Up2Date
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\refint\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.pdb
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.genruntimeconfig.cache
C:\Users\<USER>\source\repos\AdminPortal\AdminPortalBackend.WebApi\obj\Debug\net8.0\ref\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.exe
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.deps.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.runtimeconfig.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\FluentValidation.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MediatR.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MediatR.Contracts.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\SendGrid.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.MSSqlServer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\StarkbankEcdsa.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Stripe.net.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Infrastructure.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Core.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.Infrastructure.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.AssemblyInfo.cs
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.AdminPortalBackend.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.build.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.AdminPortalBackend.WebApi.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\scopedcss\bundle\AdminPortalBackend.WebApi.styles.css
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPor.E4811F96.Up2Date
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\refint\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.pdb
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.genruntimeconfig.cache
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\ref\AdminPortalBackend.WebApi.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\AdminPortalBackend.WebApi.sourcelink.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Dapper.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\sni.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\sni.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\sni.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Hangfire.AspNetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Hangfire.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Hangfire.NetCore.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Hangfire.SqlServer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.AI.OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Bcl.HashCode.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Bcl.TimeProvider.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.VectorData.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.OData.Client.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.OData.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.OData.Edm.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SemanticKernel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SemanticKernel.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SemanticKernel.Connectors.OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SemanticKernel.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.SemanticKernel.Plugins.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Spatial.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Numerics.Tensors.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.AI.ContentSafety.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.AI.FormRecognizer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Search.Documents.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Storage.Blobs.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Storage.Common.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Azure.Storage.Queues.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ClosedXML.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ClosedXML.Parser.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\DnsClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\DocumentFormat.OpenXml.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\DocumentFormat.OpenXml.Framework.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Elastic.Clients.Elasticsearch.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Elastic.Transport.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ExcelNumberFormat.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Google.Protobuf.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\HtmlAgilityPack.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\LLamaSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.AI.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Http.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.EventLog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Logging.EventSource.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.All.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.Abstractions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.AI.Anthropic.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.AI.AzureOpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.AI.LlamaSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.AI.Ollama.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.AI.Onnx.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.AI.OpenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.DocumentStorage.AWSS3.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.Postgres.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.MemoryDb.Qdrant.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.MemoryDb.Redis.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.MemoryDb.SQLServer.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.MongoDbAtlas.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.Orchestration.AzureQueues.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.Orchestration.RabbitMQ.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.SemanticKernelPlugin.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.KernelMemory.WebClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.ML.OnnxRuntimeGenAI.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Microsoft.ML.Tokenizers.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MongoDB.Driver.GridFS.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\NetTopologySuite.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Npgsql.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\NRedisStack.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\OllamaSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.DocumentLayoutAnalysis.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.Fonts.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.Package.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.Tokenization.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.Tokens.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\UglyToad.PdfPig.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Pgvector.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Polly.Core.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\RBush.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\SharpCompress.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Snappier.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\StackExchange.Redis.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.IO.Hashing.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.IO.Packaging.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\System.Linq.Async.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\de\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\es\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\fr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\it\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ja\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ko\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\pt-BR\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\ru\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\zh-Hans\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\zh-Hant\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\android\native\onnxruntime.aar
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework.zip
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime-genai.so
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\Hangfire.MaximumConcurrentExecutions.dll
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\bin\Debug\net8.0\AdminPortalBackend.WebApi.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets\msbuild.AdminPortalBackend.WebApi.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\admin-portal\Backend\AdminPortalBackend.WebApi\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
