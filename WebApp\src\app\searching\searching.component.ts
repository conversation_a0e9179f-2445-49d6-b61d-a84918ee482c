import { CommonModule } from '@angular/common';
import { Component, ViewChild, ElementRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ChatService } from '../chat.service';

@Component({
  selector: 'app-searching',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './searching.component.html',
  styleUrls: ['./searching.component.css'],
})

export class SearchingComponent {
  @ViewChild('messageInput', { static: true }) messageInput!: ElementRef<HTMLTextAreaElement>;
  message: string = '';

  constructor(private chatService: ChatService) {}

  adjustHeight(): void {
    const textarea = this.messageInput.nativeElement;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
  }

  sendMessage() {
    if (this.message.trim()) {
      this.chatService.sendMessage(this.message);
      this.chatService.openChat();
      this.message = '';
      this.resetHeight();
    } else {
      console.error('Message cannot be empty');
    }
  }

  resetHeight(): void {
    const textarea = this.messageInput.nativeElement;
    textarea.style.height = 'auto';
  }

  handleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }
}
