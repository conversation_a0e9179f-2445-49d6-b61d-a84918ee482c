﻿using AdminPortalBackend.Core.Contracts.Features;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Repositiories
{
    public interface IAppSettingRepository
    {
        Task<ResponseMessage> CreateOrUpdateWebhookNotificationUrl(string notificationUrl);
        Task<ResponseMessage> GetWebhookNotificationUrl();
    }
}
