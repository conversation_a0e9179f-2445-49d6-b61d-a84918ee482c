<h2 mat-dialog-title class="dialog-title">{{data ? 'Update':'Create'}} Business Central Connection</h2>
<form #odataForm="ngForm" (ngSubmit)="onSubmit(odataForm)" class="odata-form">
  <mat-form-field appearance="outline" class="full-width" *ngIf="!isNav">
    <mat-label>Type</mat-label>
    <mat-select name="type" [(ngModel)]="odata.type" required="" (ngModelChange)="onTypeChange()">
      <mat-option value="BCODataWebService">Web Service</mat-option>
      <mat-option value="BCODataRestApiService">Rest API</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Connection Name</mat-label>
    <input matInput placeholder="Enter Connection Name" name="connectionName" [(ngModel)]="odata.connectionName"
      required />
  </mat-form-field>

  <!-- <mat-form-field appearance="outline" class="full-width">
    <mat-label>Server Name</mat-label>
    <input matInput placeholder="Enter Server Name" name="serverName" [(ngModel)]="odata.serverName" required />
  </mat-form-field> -->

  <!-- <mat-form-field appearance="outline" class="full-width">
    <mat-label>Type</mat-label>
    <input matInput value="OData" name="type" [(ngModel)]="odata.type" />
  </mat-form-field> -->




  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Description</mat-label>
    <input matInput placeholder="Enter Additional Parameter" name="description" [(ngModel)]="odata.description" />
  </mat-form-field>



  <!-- <mat-form-field appearance="outline" class="full-width">
    <mat-label>OAuth 2.0 Token Endpoint (v2)</mat-label>
    <input matInput placeholder="Enter Token Endpoint" name="tokenEndpoint" [(ngModel)]="odata.tokenEndpoint"
      required />
  </mat-form-field> -->
  <div class="d-flex gap-1 justify-content-between">

    <mat-form-field appearance="outline" style="width: 70%;">
      <mat-label>Base Url</mat-label>
      <input matInput placeholder="Enter base url" name="baseUrl" [(ngModel)]="odata.baseUrl" required
        (input)="onApiChange()" />
    </mat-form-field>

    <mat-form-field appearance="outline" style="width: 30%;">
      <mat-label>Service Type</mat-label>
      <input matInput placeholder="Service Type" name="serviceType" [(ngModel)]="serviceType" required
        (input)="onApiChange()" [disabled]="!odata.type || odata.type === 'BCODataWebService'" />
    </mat-form-field>

    </div>


    <mat-form-field appearance="outline" class="full-width">
      <mat-label>urlEndPoint</mat-label>
      <input matInput placeholder="Your urlEndPoint" disabled name="urlEndPoint" [(ngModel)]="urlEndPoint" required />
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Client ID</mat-label>
      <input matInput placeholder="Enter Client ID" name="clientId" [(ngModel)]="odata.clientId" required />
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Client Secret</mat-label>
      <input matInput type="password" placeholder="Enter Client Secret" name="clientSecret"
        [(ngModel)]="odata.clientSecret" required />
    </mat-form-field>

    <!-- <mat-form-field appearance="outline" class="full-width">
    <mat-label>Scope</mat-label>
    <input matInput placeholder="Enter Scope" name="scope" [(ngModel)]="odata.scope" required />
  </mat-form-field> -->


    <div mat-dialog-actions class="dialog-actions" style="justify-content: space-between;">
      <div class="d-flex gap-2">

        <button mat-flat-button style="background-color: var(--bg-color); color: white;"
          [ngClass]="{ 'disabled-button': isError }" type="submit" [disabled]="!odataForm.valid"
          [disabled]="isSaving">{{data ?
          'Save':'Save'}} <span *ngIf="isSaving" class="loader"><i class="pi pi-spin pi-spinner"
              style="font-size: 1.2rem"></i></span></button>
        <button mat-button [disabled]="!isError" (click)="testBcConnection()" type="button"
          style="background-color: var(--bg-color);color: white;">
          <span *ngIf="!isLoading">Test</span> <span *ngIf="isLoading" class="loader"><i class="pi pi-spin pi-spinner"
              style="font-size: 1.2rem"></i></span>
        </button>

      </div>
      <button mat-button type="button" (click)="onClose()">Cancel</button>
    </div>

</form>
