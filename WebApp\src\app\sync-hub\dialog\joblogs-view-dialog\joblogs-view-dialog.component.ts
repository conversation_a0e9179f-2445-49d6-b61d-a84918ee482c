import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ConnectionIntegrationServiceProxy, DbConnectionDto, DbConnectionServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { CommonModule, DatePipe } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { ToasterService } from '../../../toaster.service';
@Component({
  selector: 'app-joblogs-view-dialog',
  standalone: true,
  imports: [ServiceProxyModule, CommonModule, MatTableModule, DatePipe],
  templateUrl: './joblogs-view-dialog.component.html',
  styleUrls: ['./joblogs-view-dialog.component.css']
})
export class JoblogsViewDialogComponent {
  joblogsData = []
  singlarMessage = [];
  isExecutingOnly = false
  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private _dbConnectionService: DbConnectionServiceProxy, private _connectionIntegrationService: ConnectionIntegrationServiceProxy, private toasterService: ToasterService) {

  }
  ngOnInit(): void {

    this._dbConnectionService.getTop10JobLogs(this.data.intergrationId).subscribe((res) => {
      this.joblogsData = res 

    })
  }

  async executeJob(guid: string) {
    this.isExecutingOnly = true;
    try {
      this.singlarMessage = [];
      let res = await this._connectionIntegrationService.executeJob(this.data.intergrationId).toPromise();
      //console.log(res);
      if (!res.isError) {
        this.isExecutingOnly = false;
      }
      return true;
    } catch (error) {
      this.toasterService.showToaster('error', error.message);
      return false;
    } finally {
      this.isExecutingOnly = false;
    }
  }

  timeDifferenceFormatted(time1: string, time2: string): string {
    const date1 = new Date(time1);
    const date2 = new Date(time2);

    // Extract hours, minutes, and seconds from both dates
    const hours1 = date1.getHours();
    const minutes1 = date1.getMinutes();
    const seconds1 = date1.getSeconds();

    const hours2 = date2.getHours();
    const minutes2 = date2.getMinutes();
    const seconds2 = date2.getSeconds();

    // Calculate the difference in each component
    let hoursDifference = hours2 - hours1;
    let minutesDifference = minutes2 - minutes1;
    let secondsDifference = seconds2 - seconds1;

    // Handle negative seconds and adjust minutes
    if (secondsDifference < 0) {
        secondsDifference += 60;
        minutesDifference -= 1;
    }

    // Handle negative minutes and adjust hours
    if (minutesDifference < 0) {
        minutesDifference += 60;
        hoursDifference -= 1;
    }

    // Format the output
    let result = '';
    if (hoursDifference > 0) result += `${hoursDifference} hr${hoursDifference > 1 ? 's' : ''} `;
    if (minutesDifference > 0 || hoursDifference > 0) result += `${minutesDifference} min${minutesDifference > 1 ? 's' : ''} `;
    result += `${secondsDifference} sec${secondsDifference !== 1 ? 's' : ''}`;

    return result.trim();
}

}
