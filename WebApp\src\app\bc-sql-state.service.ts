import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

interface BcSqlState {
  scrollPosition: number;
  highlightedGuid: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class BcSqlStateService {
  private state = new BehaviorSubject<BcSqlState>({
    scrollPosition: 0,
    highlightedGuid: null
  });

  currentState$ = this.state.asObservable();

  setState(scrollPosition: number, highlightedGuid: string | null) {
    this.state.next({ scrollPosition, highlightedGuid });
  }
}
