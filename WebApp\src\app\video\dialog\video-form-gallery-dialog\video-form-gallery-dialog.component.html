<h2 mat-dialog-title class="dialog-title">Add/Edit Video</h2>
<form #videoForm="ngForm" (ngSubmit)="onSubmit(videoForm)" class="video-form">
  <div class="form-field">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Item No</mat-label>
      <input matInput placeholder="Enter Item No" name="itemNo" [(ngModel)]="video.itemNo" required />
    </mat-form-field>
  </div>

  <div class="form-field">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Video Link</mat-label>
      <input matInput placeholder="Enter Video Link" name="videoLink" [(ngModel)]="video.videoLink" required />
    </mat-form-field>
  </div>

  <div class="form-field">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Title</mat-label>
      <input matInput placeholder="Enter Title" name="title" [(ngModel)]="video.title" required />
    </mat-form-field>
  </div>

  <!-- <div class="form-field">
    <mat-checkbox [(ngModel)]="video.isThumbnail" name="isThumbnail">
      Is Thumbnail
    </mat-checkbox>
  </div> -->

  <!-- <div class="form-field">
    <mat-checkbox [(ngModel)]="video.isOnVideoTab" name="isOnVideoTab">
      Is On Video Tab
    </mat-checkbox>
  </div> -->

  <div mat-dialog-actions class="dialog-actions">
    <button mat-flat-button color="primary" type="submit" [disabled]="!videoForm.valid">
      Save
    </button>
    <button mat-button type="button" (click)="onClose()">Cancel</button>
  </div>
</form>
