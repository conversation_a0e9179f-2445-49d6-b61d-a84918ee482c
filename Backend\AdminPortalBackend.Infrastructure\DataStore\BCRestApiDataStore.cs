﻿using AdminPortalBackend.Core.Contracts;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.SignalRHub;
using Azure.Core;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.VisualBasic;
using System.Net.Http.Headers;
using System.Text.Json;

namespace AdminPortalBackend.Infrastructure.DataStore;

public class BCRestApiDataStore(HttpClient httpClient, ODataService oDataService, string endpointUrl,
    string companyId, string token, ILogger<DataStoreFactory> _logger,
    IHubContext<MessageHub, IMessageClient> messageHub, IntegrationInfo integrationInfo) : IDataStore
{
    private Dictionary<string, object> storeConfig = [];
    private int totalCount = 0;

    public async Task<List<Dictionary<string, object>>> RetrieveDataAsync(string entity)
    {
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var allData = new List<Dictionary<string, object>>();
        var pageSize = 500;
        var skip = 0;
        bool hasMoreData = true;

        var message = $"{integrationInfo.IntegrationId}~({entity}) Retriving Connection Data of entity {entity}";
        await messageHub.Clients.All.SendMessage(message);
        _logger.LogInformation(message);
        try
        {
            string nextLink = storeConfig.GetValueOrDefault("NextLink") as string;
            string resourceUrl = string.Empty;
            if (!string.IsNullOrEmpty(nextLink))
            {
                resourceUrl = nextLink;
            }
            else
            {
                resourceUrl = $"{endpointUrl}companies({companyId})/{entity}";

                storeConfig.TryGetValue("Filters", out var filters);
                if (filters != null && filters is List<FilterCondition> filterList)
                {
                    var filterQuery = BuildODataFilter(filterList);
                    if (!string.IsNullOrEmpty(filterQuery))
                    {
                        resourceUrl += "&$filter=" + filterQuery;
                    }
                }
            }

            while (!String.IsNullOrEmpty(resourceUrl))
            {
                // Make the request to the API
                var response = await httpClient.GetAsync(resourceUrl);
                response.EnsureSuccessStatusCode(); // Throw if not a success code

                var responseData = await response.Content.ReadAsStringAsync();
                var apiData = JsonSerializer.Deserialize<ODataApiResponse>(responseData);

                resourceUrl = apiData.NextLink;
                this.SetConfig("NextLink", apiData.NextLink);

                // Transform the data to match the desired output format
                var transformedData = apiData.value.Select(row =>
                    row.ToDictionary(
                        kvp => char.ToUpper(kvp.Key[0]) + kvp.Key.Substring(1),
                        kvp => kvp.Value
                    )
                ).ToList();

                // Add the transformed data to the result list
                allData.AddRange(transformedData);

                message = $"{integrationInfo.IntegrationId}~({entity}) Retrieved " + transformedData.Count + " data. Total " + (totalCount + allData.Count);
                await messageHub.Clients.All.SendMessage(message);
                _logger.LogInformation(message);

                if (allData.Count >= 50000) { break; }
            }
            totalCount += allData.Count;
            if (string.IsNullOrEmpty(resourceUrl))
            {
                message = $"{integrationInfo.IntegrationId}~({entity}) Retrieved Total " + totalCount + " Data from Entity " + entity;
                await messageHub.Clients.All.SendMessage(message);
            }
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({entity}) Error: Retrieving Connection Data";
            await messageHub.Clients.All.SendMessage(message);
            _logger.LogError(ex, message + ex.Message);
        }
        return allData;
    }

    public async Task<List<Dictionary<string, object>>> SaveDataAsync(List<Dictionary<string, object>> data, string destinationUrl, string sourceTable, string primaryKeys)
    {
        var updatedProperties = data.FirstOrDefault();
        string insertedTempId = updatedProperties["id"]?.ToString();
        updatedProperties.Remove("lastModifiedDateTime");
        var postUrl = $"{endpointUrl}companies({companyId})/{destinationUrl}";
        var createdData = await oDataService.PostDataAsync(postUrl, updatedProperties, token);

        return [createdData];
    }

    public void SetConfig(string key, object value)
    {
        storeConfig[key] = value;
    }

    public async Task<Dictionary<string, object>> UpdateDataAsync(Dictionary<string, object> updatedProperties, string destinationUrl, Dictionary<string, object> primaryKeyValues)
    {
        string patchSystemId = primaryKeyValues["id"]?.ToString();
        var patchUrl = $"{endpointUrl}companies({companyId})/{destinationUrl}({patchSystemId})";
        var updatedRecord = await oDataService.PatchDataAsync(patchUrl, updatedProperties, token);

        return updatedRecord;
    }

    private string BuildODataFilter(List<FilterCondition> filter)
    {
        // Convert the filter conditions into an OData query string (e.g., `?$filter=name eq 'John Doe' and status eq 'Active'`)
        var filterStrings = filter.Select(f => $"{f.Field} {f.Operator} '{f.Value}'");
        return string.Join(" and ", filterStrings);
    }

    private string BuildFilters(Dictionary<string, string> filters)
    {
        var filterQuery = string.Join(
                    "&",
                    filters.Select(f =>
                    {
                        // Try to parse the value to a DateTime
                        DateTime dateValue;
                        if (DateTime.TryParse(f.Value, out dateValue))
                        {
                            // Format the date value for OData (ISO 8601)
                            var formattedDate = dateValue.ToString("yyyy-MM-ddTHH:mm:ssZ");
                            return $"{Uri.EscapeDataString(f.Key)} gt {Uri.EscapeDataString(formattedDate)}"; // You can adjust the operator here as needed
                        }
                        else
                        {
                            // If it's not a date, treat it as a string and apply the 'eq' operator
                            return $"{Uri.EscapeDataString(f.Key)} eq {Uri.EscapeDataString(f.Value)}";
                        }
                    })
                );

        return filterQuery;
    }

    public object GetConfig(string key)
    {
        return storeConfig.GetValueOrDefault(key);
    }
}
