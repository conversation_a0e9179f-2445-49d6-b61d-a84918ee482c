﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AdminPortalBackend.Core.Features
{
    public class CreateConnectionIntegrationDto
    {
        public Guid SourceConnectionGuid { get; set; }
        public string SourceConnectionName { get; set; }
        public string SourceDatabase { get; set; }
        public List<string> SourceTable { get; set; }
        public Guid DestinationConnectionGuid { get; set; }
        public string DestinationConnectionName { get; set; }
        public string DestinationDatabase { get; set; }
        public List<string> DestinationTable { get; set; }
        public List<string> SourcePrimaryKey { get; set; }
        public List<string> DestinationPrimaryKey { get; set; }
        public string JobFrequency { get; set; }
        public List<string> MappedColumns { get; set; } // JSON string
        public string? Settings { get; set; } // Json string
    }
}
