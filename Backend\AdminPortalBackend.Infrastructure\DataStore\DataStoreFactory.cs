﻿using AdminPortalBackend.Core.Contracts;
using AdminPortalBackend.Core.Entities;
using AdminPortalBackend.Core.Features;
using AdminPortalBackend.Core.Models;
using AdminPortalBackend.Core.Repositiories;
using AdminPortalBackend.Infrastructure.OData;
using AdminPortalBackend.Infrastructure.SignalRHub;
using Dapper;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.VisualBasic;
using MongoDB.Driver;
using System.Data.SqlClient;
using System.Text.Json;
using static AdminPortalBackend.Infrastructure.DataStore.SyncManager;
using static Hangfire.Storage.JobStorageFeatures;

namespace AdminPortalBackend.Infrastructure.DataStore;

public class DataStoreFactory(IHttpClientFactory httpClientFactory,
                              IDbConnectionRepository _dbConnectionRepo,
                              ODataService oDataService,
                              IConfiguration configuration, ILogger<DataStoreFactory> logger, IHubContext<MessageHub, IMessageClient> messageHub)
{
    private IDataStore _dataStore;
    private string _connectionType;
    private IntegrationInfo _integrationInfo;
    public async Task<DataStoreFactory> CreateDataStoreBuilder(IntegrationInfo integrationInfo)
    {
        var tableName = string.IsNullOrEmpty(integrationInfo.Table) ? [" "] : integrationInfo.Table.Split('_');
        string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];

        var message = $"{integrationInfo.IntegrationId}~({tablePart}) Getting Datasource for {integrationInfo.Database}";
        await messageHub.Clients.All.SendMessage(message);
        logger.LogInformation(message);

        try
        {
            var dbConnection = _dbConnectionRepo.GetConnection(integrationInfo.ConnectionGuid).Result;
            _connectionType = dbConnection.Type;
            _integrationInfo = integrationInfo;
            switch (dbConnection.Type)
            {
                case "BCODataRestApiService":
                    var token = oDataService.GetAccessTokenAsync(integrationInfo.ConnectionGuid).Result;
                    var odataApiConnectionDetails = _dbConnectionRepo.GetODataConnectionDetailAsync(integrationInfo.ConnectionGuid).Result;

                    //Dictionary<string, string> filters = new Dictionary<string, string>();
                    //if (!String.IsNullOrEmpty(integrationInfo.Settings))
                    //{
                    //    var setting = JsonSerializer.Deserialize<IntegrationSetting>(integrationInfo.Settings);

                    //    if (setting != null && setting.BcToSql == "Differential Load")
                    //    {
                    //        filters = _dbConnectionRepo.GetLastModifiedFilter(integrationInfo.IntegrationId.ToString());
                    //    }
                    //}

                    _dataStore = new BCRestApiDataStore(httpClientFactory.CreateClient(), oDataService, odataApiConnectionDetails.ConnectionCreds.EndpointUrl, GetCompanyId(integrationInfo), token, logger, messageHub, integrationInfo);

                    this.SetPropertiesMetadata();

                    break;

                case "BCODataWebService":
                    var accessToken = oDataService.GetAccessTokenAsync(integrationInfo.ConnectionGuid).Result;
                    var odataServiceConnectionDetails = _dbConnectionRepo.GetODataConnectionDetailAsync(integrationInfo.ConnectionGuid).Result;

                    //Dictionary<string, string> timestampFilters = new Dictionary<string, string>();
                    //if (!String.IsNullOrEmpty(integrationInfo.Settings))
                    //{
                    //    var setting = JsonSerializer.Deserialize<IntegrationSetting>(integrationInfo.Settings);

                    //    if (setting != null && setting.BcToSql == "Differential Load")
                    //    {
                    //        var lastTimestamp = _dbConnectionRepo.GetLastModifiedTimestamp(integrationInfo.IntegrationId.ToString());
                    //        timestampFilters = new Dictionary<string, string>() { { "SystemRowVersion", lastTimestamp.ToString() } };
                    //    }
                    //}

                    _dataStore = new BCWebServiceDataStore(httpClientFactory.CreateClient(), oDataService, odataServiceConnectionDetails.ConnectionCreds.EndpointUrl, GetCompanyId(integrationInfo), accessToken, logger, messageHub, integrationInfo);

                    this.SetPropertiesMetadata();

                    break;

                case "Ms SQL Server":
                    var connectionCreds = JsonSerializer.Deserialize<MssqlServerCredDto>(dbConnection.ConnectionCredJson);
                    var connectionString = $"Server={connectionCreds.ServerName};" +
                            (integrationInfo.Database != null ? $"Database={integrationInfo.Database};" : "") +
                            $"User Id={connectionCreds.UserId};" +
                            $"Password={connectionCreds.Password};" +
                            $"TrustServerCertificate={(connectionCreds.TrustedCertificate ? "yes" : "no")};";

                    var deleteStagingTableAfterLoad = configuration.GetValue<string>("DataProcessing:DeleteStagingTableAfterLoad");
                    _dataStore = new SqlServerDataStore(connectionString, logger, messageHub, deleteStagingTableAfterLoad, integrationInfo);
                    break;

                case "Excel":
                    _dataStore = new ExcelDataStore();
                    break;

                default:
                    throw new ApplicationException("DataSource not supported.");
            }

            return this;
        }
        catch (Exception ex)
        {
            message = $"{integrationInfo.IntegrationId}~({tablePart}) Error: Getting Datasource {integrationInfo.Database} Failed!";
            await messageHub.Clients.All.SendMessage(message);
            logger.LogError(ex, message);
            throw new Exception(ex.Message);
        }
    }

    public DataStoreFactory SetDifferentialLoadFilterValue()
    {
        var tableName = string.IsNullOrEmpty(_integrationInfo.Table) ? [" "] : _integrationInfo.Table.Split('_');
        string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];

        var message = $"{_integrationInfo.IntegrationId}~({tablePart}) Setting Differential Load Filters";
        messageHub.Clients.All.SendMessage(message).Wait();
        logger.LogInformation(message);

        try
        {
            if (!String.IsNullOrEmpty(_integrationInfo.Settings))
            {
                var setting = JsonSerializer.Deserialize<IntegrationSetting>(_integrationInfo.Settings);

                if (setting != null && setting.BcToSql == "Differential Load")
                {
                    var lastTimestamp = _dbConnectionRepo.GetLastModifiedTimestamp(_integrationInfo.IntegrationId.ToString());
                    this._dataStore.SetConfig("Filters", new List<FilterCondition> { new FilterCondition { Field = "SystemRowVersion", Operator = "gt", Value = lastTimestamp.ToString() } });
                }
            }

        }
        catch (Exception ex)
        {
            message = $"{_integrationInfo.IntegrationId}~({tablePart}) Error: Setting Differential Load Filters failed";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogError(ex, message);
            throw;
        }

        return this;
    }

    public DataStoreFactory SetChangeLogEntryFilterValue(long entryNo)
    {
        var tableName = string.IsNullOrEmpty(_integrationInfo.Table) ? [" "] : _integrationInfo.Table.Split('_');
        string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];
        logger.LogInformation("Setting Change Log Entry Filters {IntegrationId}", this._integrationInfo.IntegrationId);
        try
        {
            var rowVersionFilter = new FilterCondition { Field = "Entry_No", Operator = "gt", Value = entryNo.ToString() };
            //var fieldCaptionFilter = new FilterCondition { Field = "Field_Caption", Operator = "eq", Value = "SystemRowVersion" };
            //var dateFilter = new FilterCondition { Field = "Date_and_Time", Operator = "gt", Value = DateTime.Now.AddDays(-7).ToString("yyyy-MM-ddTHH:mm:ssZ") };
            this._dataStore.SetConfig("Filters", new List<FilterCondition> { rowVersionFilter });

        }
        catch (Exception ex)
        {
            var message = $"{_integrationInfo?.IntegrationId}~({tablePart}) Error: Getting Datasource {_integrationInfo.Database} Failed!";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogError(ex, message);
            throw new Exception(ex.Message);
        }

        return this;
    }

    public DataStoreFactory SetDefaultIntegrationDataLoadType()
    {
        var tableName = string.IsNullOrEmpty(_integrationInfo.Table) ? [" "] : _integrationInfo.Table.Split('_');
        string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];

        var message = $"{_integrationInfo.IntegrationId}~({tablePart}) Setting DataLoadType";
        messageHub.Clients.All.SendMessage(message).Wait();
        logger.LogInformation(message);

        try
        {
            if (!String.IsNullOrEmpty(_integrationInfo.Settings))
            {
                var setting = JsonSerializer.Deserialize<IntegrationSetting>(_integrationInfo.Settings);

                if (setting != null)
                {
                    this._dataStore.SetConfig("DataLoadType", setting.BcToSql);
                }
            }
            else
            {
                this._dataStore.SetConfig("DataLoadType", "Clean Load");
            }

            message = $"{_integrationInfo.IntegrationId}~({tablePart}) Successfully set DataLoadType";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogInformation(message);
        }
        catch (Exception ex)
        {
            message = $"{_integrationInfo.IntegrationId}~({tablePart}) Error: DataLoadType Failed";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogError(ex, message);
            throw;
        }
        return this;
    }

    public DataStoreFactory SetExplicitDataLoadType(string dataLoadType)
    {
        var tableName = string.IsNullOrEmpty(_integrationInfo.Table) ? [" "] : _integrationInfo.Table.Split('_');
        string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];

        var message = $"{_integrationInfo.IntegrationId}~({tablePart}) Setting ExplicitDataLoadType";
        messageHub.Clients.All.SendMessage(message).Wait();
        logger.LogInformation(message);

        try
        {
            if (!String.IsNullOrEmpty(dataLoadType))
            {
                this._dataStore.SetConfig("DataLoadType", dataLoadType);
            }
        }
        catch (Exception ex)
        {
            message = $"{_integrationInfo.IntegrationId}~({tablePart}) Error: ExplicitDataLoadType Failed";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogError(ex, message);
            throw;
        }

        return this;
    }

    public DataStoreFactory SetBatchNumber(int batchNumber)
    {
        logger.LogInformation("Setting SetBatchNumber {IntegrationId}", this._integrationInfo.IntegrationId);
        return this;
    }

    public DataStoreFactory SetPropertiesMetadata()
    {
        var tableName = string.IsNullOrEmpty(_integrationInfo.Table) ? [" "] : _integrationInfo.Table.Split('_');
        string tablePart = tableName.Length == 2 ? tableName[1] : tableName[0];

        var message = $"{_integrationInfo.IntegrationId}~({tablePart}) Setting Properties Metadata";
        messageHub.Clients.All.SendMessage(message).Wait();
        logger.LogInformation(message);

        try
        {
            using var connection = new SqlConnection(configuration.GetConnectionString("AdminConnection"));
            connection.Open();
            var metadataQuery = "SELECT [PropertyName], [PropertyType] FROM BCEntityMetadata WHERE EntityName = @TableName AND ConnectionId = @ConnectionId";
            var properties = connection.QueryAsync<BCEntityMetadata>(metadataQuery, new { TableName = _integrationInfo.Table, ConnectionId = _integrationInfo.ConnectionGuid }).Result;

            if (properties.Any())
            {
                this._dataStore.SetConfig("PropertiesMetadata", properties.ToList());
            }

            message = $"{_integrationInfo.IntegrationId}~({tablePart}) Successfully set Properties Metadata";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogInformation(message);
        }
        catch (Exception ex)
        {
            message = $"{_integrationInfo.IntegrationId}~({tablePart}) Error: SetPropertiesMetadata Failed";
            messageHub.Clients.All.SendMessage(message).Wait();
            logger.LogError(ex, message);
            throw;
        }

        return this;
    }

    public IDataStore Build() => _dataStore;

    private string GetCompanyId(IntegrationInfo integration)
    {
        using var connection = new SqlConnection(configuration.GetConnectionString("AdminConnection"));
        connection.Open();
        var companySql = $"Select [Id] from BCCompanies Where ConnectionId='{integration.ConnectionGuid}' AND [Name]='{integration.Database}'";
        var companyId = connection.QuerySingleOrDefault<string>(companySql);
        return companyId;
    }
}
