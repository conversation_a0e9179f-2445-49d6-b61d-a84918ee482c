// chat.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private chatMessageSubject = new Subject<string>(); // For sending messages
  private chatToggleSubject = new BehaviorSubject<boolean>(true); // For toggling chat (default to closed)

  // Observable for the chat message
  chatMessage$ = this.chatMessageSubject.asObservable();

  // Observable for chat toggle state
  chatToggle$ = this.chatToggleSubject.asObservable();

  // Method to send a message
  sendMessage(message: string) {
    this.chatMessageSubject.next(message);
  }

  // Method to toggle the chat box
  toggleChat() {
    const currentValue = this.chatToggleSubject.value;
    this.chatToggleSubject.next(!currentValue); // Toggle the state
  }

  // Method to open the chat directly
  openChat() {
    this.chatToggleSubject.next(true); // Force open
  }
}
