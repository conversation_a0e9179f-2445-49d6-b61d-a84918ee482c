import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddConnectionIntegrationComponent } from './add-connection-integration.component';

describe('AddConnectionIntegrationComponent', () => {
  let component: AddConnectionIntegrationComponent;
  let fixture: ComponentFixture<AddConnectionIntegrationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddConnectionIntegrationComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(AddConnectionIntegrationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
