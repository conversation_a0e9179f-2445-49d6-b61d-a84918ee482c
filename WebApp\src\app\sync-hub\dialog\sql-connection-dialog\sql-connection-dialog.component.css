.dialog-title {
  font-size: 24px;
  /* font-weight: 600; */
  color: #3f51b5;
  margin: 10px 0 5px;
  text-align: center;
  /* text-transform: uppercase; */
}

.sql-server-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
.disabled-button {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.6;
}
.full-width {
  width: 100%;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

button[mat-button] {
  min-width: 100px;
  margin-left: 8px;
}

button[mat-flat-button] {
  background-color: #3f51b5;
  color: #fff;
  border-radius: 4px;
  transition: background-color 0.3s;
}

button[mat-flat-button]:hover {
  background-color: #303f9f;
}

button[mat-button] {
  transition: background-color 0.3s;
}

button[mat-button]:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

input, mat-form-field {
  border-radius: 4px;
}

@media (max-width: 600px) {
  .sql-server-form {
    padding: 10px;
  }
}
